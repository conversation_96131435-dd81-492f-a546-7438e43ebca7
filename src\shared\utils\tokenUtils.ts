// Token management utilities for password reset flow

const RESET_TOKEN_KEY = "reset_access_token";

/**
 * Store the reset access token in sessionStorage
 * @param token - The access token from verify-reset-otp response
 */
export const storeResetToken = (token: string): void => {
  sessionStorage.setItem(RESET_TOKEN_KEY, token);
};

/**
 * Retrieve the reset access token from sessionStorage
 * @returns The stored reset token or null if not found
 */
export const getResetToken = (): string | null => {
  return sessionStorage.getItem(RESET_TOKEN_KEY);
};

/**
 * Clear the reset access token from sessionStorage
 */
export const clearResetToken = (): void => {
  sessionStorage.removeItem(RESET_TOKEN_KEY);
};

/**
 * Check if a reset token exists in sessionStorage
 * @returns True if token exists, false otherwise
 */
export const hasResetToken = (): boolean => {
  return !!sessionStorage.getItem(RESET_TOKEN_KEY);
};

/**
 * Check if we are currently in a password reset flow
 * @returns True if reset token exists and is valid format
 */
export const isInResetFlow = (): boolean => {
  const token = getResetToken();
  return !!token && token.length > 0;
};
