import type { Dropoff } from "@/features/booking/api/bus-schedules/bus-schedules.types";

export interface Bus {
  id: string;
  originalScheduleId?: string; // Original schedule ID without prefix for API calls
  name: string;
  image?: string;
  departureTime: string;
  arrivalTime: string;
  duration: string;
  departureLocation: string;
  arrivalLocation: string;
  price: number;
  availableSeats: number;
  remainingTickets: number;
  amenities: string[];
  type: BusType;
  isHighlighted?: boolean;
  busNumber: string;
  busDescription: string;
  departureDateTime: Date | string;
  arrivalDateTime: Date | string;
  rawDepartureTime: string;
  rawArrivalTime: string;
  dropoffId?: string;
  dropoffs?: Dropoff[];
  fleetPolicy?: string;
  bus: {
    bus_type: string;
  };
}

export interface BusFilters {
  sortBy: SortOption;
  timeSlots: TimeSlot[];
  busTypes: BusType[];
}

export interface RouteInfo {
  from: string;
  to: string;
  date: string;
  totalBuses: number;
}

export type SortOption =
  | "default"
  | "earliest"
  | "latest"
  | "cheapest"
  | "most_expensive";

export type TimeSlot =
  | "early_morning"
  | "morning"
  | "afternoon"
  | "evening"
  | "night";

export type BusType = "standard" | "limousine" | "sleeper";

export interface RadioOption {
  value: string;
  label: string;
  count?: number;
}

export interface SearchParams {
  fromPlaceId?: number;
  toPlaceId?: number;
  fromPlaceName?: string;
  toPlaceName?: string;
  departureDate?: string;
  returnDate?: string;
  numTickets?: number;
  isRoundTrip?: boolean;
}
