import { useState, useMemo } from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";

export interface ValidationRule {
  required?: boolean;
  message?: string;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string | null;
}

export interface FormValues {
  fromLocation: string;
  toLocation: string;
  departureDate: string;
  returnDate: string;
  numTickets: number;
  isRoundTrip: boolean;
}

export const useFormValidation = () => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validationRules: ValidationRules = {
    fromLocation: {
      required: true,
      message: t("form.validation.selectDeparture"),
    },
    toLocation: {
      required: true,
      message: t("form.validation.selectDestination"),
    },
    departureDate: {
      required: true,
      message: t("form.validation.selectDepartureDate"),
    },
    returnDate: {
      required: false, // Only required if round trip
      message: t("form.validation.selectReturnDate"),
    },
    numTickets: {
      required: true,
      message: t("form.validation.enterTickets"),
    },
  };

  const validateField = (
    name: string,
    value: any,
    formValues: FormValues
  ): string | null => {
    const rule = validationRules[name];
    if (!rule) return null;

    // Special validation for numTickets - handle before general required check
    if (name === "numTickets") {
      if (typeof value === "number" && value <= 0) {
        return t("form.validation.ticketsGreaterThanZero");
      }
      if (value === null || value === undefined || value === "") {
        return t("form.validation.enterTicketNumber");
      }
    }

    // Check if field is required
    if (rule.required) {
      if (!value || (typeof value === "string" && value.trim() === "")) {
        return rule.message || `${name} is required`;
      }
    }

    // Date validation for departure date
    if (name === "departureDate" && value) {
      const selectedDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day

      if (selectedDate < today) {
        return t("form.validation.departureDatePast");
      }
    }

    // Special validation for return date
    if (name === "returnDate" && formValues.isRoundTrip) {
      if (!value || (typeof value === "string" && value.trim() === "")) {
        return rule.message || t("form.validation.selectReturnDate");
      }

      // Check if return date is after departure date
      if (value && formValues.departureDate) {
        const returnDate = new Date(value);
        const departureDate = new Date(formValues.departureDate);

        if (returnDate <= departureDate) {
          return t("form.validation.returnDateAfterDeparture");
        }
      }
    }

    return null;
  };

  const validateForm = (values: FormValues): ValidationErrors => {
    const newErrors: ValidationErrors = {};

    Object.keys(validationRules).forEach((fieldName) => {
      const error = validateField(
        fieldName,
        values[fieldName as keyof FormValues],
        values
      );
      newErrors[fieldName] = error;
    });

    return newErrors;
  };

  const validateSingleField = (
    name: string,
    value: any,
    formValues: FormValues
  ) => {
    const error = validateField(name, value, formValues);
    setErrors((prev) => ({
      ...prev,
      [name]: error,
    }));
    return error;
  };

  const setFieldTouched = (name: string, isTouched: boolean = true) => {
    setTouched((prev) => ({
      ...prev,
      [name]: isTouched,
    }));
  };

  const isValid = useMemo(() => {
    return Object.values(errors).every((error) => error === null);
  }, [errors]);

  const hasErrors = useMemo(() => {
    return Object.values(errors).some((error) => error !== null);
  }, [errors]);

  return {
    errors,
    touched,
    setErrors,
    validateForm,
    validateSingleField,
    setFieldTouched,
    isValid,
    hasErrors,
  };
};
