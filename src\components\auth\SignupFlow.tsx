import React, { useState } from "react";
import { SignupForm } from "./SignupForm";
import { OtpVerification } from "./OtpVerification";
import { PasswordSetup } from "./PasswordSetup";
import { SignupSuccess } from "./SignupSuccess";
import {
  useSignup,
  useVerifyOtp,
  useSetPassword,
} from "@/shared/hooks/useAuth";
import { getTranslatedErrorMessage } from "@/shared/utils/errorMessageTranslator";

interface SignupFlowProps {
  onSignupSuccess?: () => void;
  onBack: () => void;
  className?: string;
}

type SignupStep = "form" | "otp" | "password" | "success";

export const SignupFlow: React.FC<SignupFlowProps> = ({
  onSignupSuccess,
  onBack,
  className,
}) => {
  const [currentStep, setCurrentStep] = useState<SignupStep>("form");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otpError, setOtpError] = useState("");
  const [isResendingOtp, setIsResendingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);

  const { mutateAsync: signup } = useSignup();
  const { mutateAsync: verifyOtp } = useVerifyOtp();
  const { mutateAsync: setPassword, isPending: isPasswordLoading } =
    useSetPassword();

  const handleOtpSent = (normalizedPhone: string) => {
    setPhoneNumber(normalizedPhone);
    setCurrentStep("otp");
  };

  const handleOtpVerified = async (otp: string) => {
    try {
      setIsVerifyingOtp(true);
      setOtpError("");
      await verifyOtp({
        phone_number: phoneNumber,
        otp_code: otp,
      });
      setCurrentStep("password");
    } catch (error: any) {
      console.error("OTP verification failed:", error);
      const errorMessage = getTranslatedErrorMessage(error);
      setOtpError(errorMessage);
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  const handlePasswordSet = async (password: string) => {
    try {
      await setPassword({
        password: password,
      });
      setCurrentStep("success");
    } catch (error: any) {
      console.error("Password setup failed:", error);
      // Handle error - maybe show error in PasswordSetup component
    }
  };

  const handleResendOtp = async () => {
    try {
      setIsResendingOtp(true);
      setOtpError("");
      const response = await signup({ phone_number: phoneNumber });

      // Check for errors in response data even if HTTP status is success
      const responseData = response as any;
      if (
        responseData?.error_type === "RATE_LIMITED" ||
        responseData?.success === false
      ) {
        const errorMessage = getTranslatedErrorMessage({
          response: { data: responseData },
        });
        setOtpError(errorMessage);
        return;
      }
    } catch (error: any) {
      console.error("Resend OTP failed:", error);
      setOtpError(getTranslatedErrorMessage(error));
    } finally {
      setIsResendingOtp(false);
    }
  };

  const handleBack = () => {
    if (currentStep === "form") {
      onBack();
    } else if (currentStep === "otp") {
      setCurrentStep("form");
    } else if (currentStep === "password") {
      setCurrentStep("otp");
    }
  };

  const handleContinueAfterSuccess = () => {
    onSignupSuccess?.();
  };

  // Render based on current step
  switch (currentStep) {
    case "form":
      return <SignupForm onOtpSent={handleOtpSent} className={className} />;

    case "otp":
      return (
        <OtpVerification
          onVerifySuccess={handleOtpVerified}
          onBack={handleBack}
          onResendOtp={handleResendOtp}
          isLoading={isVerifyingOtp}
          isResendLoading={isResendingOtp}
          error={otpError}
          onError={setOtpError}
          className={className}
        />
      );

    case "password":
      return (
        <PasswordSetup
          onPasswordSet={handlePasswordSet}
          onBack={handleBack}
          isLoading={isPasswordLoading}
          className={className}
        />
      );

    case "success":
      return (
        <SignupSuccess
          onContinue={handleContinueAfterSuccess}
          className={className}
        />
      );

    default:
      return null;
  }
};
