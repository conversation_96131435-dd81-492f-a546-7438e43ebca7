import { useState, useMemo } from "react";
import { usePlaces } from "./usePlaces";

interface Place {
  id: number;
  name: string;
  code: string;
}

export const useHomePageLogic = () => {
  const [fromLocation, setFromLocation] = useState<string>("");
  const [toLocation, setToLocation] = useState<string>("");
  const [isRoundTrip, setIsRoundTrip] = useState(false);

  const { data: places = [], isLoading, error } = usePlaces();

  // Filter places based on input values with memoization
  const filteredFromPlaces = useMemo(
    () =>
      places.filter((place: Place) =>
        place.name.toLowerCase().includes(fromLocation.toLowerCase())
      ),
    [places, fromLocation]
  );

  const filteredToPlaces = useMemo(
    () =>
      places.filter((place: Place) =>
        place.name.toLowerCase().includes(toLocation.toLowerCase())
      ),
    [places, toLocation]
  );

  return {
    // State
    fromLocation,
    toLocation,
    isRoundTrip,

    // State setters
    setFromLocation,
    setToLocation,
    setIsRoundTrip,

    // Data
    places,
    filteredFromPlaces,
    filteredToPlaces,

    // Loading states
    isLoading,
    error,
  };
};
