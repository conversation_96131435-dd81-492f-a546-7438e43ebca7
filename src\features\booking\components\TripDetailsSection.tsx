import { memo, useCallback } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import { useContactFormStore } from "@/stores/contactFormStore";
import { TripCard } from "@/shared/components";
import { Route as SearchRoute } from "@/routes/booking/search";

interface TripDetailsSectionProps {
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  acceptTerms?: boolean;
  searchParams?: {
    fromPlaceId?: number;
    toPlaceId?: number;
    fromPlaceName?: string;
    toPlaceName?: string;
    departureDate?: string;
    returnDate?: string;
    numTickets?: number;
    isRoundTrip?: boolean;
  };
}

const TripDetailsSection = memo(
  ({
    customerName = "",
    customerPhone = "",
    customerEmail = "",
    acceptTerms = false,
    searchParams,
  }: TripDetailsSectionProps) => {
    const navigate = useNavigate();
    const { outboundTrip, returnTrip, setOutboundTrip, setReturnTrip } =
      useSelectedTripsStore();
    const { setFormData } = useContactFormStore();

    const handleChangeOutboundTrip = useCallback(() => {
      // Lưu thông tin form hiện tại
      setFormData({
        customerName,
        customerPhone,
        customerEmail,
        acceptTerms,
      });

      // Xóa chuyến đi hiện tại
      setOutboundTrip(null);

      // Navigate về trang search với thông tin tìm kiếm
      if (outboundTrip) {
        navigate({
          to: SearchRoute.to,
          search: {
            fromPlaceId: searchParams?.fromPlaceId,
            toPlaceId: searchParams?.toPlaceId,
            fromPlaceName:
              searchParams?.fromPlaceName || outboundTrip.departureLocation,
            toPlaceName:
              searchParams?.toPlaceName || outboundTrip.arrivalLocation,
            departureDate:
              searchParams?.departureDate || outboundTrip.departureDate,
            returnDate: searchParams?.returnDate || returnTrip?.departureDate,
            numTickets: searchParams?.numTickets || 1,
            isRoundTrip: searchParams?.isRoundTrip || !!returnTrip,
            focusDate: "departure" as const,
          },
        });
      }
    }, [
      outboundTrip,
      returnTrip,
      setOutboundTrip,
      setFormData,
      navigate,
      customerName,
      customerPhone,
      customerEmail,
      acceptTerms,
      searchParams,
    ]);

    const handleChangeReturnTrip = useCallback(() => {
      // Lưu thông tin form hiện tại
      setFormData({
        customerName,
        customerPhone,
        customerEmail,
        acceptTerms,
      });

      // Xóa chuyến về hiện tại
      setReturnTrip(null);

      // Navigate về trang search với thông tin tìm kiếm
      if (returnTrip) {
        navigate({
          to: SearchRoute.to,
          search: {
            fromPlaceId: searchParams?.fromPlaceId,
            toPlaceId: searchParams?.toPlaceId,
            fromPlaceName:
              searchParams?.fromPlaceName || returnTrip.departureLocation,
            toPlaceName:
              searchParams?.toPlaceName || returnTrip.arrivalLocation,
            departureDate:
              searchParams?.departureDate || outboundTrip?.departureDate,
            returnDate: searchParams?.returnDate || returnTrip.departureDate,
            numTickets: searchParams?.numTickets || 1,
            isRoundTrip: true,
            focusDate: "return" as const,
          },
        });
      }
    }, [
      outboundTrip,
      returnTrip,
      setReturnTrip,
      setFormData,
      navigate,
      customerName,
      customerPhone,
      customerEmail,
      acceptTerms,
      searchParams,
    ]);

    return (
      <div className="bg-white border border-[#EDEDED] rounded-xl p-3 lg:p-6">
        <h3 className="text-lg font-bold text-[#181818] mb-4">
          Chi tiết chuyến đi
        </h3>

        <div className="space-y-4">
          {/* Chiều đi */}
          {outboundTrip && (
            <TripCard
              trip={outboundTrip}
              tripType="outbound"
              onChangeTrip={handleChangeOutboundTrip}
              showChangeButton={true}
            />
          )}

          {/* Chiều về */}
          {returnTrip && (
            <TripCard
              trip={returnTrip}
              tripType="return"
              onChangeTrip={handleChangeReturnTrip}
              showChangeButton={true}
            />
          )}
        </div>
      </div>
    );
  }
);

TripDetailsSection.displayName = "TripDetailsSection";

export default TripDetailsSection;
