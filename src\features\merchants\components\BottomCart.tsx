import React from "react";
import { useNavigate } from "@tanstack/react-router";
import { OrdersIcon, PackIcon } from "@/shared/components/icons";
import { orderStorage } from "@/shared/utils/localStorage.utils";
import { useStore } from "@tanstack/react-store";
import { simpleCartStore } from "@/stores/simpleCartStore";

interface BottomCartProps {
  qr: string;
}

export const BottomCart: React.FC<BottomCartProps> = React.memo(({ qr }) => {
  const navigate = useNavigate();
  const items = useStore(simpleCartStore, (state) => state.items);
  const displayTotalItems = Object.values(items).reduce(
    (sum, item) => sum + item.quantity,
    0
  );

  // Lấy orderIds từ localStorage
  const orderIds = orderStorage.getOrderHistory();
  const hasOrderIds = orderIds.length > 0;
  const hasCartItems = displayTotalItems > 0;

  // Nếu không có orderIds và không có items thì không hiển thị BottomCart
  if (!hasOrderIds && !hasCartItems) return null;

  const handleNavigateToOrder = () => {
    navigate({
      to: "/merchants/order-confirmation/$id",
      params: { id: "multiple" },
      search: { qr },
    });
  };

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-white/64 rounded-t-[20px] shadow-[0px_4px_20px_0px_rgba(132,132,132,0.12)] px-4 py-3 z-[9999] flex justify-end">
        <div className="flex gap-8 w-full max-w-lg mx-auto">
          {/* Đơn hàng button */}
          <button
            onClick={() =>
              navigate({ to: "/merchants/orders", search: { qr } })
            }
            className="flex-1 flex items-center justify-center gap-2 shadow-2xl bg-[#FFFFFF] text-[#2D5BFF] font-semibold rounded-lg py-3 px-2 transition-colors hover:bg-[#f0f6ff]"
            disabled={!hasOrderIds}
          >
            <OrdersIcon />
            <span className="text-base font-semibold">Đơn hàng</span>
          </button>
          {/* Giỏ hàng button */}
          <button
            onClick={handleNavigateToOrder}
            className="flex-1 flex items-center justify-center gap-2 bg-[#2D5BFF] text-white font-bold rounded-lg py-3 px-2 shadow transition-colors hover:bg-[#1e40af] disabled:bg-gray-300 disabled:cursor-not-allowed"
            disabled={!hasCartItems}
          >
            <PackIcon />
            <span className="text-base font-bold">Giỏ hàng</span>
            <span className="ml-1 text-base font-bold">
              ({displayTotalItems})
            </span>
          </button>
        </div>
      </div>
      {/* Bottom spacing for fixed cart */}
      <div className="h-20" />
    </>
  );
});
