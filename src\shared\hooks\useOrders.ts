import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createOrder, getOrderById, getAllOrders } from "@/api/orders";
import type { CreateOrderRequest, Order } from "@/api/orders";

// Create order mutation
export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (orderData: CreateOrderRequest) => createOrder(orderData),
    onSuccess: () => {
      // Invalidate orders queries to refetch
      queryClient.invalidateQueries({ queryKey: ["orders"] });
    },
    onError: (error) => {
      console.error("Failed to create order:", error);
    },
  });
};

// Get order by ID query
export const useGetOrderById = (orderId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ["orders", orderId],
    queryFn: () => getOrderById(orderId),
    enabled: enabled && !!orderId,
  });
};

// Get all orders query
export const useGetAllOrders = (params?: {
  page?: number;
  limit?: number;
  customer_phone?: string;
}) => {
  return useQuery<Order[]>({
    queryKey: ["orders", params],
    queryFn: () => getAllOrders(params),
  });
};
