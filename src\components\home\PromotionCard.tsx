import React, { memo } from "react";
import { BaseCard } from "@/shared/components";

interface PromotionCardProps {
  id: string;
  image: string;
  title: string;
  timeAgo: string;
  onClick?: () => void;
}

export const PromotionCard: React.FC<PromotionCardProps> = memo(
  ({ id, image, title, timeAgo, onClick }) => {
    return (
      <BaseCard
        id={id}
        image={image}
        title={title}
        timeAgo={timeAgo}
        onClick={onClick}
        borderColor="EDEDED"
        titleSize="14px"
        titleLines={2}
      />
    );
  }
);

PromotionCard.displayName = "PromotionCard";
