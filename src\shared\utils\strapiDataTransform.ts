import type { StrapiPopularRoute } from "@/shared/types/strapi";
import { getFirstStrapiImageUrl } from "./strapiHelpers";

// Transform data from Strapi to component format
export interface PopularRoute {
  id: string;
  title: string;
  price: string;
  image: string;
}

/**
 * Transform Strapi popular routes data to component format
 */
export const transformStrapiPopularRoutes = (
  strapiRoutes: StrapiPopularRoute[]
): PopularRoute[] => {
  return strapiRoutes.map((route) => {
    // Use the helper function to get image URL
    const imageUrl = getFirstStrapiImageUrl(
      route.image_url,
      "https://via.placeholder.com/250x100/E5E7EB/9CA3AF?text=No+Image"
    );

    return {
      id: route.id.toString(),
      title: route.routeName || `${route.fromName} - ${route.toName}`,
      price:
        route.priceDescription ||
        `Từ ${route.price?.toLocaleString("vi-VN")}đ` ||
        "<PERSON><PERSON><PERSON> hệ",
      image: imageUrl,
    };
  });
};
