import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  required?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, error, label, required, ...props }, ref) => {
    return (
      <div className="relative mb-6">
        {label && (
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <input
          ref={ref}
          className={cn(
            "w-full px-4 py-3 rounded-lg border bg-gray-50",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "disabled:cursor-not-allowed disabled:opacity-50",
            "transition-colors duration-200",
            error ? "border-red-500 focus:ring-red-500" : "border-gray-200",
            className
          )}
          {...props}
        />
        {/* Error message with absolute positioning to prevent UI jumping */}
        <div className="absolute left-0 top-full mt-1 min-h-[20px]">
          {error && (
            <p className="text-red-500 text-sm leading-tight animate-in fade-in-0 duration-200">
              {error}
            </p>
          )}
        </div>
      </div>
    );
  }
);

Input.displayName = "Input";
