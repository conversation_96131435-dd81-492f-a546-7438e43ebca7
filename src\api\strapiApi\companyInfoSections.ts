import { axiosInstanceStrapi } from "@/shared/api/axiosInstance";

export interface StrapiCompanyInfoLink {
  label: string;
  href: string;
}

export interface StrapiCompanyInfoSection {
  id: number;
  title: string;
  links: StrapiCompanyInfoLink[];
}

export type StrapiCompanyInfoSectionsResponse = StrapiCompanyInfoSection[];

/**
 * Fetch company info sections from Strapi CMS
 */
export const getCompanyInfoSections =
  async (): Promise<StrapiCompanyInfoSectionsResponse> => {
    const response = await axiosInstanceStrapi.get("company-info-sections");
    return response.data;
  };
