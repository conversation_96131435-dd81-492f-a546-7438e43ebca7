import axiosInstance from "@/shared/api/axiosInstance";
import {
  GET_ALL_MERCHANT_API_ENDPOINT,
  GET_MERCHANT_BY_ID_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";
import type { MerchantsResponse } from "./merchants.types";
import type {
  FoodCourtMenuResponse,
  GetMerchantMenuParams,
  FoodCourtApiError,
} from "./food-court.types";

// API Base URL for food court
const FOOD_COURT_BASE_URL = "/api/v1/food-court";

/**
 * Get merchant by ID
 * @param merchantId - The merchant ID
 * @returns Promise<MerchantsResponse>
 */
const getMerchantById = async (
  merchantId: string
): Promise<MerchantsResponse> => {
  try {
    const response = await axiosInstance.get(
      `${GET_MERCHANT_BY_ID_API_ENDPOINT}/${merchantId}`,
      {
        headers: {
          accept: "application/json",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    // Handle API errors
    const apiError: FoodCourtApiError = {
      message:
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch merchant",
      code: error.response?.status?.toString(),
      details: error.response?.data,
    };

    throw apiError;
  }
};

/**
 * Get merchant menu with categories and food items
 * @param params - Request parameters containing merchantId
 * @returns Promise<FoodCourtMenuResponse>
 */
const getMerchantMenu = async (
  params: GetMerchantMenuParams
): Promise<FoodCourtMenuResponse> => {
  try {
    const response = await axiosInstance.get<FoodCourtMenuResponse>(
      `${FOOD_COURT_BASE_URL}/merchants/${params.merchantId}/menu`,
      {
        headers: {
          accept: "application/json",
        },
      }
    );

    return response.data;
  } catch (error: any) {
    // Handle API errors
    const apiError: FoodCourtApiError = {
      message:
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch merchant menu",
      code: error.response?.status?.toString(),
      details: error.response?.data,
    };

    throw apiError;
  }
};

/**
 * Get merchant menu by ID (convenience function)
 * @param merchantId - The merchant ID
 * @returns Promise<FoodCourtMenuResponse>
 */
const getMerchantMenuById = async (
  merchantId: string
): Promise<FoodCourtMenuResponse> => {
  return getMerchantMenu({ merchantId });
};

// GET /api/v1/orders - Get all orders with optional pagination
const getAllMerchants = async (params?: {
  page?: number;
  limit?: number;
}): Promise<MerchantsResponse> => {
  const response = await axiosInstance.get(GET_ALL_MERCHANT_API_ENDPOINT, {
    params,
  });
  return response.data.merchants;
};

export {
  getMerchantById,
  getMerchantMenu,
  getMerchantMenuById,
  getAllMerchants,
};
