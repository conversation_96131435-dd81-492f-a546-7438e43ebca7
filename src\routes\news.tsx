import { createFileRoute } from "@tanstack/react-router";
import { Container } from "@/shared/components/Container";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { useArticles } from "@/shared/hooks/useArticles";
import { useNavigate } from "@tanstack/react-router";
import { useMemo } from "react";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

export const Route = createFileRoute("/news")({
  component: News,
});

function News() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data: articles, isLoading, error } = useArticles();

  // Transform articles for display
  const transformedArticles = useMemo(() => {
    if (!articles) return [];
    return articles.map((article) => ({
      id: article.id,
      title: article.title,
      content: article.content.substring(0, 200) + "...", // Preview
      published_at: article.published_at,
      image: getStrapiImageUrl(
        article.image_background,
        "/mock-images/image-2.png"
      ),
    }));
  }, [articles]);

  const handleArticleClick = (articleId: number) => {
    navigate({ to: "/news-detail/$id", params: { id: articleId.toString() } });
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <Container>
        <div className="py-8">
          <h1 className="text-3xl font-bold text-[#181818] mb-8">
            {t("header.news")}
          </h1>

          {isLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="bg-gray-300 h-48 rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">
                Không thể tải danh sách tin tức
              </p>
            </div>
          )}

          {!isLoading && !error && transformedArticles.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-600">Chưa có tin tức nào được đăng tải</p>
            </div>
          )}

          {!isLoading && transformedArticles.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {transformedArticles.map((article) => (
                <article
                  key={article.id}
                  className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleArticleClick(article.id)}
                >
                  <div className="h-48 bg-gray-200">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/mock-images/image-2.png";
                      }}
                    />
                  </div>
                  <div className="p-4">
                    <h2 className="font-bold text-lg text-[#181818] mb-2 line-clamp-2">
                      {article.title}
                    </h2>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                      {article.content}
                    </p>
                    <p className="text-gray-500 text-xs">
                      {new Date(article.published_at).toLocaleDateString(
                        "vi-VN"
                      )}
                    </p>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </Container>
    </div>
  );
}
