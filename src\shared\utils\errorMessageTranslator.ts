// Mapping of common API error messages from English to Vietnamese
const errorMessageMap: Record<string, string> = {
  // Authentication errors
  "Invalid OTP code": "Mã OTP không đúng",
  "OTP code expired": "Mã OTP đã hết hạn",
  "Phone number already exists": "Số điện thoại đã được đăng ký",
  "Phone number already registered": "Số điện thoại đã được đăng ký",
  "Phone number not registered": "Số điện thoại chưa được đăng ký",
  "User not found": "Không tìm thấy người dùng",
  "Invalid credentials": "Thông tin đăng nhập không đúng",
  Unauthorized: "Không có quyền truy cập",
  "Password too weak": "Mật khẩu quá yếu",
  "Invalid phone number": "Số điện thoại không hợp lệ",
  "Invalid phone number or password":
    "<PERSON>ố điện thoại hoặc Mật khẩu chưa chính xác",
  "Invalid phone number or SMS failed":
    "Số điện thoại không hợp lệ hoặc gửi SMS thất bại",
  "Phone number is required": "Số điện thoại là bắt buộc",
  "Password is required": "Mật khẩu là bắt buộc",
  "OTP is required": "Mã OTP là bắt buộc",
  "Invalid email format": "Định dạng email không hợp lệ",
  "Email already exists": "Email đã được sử dụng",
  "Account is locked": "Tài khoản đã bị khóa",
  "Too many attempts": "Quá nhiều lần thử. Vui lòng thử lại sau",
  "Session expired": "Phiên đăng nhập đã hết hạn",
  "Token expired": "Token đã hết hạn",
  "Invalid token": "Token không hợp lệ",

  "Phone number is not verified": "Số điện thoại chưa được xác thực",

  // Rate limiting errors
  RATE_LIMITED: "Bạn đã yêu cầu OTP quá nhiều lần. Vui lòng thử lại sau",
  "Rate limited": "Bạn đã yêu cầu OTP quá nhiều lần. Vui lòng thử lại sau",

  // Network errors
  "Network Error": "Lỗi kết nối mạng",
  "Request timeout": "Hết thời gian chờ",
  "Server Error": "Lỗi máy chủ",
  "Service unavailable": "Dịch vụ không khả dụng",
  "Internal server error": "Lỗi máy chủ nội bộ",

  // Validation errors
  "Required field": "Trường này là bắt buộc",
  "Invalid format": "Định dạng không hợp lệ",
  "Value too short": "Giá trị quá ngắn",
  "Value too long": "Giá trị quá dài",

  // Booking errors
  "Ticket not available": "Vé không có sẵn",
  "Booking failed": "Đặt vé thất bại",
  "Payment failed": "Thanh toán thất bại",
  "Order not found": "Không tìm thấy đơn hàng",

  // Generic errors
  "Something went wrong": "Có lỗi xảy ra",
  "Please try again": "Vui lòng thử lại",
  "Bad Request": "Yêu cầu không hợp lệ",
  Forbidden: "Không có quyền truy cập",
  "Not Found": "Không tìm thấy",
  "Method Not Allowed": "Phương thức không được phép",
  Conflict: "Xung đột dữ liệu",
  "Unprocessable Entity": "Dữ liệu không thể xử lý",
};

/**
 * Translates API error messages from English to Vietnamese
 * @param errorMessage - The error message to translate
 * @returns The translated Vietnamese message or the original message if no translation found
 */
export function translateErrorMessage(errorMessage: string): string {
  if (!errorMessage) return "Có lỗi xảy ra";

  // Check for exact match first
  if (errorMessageMap[errorMessage]) {
    return errorMessageMap[errorMessage];
  }

  // Check for partial matches (case insensitive)
  const lowerMessage = errorMessage.toLowerCase();
  for (const [englishError, vietnameseError] of Object.entries(
    errorMessageMap
  )) {
    if (lowerMessage.includes(englishError.toLowerCase())) {
      return vietnameseError;
    }
  }

  // If no translation found, return original message
  return errorMessage;
}

/**
 * Extracts and translates error message from API response
 * @param error - The error object from API call
 * @returns Translated Vietnamese error message
 */
export function getTranslatedErrorMessage(error: any): string {
  // Handle specific status codes with custom Vietnamese messages
  const statusCode = error?.response?.status;

  // Check for rate limiting first (can happen with various status codes)
  const errorType = error?.response?.data?.error_type;
  const message =
    error?.response?.data?.message || error?.response?.data?.error || "";

  if (
    errorType === "RATE_LIMITED" ||
    message.toLowerCase().includes("rate") ||
    message.toLowerCase().includes("only request otp")
  ) {
    // Extract seconds information if available
    const secondsMatch = message.match(/(\d+)\s*seconds?/i);

    if (secondsMatch) {
      const seconds = parseInt(secondsMatch[1]);
      return `Bạn đã yêu cầu OTP quá nhiều lần. Vui lòng thử lại sau ${seconds} giây`;
    }
    return "Bạn đã yêu cầu OTP quá nhiều lần. Vui lòng thử lại sau";
  }

  if (statusCode === 404) {
    return "Số điện thoại này chưa được đăng ký";
  }

  if (statusCode === 400) {
    // Bad Request - usually validation errors or invalid phone number
    if (
      message.toLowerCase().includes("phone") ||
      message.toLowerCase().includes("sms")
    ) {
      return "Số điện thoại không hợp lệ hoặc gửi SMS thất bại";
    }
    return "Thông tin không hợp lệ. Vui lòng kiểm tra lại";
  }

  if (statusCode === 409) {
    // Conflict - usually phone number already registered
    return message;
  }

  // Try to extract message from different possible locations
  const finalMessage =
    error?.response?.data?.message ||
    error?.response?.data?.error ||
    error?.response?.statusText ||
    error?.message ||
    "Có lỗi xảy ra";

  return translateErrorMessage(finalMessage);
}
