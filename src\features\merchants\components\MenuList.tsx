import React, { useRef, useEffect, useCallback, useState } from "react";
import { CategoryTab } from "./CategoryTab";
import { MenuItemCard } from "./MenuItemCard";
import type { Restaurant } from "@/features/merchants/types";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/shared/components/carousel";

interface MenuListProps {
  restaurant: Restaurant;
  activeCategory: string;
  onCategorySelect: (categoryId: string) => void;

  scrollToItemId?: string | null;
  onScrolledToItem?: () => void;
}

export const MenuList: React.FC<MenuListProps> = React.memo(
  ({
    restaurant,
    activeCategory,
    onCategorySelect,
    scrollToItemId,
    onScrolledToItem,
  }) => {
    // Refs
    const categoryRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
    const stickyHeaderRef = useRef<HTMLDivElement | null>(null);

    // State để track manual scroll vs auto scroll
    const [isManualScrolling, setIsManualScrolling] = useState(false);
    const manualScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    // Highlight item state
    const [highlightItemId, setHighlightItemId] = useState<string | null>(null);

    // Debounced scroll handler để tránh update quá thường xuyên
    const debouncedScrollHandler = useCallback(() => {
      if (isManualScrolling) return; // Tránh conflict với manual scroll

      const stickyHeader = stickyHeaderRef.current;
      if (!stickyHeader) return;

      const headerHeight = stickyHeader.getBoundingClientRect().height;
      let currentCategory = "";
      let maxVisibleArea = 0;

      // Tìm category có visible area lớn nhất thay vì chỉ dựa vào top position
      for (const category of restaurant.categories) {
        const ref = categoryRefs.current[category.id];
        if (ref) {
          const rect = ref.getBoundingClientRect();
          const visibleTop = Math.max(rect.top, headerHeight);
          const visibleBottom = Math.min(rect.bottom, window.innerHeight);
          const visibleArea = Math.max(0, visibleBottom - visibleTop);

          if (visibleArea > maxVisibleArea && visibleArea > 50) {
            maxVisibleArea = visibleArea;
            currentCategory = category.id;
          }
        }
      }

      // Chỉ update khi có sự thay đổi thực sự
      if (currentCategory && currentCategory !== activeCategory) {
        onCategorySelect(currentCategory);
      }
    }, [
      restaurant.categories,
      activeCategory,
      onCategorySelect,
      isManualScrolling,
    ]);

    // Optimized scroll handler với debounce
    const handleScroll = useCallback(() => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        debouncedScrollHandler();
      }, 100); // Debounce 100ms
    }, [debouncedScrollHandler]);

    // Manual category selection với improved logic
    const handleCategorySelect = useCallback(
      (categoryId: string) => {
        // Set manual scrolling flag
        setIsManualScrolling(true);

        // Clear existing timeout
        if (manualScrollTimeoutRef.current) {
          clearTimeout(manualScrollTimeoutRef.current);
        }

        // Update active category immediately
        onCategorySelect(categoryId);

        // Scroll to section
        setTimeout(() => {
          const ref = categoryRefs.current[categoryId];
          const stickyHeader = stickyHeaderRef.current;

          if (ref && stickyHeader) {
            const headerHeight = stickyHeader.getBoundingClientRect().height;
            const elementPosition = ref.getBoundingClientRect().top;
            const offsetPosition =
              elementPosition + window.pageYOffset - headerHeight - 10;

            window.scrollTo({
              top: offsetPosition,
              behavior: "smooth",
            });
          }

          // Reset manual scrolling flag sau khi scroll xong
          manualScrollTimeoutRef.current = setTimeout(() => {
            setIsManualScrolling(false);
          }, 1000); // 1 second để đảm bảo smooth scroll hoàn thành
        }, 50);
      },
      [onCategorySelect]
    );

    // Setup scroll listener
    useEffect(() => {
      window.addEventListener("scroll", handleScroll, { passive: true });

      return () => {
        window.removeEventListener("scroll", handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        if (manualScrollTimeoutRef.current) {
          clearTimeout(manualScrollTimeoutRef.current);
        }
      };
    }, [handleScroll]);

    // Cleanup timeouts on unmount
    useEffect(() => {
      return () => {
        if (manualScrollTimeoutRef.current) {
          clearTimeout(manualScrollTimeoutRef.current);
        }
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }, []);

    useEffect(() => {
      if (!scrollToItemId) return;
      let attempts = 0;
      const maxAttempts = 15;
      const interval = setInterval(() => {
        const el = document.getElementById(`menu-item-${scrollToItemId}`);
        if (el) {
          el.scrollIntoView({ behavior: "smooth", block: "center" });
          clearInterval(interval);
          setHighlightItemId(scrollToItemId);
          // Remove highlight after 1.5s
          setTimeout(() => setHighlightItemId(null), 1500);
          if (onScrolledToItem) onScrolledToItem();
        }
        attempts++;
        if (attempts > maxAttempts) {
          clearInterval(interval);
        }
      }, 100);
      return () => clearInterval(interval);
    }, [scrollToItemId, restaurant, onScrolledToItem]);

    return (
      <div className="bg-white">
        {/* Sticky Category Tabs */}
        <div ref={stickyHeaderRef} className="sticky top-0 z-30 bg-white py-2">
          <div className="px-4">
            <Carousel
              opts={{ align: "start", dragFree: true, slidesToScroll: 1 }}
            >
              <CarouselContent>
                {restaurant.categories.map((category) => (
                  <CarouselItem
                    key={category.id}
                    className="basis-auto w-auto px-1"
                  >
                    <CategoryTab
                      category={category}
                      isActive={activeCategory === category.id}
                      onSelect={() => handleCategorySelect(category.id)}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        {/* Menu Items */}
        <div className="px-4">
          {restaurant.categories.map((category) => (
            <div
              key={category.id}
              className="mb-6 pt-4"
              ref={(el) => {
                categoryRefs.current[category.id] = el;
              }}
            >
              <h2 className="text-base font-bold text-[#181818] mb-4">
                {category.name}
              </h2>

              <div className="space-y-5">
                {category.food_items.map((item) => (
                  <div key={item.id} id={`menu-item-${item.id}`}>
                    <MenuItemCard
                      merchantId={restaurant.id}
                      merchantName={restaurant.name}
                      item={item}
                      highlight={item.id === highlightItemId}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
);
