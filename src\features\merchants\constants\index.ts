// Default placeholder images
export const DEFAULT_PLACEHOLDER = "/placeholder-image.jpg";
export const DEFAULT_FOOD_PLACEHOLDER = DEFAULT_PLACEHOLDER;

// Delivery Type Enum (matching API format)
export enum DeliveryTypeEnum {
  SELF_PICKUP = "self_pickup",
  TABLE_DELIVERY = "table_delivery",
}

// Order Status Enum
export enum OrderStatusEnum {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  PREPARING = "preparing",
  READY = "ready",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

// Payment Status Enum
export enum PaymentStatusEnum {
  PENDING = "pending",
  PAID = "paid",
  FAILED = "failed",
}

// Thêm enum cho phương thức thanh toán
export enum PaymentTypeEnum {
  CASH = "cash",
  CARD = "card",
  E_WALLET = "e_wallet",
}
