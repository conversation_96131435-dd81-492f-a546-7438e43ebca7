import React, { useState } from "react";
import type { FoodCourtOrder } from "@/features/merchants/api/orders.types";

interface OrderDetailProps {
  order: FoodCourtOrder;
}

const OrderDetail: React.FC<OrderDetailProps> = ({ order }) => {
  const [expanded, setExpanded] = useState(false);
  return (
    <>
      <button
        className="self-end text-xs text-blue-600 hover:underline mt-1"
        onClick={() => setExpanded((prev) => !prev)}
      >
        {expanded ? "Ẩn chi tiết" : "Xem chi tiết"}
      </button>
      {expanded && (
        <div className="mt-2 space-y-3">
          {order.merchant_orders.map((merchantOrder) => (
            <div key={merchantOrder.id} className="bg-gray-50 rounded p-2">
              <div className="font-semibold text-blue-600 mb-1">
                {merchantOrder.merchant?.name || "-"}
              </div>
              <div className="space-y-1">
                {merchantOrder.order_items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>
                      {item.food_item.name}{" "}
                      <span className="text-xs text-gray-400">
                        x{item.quantity}
                      </span>
                    </span>
                    <span>
                      {(item.unit_price * item.quantity).toLocaleString()}₫
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default OrderDetail;
