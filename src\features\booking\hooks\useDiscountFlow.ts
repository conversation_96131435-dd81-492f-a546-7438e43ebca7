import { useDiscountPlaces } from "./useDiscountPlaces";
import { useDiscountBusOperators } from "./useDiscountBusOperators";
import type { StrapiDiscountPlace } from "@/shared/types/strapi";

/**
 * Combined hook that demonstrates the discount workflow:
 * 1. Fetch discount places first
 * 2. Use place_id to fetch discount bus operators for a specific place
 */
export const useDiscountFlow = (selectedPlaceId?: number) => {
  // First, get all discount places
  const discountPlacesQuery = useDiscountPlaces();

  // Then, get discount bus operators for the selected place
  const discountBusOperatorsQuery = useDiscountBusOperators(
    selectedPlaceId ? { place_id: selectedPlaceId } : undefined,
    !!selectedPlaceId // Only enabled when place_id is provided
  );

  // Helper function to get discount bus operators for a specific place
  const getOperatorsForPlace = (placeId: number) => {
    return useDiscountBusOperators({ place_id: placeId });
  };

  // Helper to find a place by place_id
  const findPlaceById = (placeId: number): StrapiDiscountPlace | undefined => {
    return discountPlacesQuery.data?.find(
      (place) => place.place_id === placeId
    );
  };

  return {
    // Discount places data and state
    discountPlaces: discountPlacesQuery.data,
    isLoadingPlaces: discountPlacesQuery.isLoading,
    placesError: discountPlacesQuery.error,

    // Discount bus operators data and state (for selected place)
    discountBusOperators: discountBusOperatorsQuery.data,
    isLoadingOperators: discountBusOperatorsQuery.isLoading,
    operatorsError: discountBusOperatorsQuery.error,

    // Helper functions
    getOperatorsForPlace,
    findPlaceById,

    // Combined loading state
    isLoading:
      discountPlacesQuery.isLoading ||
      (selectedPlaceId && discountBusOperatorsQuery.isLoading),
  };
};

/**
 * Hook to get discount bus operators for all places
 * This will fetch operators for each place individually
 */
export const useAllDiscountOperators = () => {
  const { discountPlaces } = useDiscountFlow();

  const operatorQueries =
    discountPlaces?.map((place) => ({
      place,
      query: useDiscountBusOperators({ place_id: place.place_id }),
    })) || [];

  return {
    operatorQueries,
    isLoading: operatorQueries.some((q) => q.query.isLoading),
    hasError: operatorQueries.some((q) => q.query.error),
  };
};
