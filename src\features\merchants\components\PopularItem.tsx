import { useCallback, useEffect, useState } from "react";
import { Container } from "@/shared/components/Container";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/shared/components/carousel";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import PopularItemCard from "./PopularItemCard";
import { useGetAllItems } from "@/shared/hooks/useItems";
import type { Item } from "@/api/items/items.types";

export default function PopularItems() {
  const { data: items } = useGetAllItems({ isHighlighted: true });

  const [api, setApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  // Setup carousel API
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    onSelect();
    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api.off("select", onSelect);
      api.off("reInit", onSelect);
    };
  }, [api]);

  const scrollToPrevious = useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollToNext = useCallback(() => {
    api?.scrollNext();
  }, [api]);

  return (
    <div className="rounded-2xl bg-[#FFFFFF] p-4">
      <div className="flex justify-between">
        <p className="text-[#FF7F37] text-base">Món nổi bật</p>
        <button className="text-sm text-[#5C5C5C]">Xem tất cả</button>
      </div>

      <Container>
        <div className="flex items-center justify-between mb-6">
          {/* Custom Navigation - Hidden on mobile */}
          <div className="hidden md:flex gap-2">
            <button
              onClick={scrollToPrevious}
              disabled={!canScrollPrev}
              className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 ${
                !canScrollPrev
                  ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                  : "bg-white text-[#181818] hover:bg-[#2D5BFF] hover:text-white border-[#EFEFEF] hover:border-[#2D5BFF]"
              }`}
              aria-label="Previous routes"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <button
              onClick={scrollToNext}
              disabled={!canScrollNext}
              className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 shadow-[0px_0px_30px_0px_rgba(132,132,132,0.25)] ${
                !canScrollNext
                  ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                  : "bg-[#2D5BFF] text-white hover:bg-[#1e40af] border-[#2D5BFF]"
              }`}
              aria-label="Next routes"
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </Container>

      <Container className="py-2 px-0 sm:px-20 lg:px-25 max-w-full lg:max-w-7xl">
        <Carousel
          setApi={setApi}
          className="w-full"
          opts={{
            align: "start",
            slidesToScroll: 1,
            dragFree: true,
          }}
        >
          <CarouselContent className="sm:pl-0">
            {items?.map((item: Item) => (
              <CarouselItem
                key={item.id}
                className="basis-1/2,5 md:basis-1/4 lg:basis-1/5 flex justify-center"
              >
                <PopularItemCard item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </Container>
    </div>
  );
}
