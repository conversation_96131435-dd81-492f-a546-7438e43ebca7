import React, { useCallback } from "react";
import { cn } from "@/lib/utils";
import type { MenuCategory } from "@/features/merchants/types";

interface CategoryTabProps {
  category: MenuCategory;
  isActive: boolean;
  onSelect: () => void;
}

export const CategoryTab: React.FC<CategoryTabProps> = React.memo(
  ({ category, isActive, onSelect }) => {
    // Optimize click handler để tránh re-render
    const handleClick = useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        onSelect();
      },
      [onSelect]
    );

    return (
      <div className="flex flex-col items-center min-w-fit">
        <button
          onClick={handleClick}
          className={cn(
            "flex items-center justify-center gap-3 py-[7px] px-4 text-sm font-bold transition-all whitespace-nowrap select-none",
            isActive ? "text-[#181818]" : "text-[#5C5C5C]"
          )}
          type="button"
        >
          <div className="flex items-center justify-center text-center">
            <span className="leading-tight">{category.name}</span>
          </div>
        </button>
        {isActive && (
          <div className="w-full h-0.5 bg-[#2D5BFF] rounded-sm transition-all duration-200" />
        )}
      </div>
    );
  },
  // Custom comparison function để tránh re-render không cần thiết
  (prevProps, nextProps) => {
    return (
      prevProps.category.id === nextProps.category.id &&
      prevProps.category.name === nextProps.category.name &&
      prevProps.isActive === nextProps.isActive &&
      prevProps.onSelect === nextProps.onSelect
    );
  }
);
