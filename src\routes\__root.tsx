import { Outlet, createRootRoute, useLocation } from "@tanstack/react-router";
import { SelectedTripsProvider } from "../stores/selectedTripsStore";
import { Header } from "@/shared/components/Header";
import Footer from "@/shared/components/Footer";
import { useScreenSize } from "@/shared/hooks/useScreenSize";

function RootComponent() {
  const location = useLocation();
  const { isMobile } = useScreenSize();

  const isCheckoutOrPaymentSuccess =
    isMobile &&
    (location.pathname === "/booking/checkout" ||
      location.pathname === "/payment-success");

  // Chỉ ẩn Header khi ở mobile và đang ở trang search/checkout
  const shouldHideHeader =
    isMobile &&
    (location.pathname === "/booking/search" ||
      location.pathname === "/booking/checkout");

  const shouldHideFooter = isMobile && isCheckoutOrPaymentSuccess;

  if (location.pathname.includes("/merchants")) {
    return <Outlet />;
  }

  return (
    <SelectedTripsProvider>
      {!shouldHideHeader && <Header />}
      <Outlet />
      {!shouldHideFooter && <Footer />}
      {/* <TanStackRouterDevtools /> */}
    </SelectedTripsProvider>
  );
}

export const Route = createRootRoute({
  component: RootComponent,
});
