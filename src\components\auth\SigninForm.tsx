import React, { useState } from "react";
import { Button } from "@/shared/components/button";
import { Input, PasswordInput, Checkbox } from "@/shared/components";
import { cn } from "@/lib/utils";
import { useSignin } from "@/shared/hooks/useAuth";
import { getTranslatedErrorMessage } from "@/shared/utils/errorMessageTranslator";
import { validateVietnamesePhoneNumber } from "@/shared/utils/phoneNumberUtils";

interface SigninFormProps {
  onSigninSuccess?: () => void;
  onForgotPassword?: () => void;
  className?: string;
}

export const SigninForm: React.FC<SigninFormProps> = ({
  onSigninSuccess,
  onForgotPassword,
  className,
}) => {
  const [formData, setFormData] = useState({
    phoneNumber: "",
    password: "",
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { mutateAsync: signin, isPending: isLoading } = useSignin();

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    const phoneValidation = validateVietnamesePhoneNumber(formData.phoneNumber);
    if (!phoneValidation.isValid) {
      newErrors.phoneNumber = phoneValidation.errorMessage!;
    }

    if (!formData.password) {
      newErrors.password = "Mật khẩu là bắt buộc";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignin = async () => {
    if (!validateForm()) return;

    try {
      // Normalize phone number before sending to API
      const phoneValidation = validateVietnamesePhoneNumber(
        formData.phoneNumber
      );
      const normalizedPhone =
        phoneValidation.normalizedNumber || formData.phoneNumber;

      await signin({
        phone_number: normalizedPhone,
        password: formData.password,
      });
      onSigninSuccess?.();
    } catch (error: any) {
      console.error("Signin error:", error);
      setErrors({
        general: getTranslatedErrorMessage(error),
      });
    }
  };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="space-y-4">
        {/* Phone Number */}
        <Input
          type="tel"
          name="phoneNumber"
          placeholder="Số điện thoại "
          value={formData.phoneNumber}
          onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
          error={errors.phoneNumber}
        />

        {/* Password */}
        <PasswordInput
          name="password"
          placeholder="Mật khẩu"
          value={formData.password}
          onChange={(e) => handleInputChange("password", e.target.value)}
          error={errors.password}
        />

        {/* Remember Me and Forgot Password */}
        <div className="flex items-center justify-between">
          <Checkbox
            checked={rememberMe}
            onChange={setRememberMe}
            label="Lưu tài khoản"
          />
          <button
            type="button"
            className="text-sm hover:text-red-600"
            onClick={onForgotPassword}
          >
            Quên mật khẩu?
          </button>
        </div>

        {/* Error Message */}
        {errors.general && (
          <p className="text-red-500 text-sm text-center">{errors.general}</p>
        )}

        {/* Submit Button */}
        <Button
          onClick={handleSignin}
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-bold"
        >
          {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
        </Button>
      </div>
    </div>
  );
};
