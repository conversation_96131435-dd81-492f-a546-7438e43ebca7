import React, { useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Button } from "@/shared/components/button";
import { cn } from "@/lib/utils";

interface SignupSuccessProps {
  onContinue?: () => void;
  className?: string;
}

export const SignupSuccess: React.FC<SignupSuccessProps> = ({
  onContinue,
  className,
}) => {
  const navigate = useNavigate();

  useEffect(() => {
    const timeout = setTimeout(() => {
      navigate({ to: "/" });
    }, 2500);

    return () => clearTimeout(timeout);
  }, [navigate]);

  return (
    <div className={cn("w-full max-w-md mx-auto text-center", className)}>
      {/* Success Icon */}
      <div className="flex justify-center mb-8">
        <img
          src="/assets/Successmark.svg"
          alt="Success"
          className="w-20 h-20"
        />
      </div>

      {/* Success Message */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          <PERSON><PERSON>ng ký thành công
        </h2>
      </div>

      {/* Continue Button */}
      {onContinue && (
        <Button
          onClick={onContinue}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-6 rounded-lg font-bold"
        >
          Tiếp tục
        </Button>
      )}
    </div>
  );
};
