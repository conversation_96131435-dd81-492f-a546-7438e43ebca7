import type { Merchant } from "@/api/merchants/merchants.types";

export default function PopularMerchantCard({
  merchant,
  onClick,
  isSelected,
}: {
  merchant: Merchant;
  onClick?: () => void;
  isSelected?: boolean;
}) {
  return (
    <div className="w-[150px] flex flex-col">
      <div className="relative mb-2">
        <div
          className={`w-full h-[100px] rounded-lg cursor-pointer border-2 transition-all duration-200 bg-white overflow-hidden flex items-center justify-center ${
            isSelected ? "border-[#FF7F37] shadow-lg" : "border-transparent"
          }`}
          onClick={onClick}
        >
          <img
            className="w-full h-full object-full"
            src={
              merchant.logo_image?.sizes?.large ??
              "assets/merchants/restaurant_default.jpg"
            }
            alt="Restaurant"
          />
        </div>
      </div>

      <div className="bg-transparent rounded-b-lg px-0 pb-2">
        <p
          className={`text-sm font-bold text-start ${
            isSelected ? "text-[#FF7F37]" : "text-[#5C5C5C]"
          }`}
        >
          {merchant.name}
        </p>
      </div>
    </div>
  );
}
