import type { logo_image } from "./merchants.types";

// QR Table API Types
export interface QRTable {
  id: string;
  name: string;
  description?: string;
  status: string;
  capacity: number;
  location?: string;
  qr_code: string;
  food_court_id: string;
  created_at: string;
  updated_at: string;
}

export interface QRMerchant {
  id: string;
  name: string;
  description: string;
  contact_phone: string;
  operating_hours_start: string;
  operating_hours_end: string;
  cover_image: string;
  logo_image: logo_image;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface QRFoodCourt {
  id: string;
  name: string;
  description: string;
  address: string;
  contact_phone: string;
  operating_hours_start: string;
  operating_hours_end: string;
  cover_image: string;
  logo_image: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface QRApiResponse {
  table: QRTable;
  food_court: QRFoodCourt;
  merchants: QRMerchant[];
}

// Request types
export interface GetQRInfoRequest {
  qrCode: string;
}
