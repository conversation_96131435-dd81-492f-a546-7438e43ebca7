import React from "react";
import { cn } from "@/lib/utils";

interface AuthTabsProps {
  activeTab: "signin" | "signup";
  onTabChange: (tab: "signin" | "signup") => void;
}

export const AuthTabs: React.FC<AuthTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  return (
    <div className="bg-gray-100 p-1 rounded-lg mb-8">
      <div className="flex">
        <button
          className={cn(
            "flex-1 py-2 px-4 rounded-md text-sm font-bold transition-colors",
            activeTab === "signin"
              ? "bg-blue-600 text-white"
              : "text-gray-600 hover:text-gray-800"
          )}
          onClick={() => onTabChange("signin")}
        >
          Đăng nhập
        </button>
        <button
          className={cn(
            "flex-1 py-2 px-4 rounded-md text-sm font-bold transition-colors",
            activeTab === "signup"
              ? "bg-blue-600 text-white"
              : "text-gray-600 hover:text-gray-800"
          )}
          onClick={() => onTabChange("signup")}
        >
          <PERSON><PERSON><PERSON> k<PERSON>
        </button>
      </div>
    </div>
  );
};
