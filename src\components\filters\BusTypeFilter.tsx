import React from "react";
import { clsx } from "clsx";
import type { BusType } from "@/shared/types/bus";

interface BusTypeFilterProps {
  selectedTypes: BusType[];
  onChange: (types: BusType[]) => void;
}

const busTypeOptions = [
  { value: "standard" as BusType, label: "Ghế" },
  { value: "sleeper" as BusType, label: "Giường" },
  { value: "limousine" as BusType, label: "Limousine" },
];

const BusTypeFilter: React.FC<BusTypeFilterProps> = ({
  selectedTypes,
  onChange,
}) => {
  const handleTypeToggle = (busType: BusType) => {
    if (selectedTypes.includes(busType)) {
      onChange(selectedTypes.filter((type) => type !== busType));
    } else {
      onChange([...selectedTypes, busType]);
    }
  };

  return (
    <div className="flex flex-wrap xl:flex-row gap-3 py-2">
      {busTypeOptions.map((option) => {
        const isSelected = selectedTypes.includes(option.value);
        return (
          <button
            key={option.value}
            onClick={() => handleTypeToggle(option.value)}
            className={clsx(
              "px-6 py-2 rounded-lg border font-mulish font-bold text-sm text-center transition-all",
              isSelected
                ? "border-[#2D5BFF] bg-[#2D5BFF] text-white"
                : "border-[#EDEDED] bg-white text-[#5C5C5C] hover:border-[#D7D7D7]"
            )}
          >
            {option.label}
          </button>
        );
      })}
    </div>
  );
};

export default BusTypeFilter;
