import React from 'react';

interface BusSearchLayoutProps {
  children: React.ReactNode;
}

const BusSearchLayout: React.FC<BusSearchLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex gap-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default BusSearchLayout;