import React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { ArrowLeftOnRectangleIcon } from "@heroicons/react/24/outline";
import { UserIcon } from "@heroicons/react/24/outline";
import { TicketIcon as TicketIconOutline } from "@heroicons/react/24/outline";
import { TicketIcon as TicketIconSolid } from "@heroicons/react/24/solid";
import { KeyIcon } from "@heroicons/react/24/outline";

export interface SidebarItem {
  label: string;
  icon: "user" | "ticket" | "key";
  path: string;
  badgeDot?: boolean;
  onClick?: () => void;
  disabled?: boolean;
}

export interface SidebarProps {
  items: SidebarItem[];
  activePath: string;
  onNavigate?: (path: string) => void;
  logoutLabel?: string;
  onLogout?: () => void;
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = React.memo(
  ({
    items,
    activePath,
    onNavigate,
    logoutLabel = "Đăng xuất",
    onLogout,
    className,
  }) => {
    return (
      <aside
        className={cn(
          "bg-white rounded-[24px] w-full h-full max-h-[calc(100vh-300px)] w-[280px] flex flex-col gap-3 shadow-[0px_2px_4px_0px_rgba(0,0,0,0.05)]",
          className
        )}
      >
        {items.map((item) => {
          const active = activePath === item.path;
          return (
            <Button
              key={item.path}
              variant={active ? "secondary" : "ghost"}
              className={cn(
                "flex items-center gap-4 py-4 text-left transition text-[15px] w-full justify-start h-auto",
                active
                  ? "text-[#2F54EB] bg-white hover:bg-[#EEF2FF]/90"
                  : "text-[#595959] hover:bg-[#FAFAFA]"
              )}
              onClick={() => {
                if (item.disabled) return;
                item.onClick?.();
                onNavigate?.(item.path);
              }}
              disabled={item.disabled}
            >
              <span className="relative flex items-center">
                <span
                  className={cn(
                    "flex items-center justify-center rounded-lg",
                    active ? "bg-[#2D5BFF]" : "bg-[#EFEFEF]"
                  )}
                  style={{ width: 32, height: 32, padding: 8 }}
                >
                  {item.icon === "user" && (
                    <UserIcon
                      className={cn(
                        "w-4 h-4",
                        active ? "text-white" : "text-[#7C7B7B]"
                      )}
                    />
                  )}
                  {item.icon === "ticket" &&
                    (active ? (
                      <TicketIconSolid className="w-4 h-4 text-white" />
                    ) : (
                      <TicketIconOutline className="w-4 h-4 text-[#8A8A8A]" />
                    ))}
                  {item.icon === "key" && (
                    <KeyIcon
                      className={cn(
                        "w-4 h-4",
                        active ? "text-white" : "text-[#7C7B7B]"
                      )}
                    />
                  )}
                </span>
              </span>
              <span className={cn("font-normal", active && "font-bold")}>
                {item.label}
              </span>
            </Button>
          );
        })}
        <Button
          variant="ghost"
          className="flex gap-4 px-6 py-4 text-left font-normal text-[15px] text-[#FF4D4F] hover:bg-[#FFF1F0] w-full justify-start h-auto"
          onClick={onLogout}
        >
          <ArrowLeftOnRectangleIcon className="w-10 h-10 ml-2" />
          {logoutLabel}
        </Button>
      </aside>
    );
  }
);
