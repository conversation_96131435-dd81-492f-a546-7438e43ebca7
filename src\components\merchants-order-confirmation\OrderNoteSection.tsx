import React from "react";

interface OrderNoteSectionProps {
  note: string;
  onNoteChange: (note: string) => void;
  disabled?: boolean;
  maxLength?: number;
}

export const OrderNoteSection: React.FC<OrderNoteSectionProps> = ({
  note,
  onNoteChange,
  disabled = false,
  maxLength = 200,
}) => {
  return (
    <div className="bg-white rounded-xl border border-[#EDEDED] p-3">
      <div className="bg-[#F8F8F8] rounded-lg h-[100px] relative">
        <textarea
          value={note}
          onChange={(e) => onNoteChange(e.target.value)}
          className="w-full h-full bg-transparent p-4 text-sm font-normal text-[#5C5C5C] outline-none resize-none"
          placeholder="Ghi chú đơn hàng"
          maxLength={maxLength}
          disabled={disabled}
        />
      </div>
    </div>
  );
};
