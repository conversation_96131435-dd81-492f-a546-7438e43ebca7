# API Integration Guide

This document explains how to use the authentication and fleet management APIs with TanStack Query.

## 🔐 Authentication APIs

### Available Endpoints

1. **POST /api/v1/auth/signup** - Sign up with phone number
2. **POST /api/v1/auth/verify-otp** - Verify OTP code
3. **POST /api/v1/auth/set-password** - Set user password
4. **POST /api/v1/auth/signin** - Sign in with password
5. **GET /api/v1/auth/profile** - Get user profile
6. **GET /api/v1/auth/admin/test** - Admin test endpoint

### Usage Examples

#### 1. Sign Up Flow

```tsx
import { useSignup, useVerifyOtp, useSetPassword } from '../hooks/useAuth';

function SignupForm() {
  const signupMutation = useSignup();
  const verifyOtpMutation = useVerifyOtp();
  const setPasswordMutation = useSetPassword();

  // Step 1: Sign up with phone number
  const handleSignup = () => {
    signupMutation.mutate({
      phone_number: "+84901234567"
    });
  };

  // Step 2: Verify OTP
  const handleVerifyOtp = () => {
    verifyOtpMutation.mutate({
      phone_number: "+84901234567",
      otp_code: "123456"
    });
  };

  // Step 3: Set password and complete registration
  const handleSetPassword = () => {
    setPasswordMutation.mutate({
      password: "MySecurePassword123!",
      full_name: "Nguyen Van A",
      email: "<EMAIL>"
    });
  };

  return (
    // Your form JSX here
  );
}
```

#### 2. Sign In

```tsx
import { useSignin, useProfile } from '../hooks/useAuth';

function SigninForm() {
  const signinMutation = useSignin();
  const { data: profile } = useProfile();

  const handleSignin = () => {
    signinMutation.mutate({
      phone_number: "+84901234567",
      password: "MySecurePassword123!"
    });
  };

  if (profile) {
    return <div>Welcome, {profile.full_name}!</div>;
  }

  return (
    // Your signin form JSX here
  );
}
```

#### 3. Auto Authentication

```tsx
import { useInitializeAuth } from '../hooks/useAuth';

function App() {
  // This will automatically restore auth token from localStorage
  useInitializeAuth();

  return (
    // Your app JSX here
  );
}
```

#### 4. Logout

```tsx
import { useLogout } from "../hooks/useAuth";

function LogoutButton() {
  const logoutMutation = useLogout();

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <button onClick={handleLogout}>
      {logoutMutation.isPending ? "Logging out..." : "Logout"}
    </button>
  );
}
```

## 🚐 Fleet Management APIs

### Available Endpoints

1. **POST /api/v1/fleets** - Create a new fleet
2. **GET /api/v1/fleets** - Get all fleets
3. **GET /api/v1/fleets/:id** - Get fleet by ID
4. **PUT /api/v1/fleets/:id** - Update fleet
5. **DELETE /api/v1/fleets/:id** - Delete fleet

### Usage Examples

#### 1. Create Fleet

```tsx
import { useCreateFleet } from "../hooks/useFleets";

function CreateFleetForm() {
  const createFleetMutation = useCreateFleet();

  const handleCreateFleet = () => {
    createFleetMutation.mutate({
      name: "City Express Fleet",
      description: "Fleet for city routes",
    });
  };

  return (
    <button
      onClick={handleCreateFleet}
      disabled={createFleetMutation.isPending}
    >
      {createFleetMutation.isPending ? "Creating..." : "Create Fleet"}
    </button>
  );
}
```

#### 2. List Fleets

```tsx
import { useFleets } from "../hooks/useFleets";

function FleetList() {
  const { data: fleets, isLoading, error } = useFleets();

  if (isLoading) return <div>Loading fleets...</div>;
  if (error) return <div>Error loading fleets</div>;

  return (
    <ul>
      {fleets?.map((fleet) => (
        <li key={fleet.id}>
          {fleet.name} - {fleet.description}
        </li>
      ))}
    </ul>
  );
}
```

#### 3. Update Fleet

```tsx
import { useUpdateFleet } from "../hooks/useFleets";

function UpdateFleetForm({ fleetId }: { fleetId: number }) {
  const updateFleetMutation = useUpdateFleet();

  const handleUpdate = () => {
    updateFleetMutation.mutate({
      id: fleetId,
      data: {
        name: "Updated Fleet Name",
        description: "Updated description",
      },
    });
  };

  return <button onClick={handleUpdate}>Update Fleet</button>;
}
```

#### 4. Delete Fleet

```tsx
import { useDeleteFleet } from "../hooks/useFleets";

function DeleteFleetButton({ fleetId }: { fleetId: number }) {
  const deleteFleetMutation = useDeleteFleet();

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this fleet?")) {
      deleteFleetMutation.mutate(fleetId);
    }
  };

  return <button onClick={handleDelete}>Delete Fleet</button>;
}
```

## 🔧 Configuration

### Environment Variables

Make sure to set your API base URL in your `.env` file:

```env
VITE_API_BASE_URL=https://your-api-domain.com
```

### Authorization

The authentication functions automatically handle JWT tokens:

- Tokens are stored in `localStorage`
- Automatically added to request headers
- Cleared on logout
- Restored on app initialization

### Error Handling

All hooks include built-in error handling:

```tsx
const signupMutation = useSignup();

if (signupMutation.error) {
  // Handle error
  console.error("Signup failed:", signupMutation.error);
}
```

### Query Invalidation

The hooks automatically invalidate related queries when data changes:

- Creating/updating/deleting fleets invalidates fleet lists
- Authentication actions invalidate auth queries
- Profile updates invalidate profile data

## 📝 Types

All TypeScript types are available in `src/types/auth.ts`:

- `SignupRequest`, `SignupResponse`
- `VerifyOtpRequest`, `VerifyOtpResponse`
- `SetPasswordRequest`, `SetPasswordResponse`
- `SigninRequest`, `SigninResponse`
- `UserProfile`, `AuthUser`
- `CreateFleetRequest`, `Fleet`
- `ApiError`

## 🧪 Testing

See `src/components/auth/AuthExample.tsx` for a complete implementation example that demonstrates all the authentication and fleet management features.
