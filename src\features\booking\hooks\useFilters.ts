import { useState, useMemo } from "react";
import type { BusFilters, Bus } from "@/shared/types/bus";

const useFilters = (buses: Bus[]) => {
  const [filters, setFilters] = useState<BusFilters>({
    sortBy: "default",
    timeSlots: [],
    busTypes: [],
  });

  const filteredAndSortedBuses = useMemo(() => {
    let result = [...buses];

    // Apply time slot filters
    if (filters.timeSlots.length > 0) {
      result = result.filter((bus) => {
        const departureTime = bus.rawDepartureTime;
        const hour = parseInt(departureTime.split(":")[0]);

        return filters.timeSlots.some((slot) => {
          switch (slot) {
            case "morning":
              return hour >= 0 && hour < 6;
            case "afternoon":
              return hour >= 12 && hour < 18;
            case "evening":
              return hour >= 18 && hour < 24;
            case "night":
              return hour >= 6 && hour < 12;
            default:
              return false;
          }
        });
      });
    }

    // Apply bus type filters
    if (filters.busTypes.length > 0) {
      result = result.filter((bus) => filters.busTypes.includes(bus.type));
    }

    // Apply sorting
    switch (filters.sortBy) {
      case "earliest":
        result.sort((a, b) =>
          a.rawDepartureTime.localeCompare(b.rawDepartureTime)
        );
        break;
      case "latest":
        result.sort((a, b) =>
          b.rawDepartureTime.localeCompare(a.rawDepartureTime)
        );
        break;
      case "cheapest":
        result.sort((a, b) => a.price - b.price);
        break;
      case "most_expensive":
        result.sort((a, b) => b.price - a.price);
        break;
      default:
        // Keep original order
        break;
    }

    return result;
  }, [buses, filters]);

  return {
    filters,
    setFilters,
    filteredAndSortedBuses,
  };
};

export default useFilters;
