import { useCallback } from "react";
import { useStore } from "@tanstack/react-store";
import { cartStore, cartActions } from "@/stores/cartStore";
import type { Restaurant, CartItem } from "@/features/merchants/types";
import { simpleCartActions } from "@/stores/simpleCartStore";

export const useCartStorage = () => {
  const multipleRestaurantCartData = useStore(
    cartStore,
    (state) => state.multipleRestaurantCartData
  );

  const saveCartData = useCallback((restaurant: Restaurant) => {
    const cartItems: CartItem[] = [];

    restaurant.categories.forEach((category) => {
      category.food_items.forEach(() => {
        // if (item.quantity > 0) {
        //   cartItems.push({
        //     id: item.id,
        //     name: item.name,
        //     price: item.price,
        //     quantity: item.quantity,
        //     image_url: item.image_url,
        //     description: item.description,
        //   });
        // }
      });
    });

    if (cartItems.length > 0) {
      simpleCartActions.addItem({
        id: cartItems[0].id,
        name: cartItems[0].name,
        price: cartItems[0].price,
        quantity: cartItems[0].quantity,
        image_url: cartItems[0].image_url,
        description: cartItems[0].description,
        merchantId: restaurant.id,
      });
    }
  }, []);

  const clearCartData = useCallback(() => {
    cartActions.clearMultipleRestaurantCart();
  }, []);

  return {
    multipleRestaurantCartData,
    saveCartData,
    clearCartData,
  };
};
