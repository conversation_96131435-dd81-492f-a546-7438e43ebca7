import React, { useState } from "react";
import { updatePassword } from "@/api/auth/authApi";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import {
  passwordChangeSchema,
  type PasswordChangeFormValues,
} from "@/schemas/passwordSchema";
import { usePasswordInput } from "@/hooks/usePasswordInput";

type FormValues = PasswordChangeFormValues;

const PasswordChangePanel: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Use custom hooks for password inputs
  const oldPasswordInput = usePasswordInput();
  const newPasswordInput = usePasswordInput();
  const confirmPasswordInput = usePasswordInput();

  const form = useForm<FormValues>({
    resolver: zodResolver(passwordChangeSchema),
    mode: "onChange",
  });

  const handleSubmit = async (values: FormValues) => {
    setError(null);
    setSuccess(false);
    setLoading(true);
    try {
      await updatePassword({
        current_password: values.oldPassword,
        new_password: values.newPassword,
      });
      setSuccess(true);
      form.reset();
    } catch (err: any) {
      if (err.status === 400) {
        return setError("Mật khẩu mới phải khác với mật khẩu hiện tại");
      }
      if (err.status === 401) {
        return setError("Mật khẩu hiện tại chưa chính xác");
      }
      setError("Đã xảy ra lỗi, vui lòng thử lại");
    } finally {
      setLoading(false);
    }
  };

  const {
    register,
    formState: { errors },
  } = form;

  const RequiredLabel: React.FC<{ text: string; error?: string }> = ({
    text,
    error,
  }) => (
    <div>
      <label className="block font-bold text-[#5C5C5C] text-[12px] leading-4 mb-1 font-mulish">
        {text} <span className="text-red-500">*</span>
      </label>
      {error && (
        <span className="text-red-500 text-[11px] mt-0.5 block">{error}</span>
      )}
    </div>
  );

  return (
    <form
      className="w-full max-w-full sm:max-w-2xl md:max-w-5xl mx-auto px-2 sm:px-0"
      onSubmit={form.handleSubmit(handleSubmit)}
    >
      <div className="font-bold text-[#181818] text-[14px] leading-[18px] mb-4">
        Thay đổi mật khẩu
      </div>
      <div className="flex flex-col md:flex-row gap-4 md:gap-6 w-full">
        {/* Left: Old password */}
        <div className="flex-1 min-w-0">
          <div
            className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${errors.oldPassword ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
          >
            <RequiredLabel
              text="Mật khẩu cũ"
              error={errors.oldPassword?.message}
            />
            <div className="flex items-center relative">
              <input
                type={oldPasswordInput.show ? "text" : "password"}
                className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                placeholder="Nhập mật khẩu cũ"
                autoComplete="current-password"
                maxLength={oldPasswordInput.maxLength}
                {...register("oldPassword", {
                  onChange: oldPasswordInput.handlePasswordChange,
                })}
              />
              <button
                type="button"
                onClick={oldPasswordInput.toggle}
                className="absolute right-0 top-1/2 -translate-y-1/2 p-1 hover:text-[#2d5bff]"
                tabIndex={-1}
              >
                {oldPasswordInput.show ? (
                  <EyeSlashIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                ) : (
                  <EyeIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                )}
              </button>
            </div>
          </div>
        </div>
        {/* Right: New password + Confirm */}
        <div className="flex-1 min-w-0 flex flex-col gap-4">
          <div
            className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${errors.newPassword ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
          >
            <RequiredLabel
              text="Mật khẩu mới"
              error={errors.newPassword?.message}
            />
            <div className="flex items-center relative">
              <input
                type={newPasswordInput.show ? "text" : "password"}
                className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                placeholder="Nhập mật khẩu mới"
                autoComplete="new-password"
                maxLength={newPasswordInput.maxLength}
                {...register("newPassword", {
                  onChange: newPasswordInput.handlePasswordChange,
                })}
              />
              <button
                type="button"
                onClick={newPasswordInput.toggle}
                className="absolute right-0 top-1/2 -translate-y-1/2 p-1 hover:text-[#2d5bff]"
                tabIndex={-1}
              >
                {newPasswordInput.show ? (
                  <EyeSlashIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                ) : (
                  <EyeIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                )}
              </button>
            </div>
          </div>
          <div
            className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${errors.confirmPassword ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
          >
            <RequiredLabel
              text="Nhập lại mật khẩu mới"
              error={errors.confirmPassword?.message}
            />
            <div className="flex items-center relative">
              <input
                type={confirmPasswordInput.show ? "text" : "password"}
                className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                placeholder="Nhập lại mật khẩu mới"
                autoComplete="new-password"
                maxLength={confirmPasswordInput.maxLength}
                {...register("confirmPassword", {
                  onChange: confirmPasswordInput.handlePasswordChange,
                })}
              />
              <button
                type="button"
                onClick={confirmPasswordInput.toggle}
                className="absolute right-0 top-1/2 -translate-y-1/2 p-1 hover:text-[#2d5bff]"
                tabIndex={-1}
              >
                {confirmPasswordInput.show ? (
                  <EyeSlashIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                ) : (
                  <EyeIcon className="w-4 h-4 text-[#7C7B7B] hover:text-[#2d5bff]" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-6 flex flex-col gap-2 max-w-full sm:max-w-md">
        <button
          type="submit"
          className="bg-[#2d5bff] text-white rounded-lg px-6 py-3 font-extrabold text-[16px] md:text-[18px] leading-[24px] tracking-wide w-full sm:w-60 self-start hover:bg-[#2046c7] transition disabled:opacity-60 disabled:cursor-not-allowed"
          disabled={loading}
        >
          {loading ? "Đang lưu..." : "Đổi mật khẩu"}
        </button>
        {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
        {success && (
          <div className="text-green-600 text-sm mt-2">
            Đổi mật khẩu thành công!
          </div>
        )}
      </div>
    </form>
  );
};

export { PasswordChangePanel };
