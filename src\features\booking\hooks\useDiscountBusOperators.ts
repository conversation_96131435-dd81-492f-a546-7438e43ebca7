import { useQuery } from "@tanstack/react-query";
import {
  getDiscountBusOperators,
  getDiscountBusOperatorById,
  type GetDiscountBusOperatorsParams,
} from "@/api/strapiApi";
import type {
  StrapiDiscountBusOperatorsResponse,
  StrapiDiscountBusOperator,
  StrapiApiError,
} from "@/shared/types/strapi";

// Query keys for discount bus operators
export const discountBusOperatorsKeys = {
  all: ["discountBusOperators"] as const,
  list: (params?: GetDiscountBusOperatorsParams) =>
    [...discountBusOperatorsKeys.all, "list", params] as const,
  detail: (id: string) =>
    [...discountBusOperatorsKeys.all, "detail", id] as const,
};

/**
 * Hook to fetch discount bus operators from Strapi
 * @param params - Optional parameters including place_id filter
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useDiscountBusOperators = (
  params?: GetDiscountBusOperatorsParams,
  enabled: boolean = true
) => {
  return useQuery<StrapiDiscountBusOperatorsResponse, StrapiApiError>({
    queryKey: discountBusOperatorsKeys.list(params),
    queryFn: () => getDiscountBusOperators(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    enabled,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook to fetch a single discount bus operator by ID from Strapi
 * @param id - The discount bus operator ID to fetch
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useDiscountBusOperatorById = (
  id: string,
  enabled: boolean = true
) => {
  return useQuery<StrapiDiscountBusOperator, StrapiApiError>({
    queryKey: discountBusOperatorsKeys.detail(id),
    queryFn: () => getDiscountBusOperatorById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    enabled: enabled && !!id,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
