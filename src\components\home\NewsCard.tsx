import React, { memo } from "react";
import { BaseCard } from "@/shared/components";

interface NewsCardProps {
  id: string;
  image: string;
  title: string;
  timeAgo: string;
  onHover?: boolean;
  onClick?: () => void;
}

export const NewsCard: React.FC<NewsCardProps> = memo(
  ({ id, image, title, timeAgo, onClick }) => {
    return (
      <BaseCard
        id={id}
        image={image}
        title={title}
        timeAgo={timeAgo}
        onClick={onClick}
        borderColor="EFEFEF"
        titleSize="12px"
        titleLines={2}
      />
    );
  }
);

NewsCard.displayName = "NewsCard";
