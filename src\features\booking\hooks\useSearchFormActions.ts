import { useSearchForm } from "@/contexts/SearchFormContext";

/**
 * Custom hook that provides convenient actions for working with the search form
 * This hook can be used anywhere in the app to interact with the search form state
 */
export const useSearchFormActions = () => {
  const {
    state,
    variant,
    setFromLocation,
    setToLocation,
    setIsRoundTrip,
    setDepartureDate,
    setReturnDate,
    setNumTickets,
    swapLocations,
    resetForm,
    handleSubmit,
    places,
    filteredFromPlaces,
    filteredToPlaces,
    isLoading,
    error,
  } = useSearchForm();

  // Convenience methods for common operations
  const setSearchRoute = (fromLocation: string, toLocation: string) => {
    setFromLocation(fromLocation);
    setToLocation(toLocation);
  };

  const setDates = (departureDate: string, returnDate?: string) => {
    setDepartureDate(departureDate);
    if (returnDate) {
      setReturnDate(returnDate);
      setIsRoundTrip(true);
    }
  };

  const quickSearch = (
    fromLocation: string,
    toLocation: string,
    departureDate: string,
    numTickets: number = 1
  ) => {
    setFromLocation(fromLocation);
    setToLocation(toLocation);
    setDepartureDate(departureDate);
    setNumTickets(numTickets);
  };

  const isFormValid = () => {
    return state.fromLocation && state.toLocation && state.departureDate;
  };

  const getSearchParams = () => {
    const fromPlace = places.find((p) => p.name === state.fromLocation);
    const toPlace = places.find((p) => p.name === state.toLocation);

    if (!fromPlace || !toPlace) return null;

    return {
      fromPlaceId: Number(fromPlace.id),
      toPlaceId: Number(toPlace.id),
      fromPlaceName: state.fromLocation,
      toPlaceName: state.toLocation,
      departureDate: state.departureDate,
      returnDate: state.isRoundTrip ? state.returnDate : undefined,
      numTickets: state.numTickets,
    };
  };

  return {
    // State
    state,
    variant,

    // Basic actions
    setFromLocation,
    setToLocation,
    setIsRoundTrip,
    setDepartureDate,
    setReturnDate,
    setNumTickets,
    swapLocations,
    resetForm,
    handleSubmit,

    // Convenience methods
    setSearchRoute,
    setDates,
    quickSearch,

    // Validation and utilities
    isFormValid,
    getSearchParams,

    // Data
    places,
    filteredFromPlaces,
    filteredToPlaces,

    // Loading states
    isLoading,
    error,
  };
};
