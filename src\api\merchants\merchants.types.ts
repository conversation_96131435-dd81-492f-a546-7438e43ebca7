// Response types

import type { Item } from "./qr.types";

type sizes = {
  small: string;
  medium: string;
  large: string;
  original: string;
  thumbnail: string;
};

export type logo_image = {
  sizes: sizes;
  originalName: string;
  id: string;
};

export interface Merchant {
  contact_phone: string;
  cover_image: string;
  description: string;
  display_order: number;
  id: string;
  is_active: boolean;
  logo_image: logo_image;
  name: string;
  operating_hours_end: string;
  operating_hours_start: string;
  categories: Item[];
}
export type MerchantsResponse = Merchant[];
