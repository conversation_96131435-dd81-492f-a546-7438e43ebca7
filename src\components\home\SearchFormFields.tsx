import React from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";
import {
  LocationCombobox,
  type LocationComboboxRef,
} from "@/shared/components/LocationCombobox";
import {
  cn,
  FORM_INPUT,
  FORM_LABEL,
} from "@/shared/components/FormFieldSearch";
import {
  DatePicker,
  type DatePickerRef,
} from "@/shared/components/date-picker";
import { useSearchForm } from "../../contexts/SearchFormContext";
import { format } from "date-fns";
import { ArrowIcon } from "@/shared";

// interface SearchFormFieldsProps {
//   focusDate?: "departure" | "return"; // Which date picker to auto-focus
// }

export const SearchFormFields = React.memo(() => {
  const { t } = useTranslation();
  const {
    state,
    setFromLocation,
    setToLocation,
    setDepartureDate,
    setReturnDate,
    setNumTickets,
    swapLocations,
    places,
    filteredFromPlaces,
    filteredToPlaces,
    errors,
    touched,
    isLoading,
  } = useSearchForm();

  // Refs để control việc mở các picker/combobox
  const departureDatePickerRef = React.useRef<DatePickerRef | null>(null);
  const returnDatePickerRef = React.useRef<DatePickerRef | null>(null);
  const fromLocationRef = React.useRef<LocationComboboxRef | null>(null);
  const toLocationRef = React.useRef<LocationComboboxRef | null>(null);

  // Auto-open logic
  const handleToLocationAutoOpen = React.useCallback(() => {
    if (!state.fromLocation && fromLocationRef.current) {
      setTimeout(() => {
        fromLocationRef.current?.openDropdown();
      }, 100);
    }
  }, [state.fromLocation]);

  const handleFromLocationAutoOpen = React.useCallback(() => {
    if (!state.toLocation && toLocationRef.current) {
      setTimeout(() => {
        toLocationRef.current?.openDropdown();
      }, 100);
    }
  }, [state.toLocation]);

  const handleDepartureDateChange = React.useCallback(
    (date: Date | undefined) => {
      if (date) {
        const formattedDate = format(date, "yyyy-MM-dd");
        setDepartureDate(formattedDate);

        // Tự động mở return date picker nếu là round trip và chưa có return date
        // if (
        //   state.isRoundTrip &&
        //   !state.returnDate &&
        //   returnDatePickerRef.current
        // ) {
        //   // Delay một chút để đảm bảo departure date đã được set và DOM đã update
        //   setTimeout(() => {
        //     returnDatePickerRef.current?.openCalendar();
        //   }, 100);
        // }
      } else {
        setDepartureDate("");
      }
    },
    [setDepartureDate, state.isRoundTrip, state.returnDate]
  );

  const handleReturnDateChange = React.useCallback(
    (date: Date | undefined) => {
      if (date) {
        const formattedDate = format(date, "yyyy-MM-dd");
        setReturnDate(formattedDate);
      } else {
        setReturnDate("");
      }
    },
    [setReturnDate]
  );

  const handleNumTicketsChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (value === "") {
        setNumTickets(0); // Set to 0 so validation will catch it and input stays empty
      } else {
        const numValue = Number(value);
        setNumTickets(numValue);
      }
    },
    [setNumTickets]
  );

  // State để control focus cho input số vé
  const [ticketsInputFocused, setTicketsInputFocused] = React.useState(false);

  // Convert string dates to Date objects for DatePicker
  const departureDate = state.departureDate
    ? new Date(state.departureDate)
    : undefined;
  const returnDate = state.returnDate ? new Date(state.returnDate) : undefined;

  // Date validation functions
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day for accurate comparison

  // Disable dates before today for departure date
  const isDepartureDateDisabled = React.useCallback(
    (date: Date) => {
      return date < today;
    },
    [today]
  );

  // Disable dates before departure date for return date
  const isReturnDateDisabled = React.useCallback(
    (date: Date) => {
      if (!departureDate) return date < today;
      const departureDateOnly = new Date(departureDate);
      departureDateOnly.setHours(0, 0, 0, 0);
      return date <= departureDateOnly;
    },
    [departureDate, today]
  );

  return (
    <div className="flex flex-col gap-6 px-4 md:px-6 pb-4 md:pb-6">
      {/* Mobile Layout */}
      <div className="flex flex-col gap-6 lg:hidden">
        {/* Hàng 1: Điểm đi + Swap + Điểm đến */}
        <div className="flex items-center gap-2">
          <LocationCombobox
            ref={fromLocationRef}
            places={places}
            value={state.fromLocation}
            onValueChange={setFromLocation}
            placeholder={t("home.form.departurePlaceholder")}
            label={t("home.form.departure")}
            error={touched.fromLocation ? errors.fromLocation : null}
            className="flex-1"
            excludedValue={state.toLocation}
            excludedLabel={t("home.form.selectedAsDestination")}
            onAutoOpen={handleFromLocationAutoOpen}
            disabled={true}
          />

          {/* Swap Icon Mobile */}
          <div className="flex justify-center lg:pt-6">
            <button
              type="button"
              onClick={swapLocations}
              disabled={true}
              className="p-2 rounded-lg bg-gray-200 border border-gray-300 cursor-not-allowed opacity-50 transition-colors"
            >
              <ArrowIcon />
            </button>
          </div>

          <LocationCombobox
            ref={toLocationRef}
            places={places}
            value={state.toLocation}
            onValueChange={setToLocation}
            placeholder={t("home.form.destinationPlaceholder")}
            label={t("home.form.destination")}
            error={touched.toLocation ? errors.toLocation : null}
            className="flex-1"
            excludedValue={state.fromLocation}
            excludedLabel={t("home.form.selectedAsDeparture")}
            onAutoOpen={handleToLocationAutoOpen}
          />
        </div>

        {/* Hàng 2: Ngày đi + Ngày về + Số vé */}
        <div className="flex items-stretch gap-2">
          {/* Ngày đi */}
          <DatePicker
            ref={departureDatePickerRef}
            date={departureDate}
            onDateChange={handleDepartureDateChange}
            placeholder={t("home.form.selectDate")}
            label={t("home.form.departureDate")}
            className="flex-1 min-w-0"
            error={touched.departureDate ? errors.departureDate : null}
            disabled={isDepartureDateDisabled}
            fromDate={today}
            referenceDate={returnDate}
            referenceDateLabel={
              state.isRoundTrip ? t("home.form.returnDate") : undefined
            }
            // initialActiveTab="departure"
          />

          {/* Ngày về (ẩn/hiện nhưng giữ layout) */}
          {state.isRoundTrip && (
            <DatePicker
              ref={returnDatePickerRef}
              date={returnDate}
              onDateChange={handleReturnDateChange}
              placeholder={t("home.form.selectDate")}
              label={t("home.form.returnDate")}
              className="flex-1 min-w-0"
              error={touched.returnDate ? errors.returnDate : null}
              disabled={isReturnDateDisabled}
              fromDate={departureDate || today}
              referenceDate={departureDate}
              referenceDateLabel={t("home.form.departureDate")}
              // initialActiveTab="return"
            />
          )}

          {/* Số vé */}
          <div className="w-20 relative flex-shrink-0">
            <div
              className={cn(
                "h-[50px] pt-[6px] px-[16px] items-center justify-center rounded-md border transition-all",
                touched.numTickets && errors.numTickets
                  ? "bg-red-50 border-red-500"
                  : ticketsInputFocused
                    ? "bg-white border-[#FF7F37] shadow-sm"
                    : "bg-gray-100 border-transparent hover:border-gray-300"
              )}
            >
              <label className={FORM_LABEL}>
                {t("home.form.numberOfTickets")}
              </label>
              <input
                type="number"
                name="numTickets"
                value={state.numTickets === 0 ? "" : state.numTickets}
                onChange={handleNumTicketsChange}
                onFocus={() => setTicketsInputFocused(true)}
                onBlur={() => setTicketsInputFocused(false)}
                className={cn(
                  FORM_INPUT,
                  "font-semibold text-base w-full bg-transparent outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
                  touched.numTickets && errors.numTickets
                    ? "text-red-600"
                    : state.numTickets > 0
                      ? "text-black"
                      : "text-[#747474]"
                )}
              />
            </div>
          </div>
        </div>

        {/* Hàng 3: Submit Button */}
        <div className="w-full">
          <button
            type="submit"
            disabled={isLoading}
            className="w-full h-[50px] bg-[#2D5BFF] hover:bg-[#1E4FFF] disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg font-bold text-base transition-colors duration-200 shadow-md hover:shadow-lg flex items-center justify-center gap-2"
          >
            {isLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            {t("home.form.search")}
          </button>
        </div>
      </div>

      {/* Desktop Layout - Original */}
      <div className="hidden lg:flex lg:flex-row items-stretch lg:items-end gap-8">
        {/* Phần Location - Desktop: Horizontal với swap */}
        <div className="flex flex-row items-end gap-2 flex-1">
          {/* Điểm đi */}
          <LocationCombobox
            ref={fromLocationRef}
            places={filteredFromPlaces}
            value={state.fromLocation}
            onValueChange={setFromLocation}
            placeholder={t("home.form.departurePlaceholder")}
            label={t("home.form.departure")}
            error={touched.fromLocation ? errors.fromLocation : null}
            className="w-full md:min-w-[120px]"
            excludedValue={state.toLocation}
            excludedLabel={t("home.form.selectedAsDestination")}
            onAutoOpen={handleFromLocationAutoOpen}
            disabled={true}
          />

          {/* Swap Icon Desktop */}
          <div className="flex items-end justify-center pb-2">
            <button
              type="button"
              onClick={swapLocations}
              disabled={true}
              className="w-8 h-8 flex items-center justify-center"
            >
              <ArrowIcon />
            </button>
          </div>

          {/* Điểm đến */}
          <LocationCombobox
            ref={toLocationRef}
            places={filteredToPlaces}
            value={state.toLocation}
            onValueChange={setToLocation}
            placeholder={t("home.form.destinationPlaceholder")}
            label={t("home.form.destination")}
            error={touched.toLocation ? errors.toLocation : null}
            className="w-full md:min-w-[120px]"
            excludedValue={state.fromLocation}
            excludedLabel={t("home.form.selectedAsDeparture")}
            onAutoOpen={handleToLocationAutoOpen}
          />
        </div>

        {/* Phần Date và Tickets - Desktop: Continue horizontal */}
        <div className="flex flex-row items-end gap-2">
          {/* Ngày đi */}
          <DatePicker
            ref={departureDatePickerRef}
            date={departureDate}
            onDateChange={handleDepartureDateChange}
            placeholder={t("home.form.selectDate")}
            label={t("home.form.departureDate")}
            className="max-w-[100px] md:w-[120px] flex-shrink-0"
            error={touched.departureDate ? errors.departureDate : null}
            disabled={isDepartureDateDisabled}
            fromDate={today}
            referenceDate={returnDate}
            referenceDateLabel={
              state.isRoundTrip ? t("home.form.returnDate") : undefined
            }
            // initialActiveTab="departure"
          />

          {/* Ngày về (ẩn/hiện nhưng giữ layout) */}
          {state.isRoundTrip && (
            <DatePicker
              ref={returnDatePickerRef}
              date={returnDate}
              onDateChange={handleReturnDateChange}
              placeholder={t("home.form.selectDate")}
              label={t("home.form.returnDate")}
              className="max-w-[100px] md:w-[120px] flex-shrink-0"
              error={touched.returnDate ? errors.returnDate : null}
              disabled={isReturnDateDisabled}
              fromDate={departureDate || today}
              referenceDate={departureDate}
              referenceDateLabel={t("home.form.departureDate")}
              // initialActiveTab="return"
            />
          )}

          {/* Số vé */}
          <div className="w-20 relative flex-shrink-0">
            <div
              className={cn(
                "h-[50px] pt-[6px] px-[16px] rounded-md border transition-all",
                touched.numTickets && errors.numTickets
                  ? "bg-red-50 border-red-500"
                  : ticketsInputFocused
                    ? "bg-white border-[#FF7F37] shadow-sm"
                    : "bg-gray-100 border-transparent hover:border-gray-300"
              )}
            >
              <label className={FORM_LABEL}>
                {t("home.form.numberOfTickets")}
              </label>
              <input
                type="number"
                name="numTickets"
                value={state.numTickets === 0 ? "" : state.numTickets}
                onChange={handleNumTicketsChange}
                onFocus={() => setTicketsInputFocused(true)}
                onBlur={() => setTicketsInputFocused(false)}
                className={cn(
                  FORM_INPUT,
                  "font-semibold text-base w-full bg-transparent outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
                  touched.numTickets && errors.numTickets
                    ? "text-red-600"
                    : state.numTickets > 0
                      ? "text-black"
                      : "text-[#747474]"
                )}
              />
            </div>
          </div>

          {/* Submit Button Desktop */}
          <div className="w-auto">
            <button
              type="submit"
              disabled={isLoading}
              className="w-[160px] xl:w-[193px] h-[50px] bg-[#2D5BFF] hover:bg-[#1E4FFF] disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg font-bold text-base transition-colors duration-200 shadow-md hover:shadow-lg flex items-center justify-center gap-2"
            >
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              {t("home.form.search")}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});
