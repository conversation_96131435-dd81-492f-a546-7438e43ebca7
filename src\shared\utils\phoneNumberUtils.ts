// Vietnamese phone number utility functions

/**
 * Normalizes a phone number by removing spaces, dashes, parentheses, etc.
 */
export function normalizePhoneNumber(phoneNumber: string): string {
  return phoneNumber.replace(/[\s\-\(\)\+]/g, "");
}

/**
 * Converts various Vietnamese phone number formats to a standard format
 * @param phoneNumber - Input phone number
 * @param outputFormat - 'international' (+84) or 'local' (0)
 * @returns Formatted phone number or original if invalid
 */
export function formatVietnamesePhoneNumber(
  phoneNumber: string,
  outputFormat: "international" | "local" = "local"
): string {
  const normalized = normalizePhoneNumber(phoneNumber);

  // Handle international format (+84)
  if (normalized.startsWith("84") && normalized.length === 11) {
    const mainNumber = normalized.substring(2); // Remove '84'
    return outputFormat === "international"
      ? `+84${mainNumber}`
      : `0${mainNumber}`;
  }

  // Handle local format with leading 0
  if (normalized.startsWith("0") && normalized.length === 10) {
    const mainNumber = normalized.substring(1); // Remove '0'
    return outputFormat === "international"
      ? `+84${mainNumber}`
      : `0${mainNumber}`;
  }

  // Handle direct 9-digit number (add appropriate prefix)
  if (normalized.length === 9 && /^[0-9]+$/.test(normalized)) {
    return outputFormat === "international"
      ? `+84${normalized}`
      : `0${normalized}`;
  }

  // Return original if doesn't match any pattern
  return phoneNumber;
}

/**
 * Validates Vietnamese phone number
 * Accepts formats: +84xxxxxxxxx, 84xxxxxxxxx, 0xxxxxxxxx, xxxxxxxxx
 * Always returns normalized number in international format (+84)
 */
export function validateVietnamesePhoneNumber(phoneNumber: string): {
  isValid: boolean;
  errorMessage?: string;
  normalizedNumber?: string;
} {
  if (!phoneNumber) {
    return {
      isValid: false,
      errorMessage: "Vui lòng nhập số điện thoại",
    };
  }

  const normalized = normalizePhoneNumber(phoneNumber);

  // Valid Vietnamese mobile prefixes (updated based on current networks)
  const validPrefixes = [
    "032",
    "033",
    "034",
    "035",
    "036",
    "037",
    "038",
    "039", // Viettel
    "070",
    "076",
    "077",
    "078",
    "079", // Mobifone
    "081",
    "082",
    "083",
    "084",
    "085",
    "088",
    "091",
    "094", // Vinaphone
    "052",
    "055",
    "056",
    "058",
    "092", // Vietnamobile & Wintel
    "059",
    "099", // Gmobile
    "089",
    "090",
    "093",
    "086",
    "087",
    "096",
    "097",
    "098", // Others
  ];

  // Check for international format (+84 or 84)
  if (normalized.startsWith("84")) {
    if (normalized.length === 11 && /^84[0-9]{9}$/.test(normalized)) {
      // Check if the number after 84 has a valid Vietnamese prefix
      const phoneAfter84 = normalized.substring(2); // Remove '84'
      const prefix = phoneAfter84.substring(0, 3); // Get first 3 digits after 84 (full prefix)

      // Check if this 3-digit prefix is in our valid prefixes list
      if (validPrefixes.includes(prefix)) {
        return {
          isValid: true,
          normalizedNumber: `+84${phoneAfter84}`, // Always return +84 format
        };
      } else {
        return {
          isValid: false,
          errorMessage: "Số điện thoại không đúng định dạng nhà mạng Việt Nam",
        };
      }
    } else {
      return {
        isValid: false,
        errorMessage: "Số điện thoại quốc tế phải có định dạng +84xxxxxxxxx",
      };
    }
  }

  // Check for local format with leading 0
  if (normalized.startsWith("0")) {
    if (normalized.length === 10 && /^0[0-9]{9}$/.test(normalized)) {
      // Additional check for valid Vietnamese mobile prefixes
      const prefix = normalized.substring(0, 3);

      if (validPrefixes.includes(prefix)) {
        return {
          isValid: true,
          normalizedNumber: `+84${normalized.substring(1)}`, // Convert 0xxx to +84xxx
        };
      } else {
        return {
          isValid: false,
          errorMessage: "Số điện thoại không đúng định dạng nhà mạng Việt Nam",
        };
      }
    } else {
      return {
        isValid: false,
        errorMessage: "Số điện thoại chưa đúng định dạng",
      };
    }
  }

  // Check for 9-digit number (without prefix)
  if (normalized.length === 9 && /^[0-9]{9}$/.test(normalized)) {
    // Convert to local format first, then validate and return international format
    const withPrefix = `0${normalized}`;
    const validation = validateVietnamesePhoneNumber(withPrefix);
    return validation;
  }

  return {
    isValid: false,
    errorMessage: "Số điện thoại không hợp lệ.",
  };
}

/**
 * Formats phone number for display (adds spaces for readability)
 */
export function formatPhoneNumberForDisplay(phoneNumber: string): string {
  const normalized = normalizePhoneNumber(phoneNumber);

  if (normalized.startsWith("84") && normalized.length === 11) {
    return `+84 ${normalized.substring(2, 5)} ${normalized.substring(5, 8)} ${normalized.substring(8)}`;
  }

  if (normalized.startsWith("0") && normalized.length === 10) {
    return `${normalized.substring(0, 4)} ${normalized.substring(4, 7)} ${normalized.substring(7)}`;
  }

  return phoneNumber;
}
