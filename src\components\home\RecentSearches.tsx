import React, { useCallback } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useNavigate } from "@tanstack/react-router";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { useRecentSearches } from "@/hooks/useRecentSearches";
import { useSearchForm } from "@/contexts/SearchFormContext";
import { convertToISODate } from "@/lib/dateUtils";

interface RecentSearchesProps {
  onSearchSelect?: () => void;
}

export const RecentSearches: React.FC<RecentSearchesProps> = React.memo(
  ({ onSearchSelect }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { recentSearches, removeSearch } = useRecentSearches();
    const {
      setFromLocation,
      setToLocation,
      setDepartureDate,
      setReturnDate,
      setNumTickets,
      setIsRoundTrip,
      setErrors,
    } = useSearchForm();

    // Format date for display
    const formatDisplayDate = (dateString: string) => {
      try {
        const date = new Date(convertToISODate(dateString));
        return date.toLocaleDateString("vi-VN", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        });
      } catch {
        return dateString;
      }
    };

    // Handle search selection
    const handleSearchSelect = useCallback(
      (search: any) => {
        // Clear all errors first
        setErrors({});

        // Fill the form
        setFromLocation(search.fromLocation);
        setToLocation(search.toLocation);
        setDepartureDate(search.departureDate);
        setNumTickets(search.numTickets);
        setIsRoundTrip(search.isRoundTrip);

        if (search.returnDate) {
          setReturnDate(search.returnDate);
        } else {
          setReturnDate("");
        }

        // Navigate to search page with the saved parameters
        const searchParams: any = {
          fromPlaceId: Number(search.fromPlaceId),
          toPlaceId: Number(search.toPlaceId),
          fromPlaceName: search.fromLocation,
          toPlaceName: search.toLocation,
        };

        if (search.departureDate) {
          searchParams.departureDate = search.departureDate;
        }

        if (search.numTickets) {
          searchParams.numTickets = Number(search.numTickets);
        }

        // Include round trip information
        if (search.isRoundTrip) {
          searchParams.isRoundTrip = true;
          if (search.returnDate) {
            searchParams.returnDate = search.returnDate;
          }
        }

        // Call callback if provided
        if (onSearchSelect) {
          onSearchSelect();
        }

        // Navigate to search page
        navigate({ to: "/booking/search", search: searchParams });
      },
      [
        setFromLocation,
        setToLocation,
        setDepartureDate,
        setReturnDate,
        setNumTickets,
        setIsRoundTrip,
        setErrors,
        navigate,
        onSearchSelect,
      ]
    );

    // Handle remove search
    const handleRemoveSearch = useCallback(
      (e: React.MouseEvent, id: string) => {
        e.stopPropagation();
        removeSearch(id);
      },
      [removeSearch]
    );

    return (
      <div className="space-y-2 px-4 md:px-6 mb-4">
        <hr className="my-4 mx-4 md:mx-6" />
        <p className="font-bold text-base text-[#5C5C5C]">Tìm kiếm gần đây</p>

        <div className="flex flex-wrap gap-3">
          {recentSearches.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-sm text-[#5C5C5C]">
                Chưa có tìm kiếm nào gần đây
              </p>
            </div>
          ) : (
            recentSearches.map((search) => (
              <div key={search.id} className="relative group">
                <button
                  type="button"
                  onClick={() => handleSearchSelect(search)}
                  className="rounded-md flex cursor-pointer items-start flex-col border py-1.5 px-4 border-[#D7D7D7] gap-y-1 text-xs transition-all duration-200 hover:border-[#1E4FFF] hover:bg-[#F0F4FF] hover:shadow-md pr-8"
                >
                  <p className="font-bold text-[#181818]">
                    {search.fromLocation} - {search.toLocation}
                  </p>
                  <p className="text-[#5C5C5C]">
                    {formatDisplayDate(search.departureDate)}
                    {search.returnDate &&
                      ` - ${formatDisplayDate(search.returnDate)}`}
                  </p>
                  {search.numTickets > 1 && (
                    <p className="text-[#5C5C5C]">
                      {search.numTickets} {t("home.form.tickets")}
                    </p>
                  )}
                </button>

                {/* Remove button */}
                <button
                  type="button"
                  onClick={(e) => handleRemoveSearch(e, search.id)}
                  className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-0.5 rounded-full hover:bg-red-100 text-[#5C5C5C] hover:text-red-500"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </div>
            ))
          )}
        </div>
      </div>
    );
  }
);
