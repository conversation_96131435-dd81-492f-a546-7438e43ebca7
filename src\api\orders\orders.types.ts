// Request types
export interface CreateOrderTicket {
  bus_schedule_id: string;
  dropoff_id: string;
  departure_date: string; // YYYY-MM-DD format
  quantity: number;
  ticket_type?: string; // Optional ticket type like "One Way" or "Return"
}

export interface CreateOrderRequest {
  customer_name: string;
  customer_phone: string;
  customer_email?: string; // Optional email field
  tickets: CreateOrderTicket[];
}

// Location/Place types
export interface Place {
  id: number;
  parentId: number;
  type: string;
  name: string;
  tags: string;
  level: number;
  code: string;
}

// Fleet and Bus types
export interface Fleet {
  id: string;
  name: string;
  description: string;
  policy: string;
  created_at: string;
  updated_at: string;
}

export interface Bus {
  id: string;
  license_plate: string;
  brand: string;
  model: string;
  bus_type: string;
  seats: number;
  ticket_price: string;
  description: string;
  images: string | null;
  is_active: boolean;
  fleet_id: string;
  created_at: string;
  updated_at: string;
  fleet: Fleet;
}

// Bus Schedule types
export interface BusSchedule {
  id: string;
  bus_id: string;
  departure_place_id: number;
  departure_time: string;
  day_of_week: number;
  created_at: string;
  updated_at: string;
  bus: Bus;
  departure_place: Place;
}

// Bus Dropoff types
export interface BusDropoff {
  id: string;
  bus_schedule_id: string;
  arrival_place_id: number;
  location_name: string;
  duration_minutes: number;
  ticket_price: string;
  dropoff_order: number;
  created_at: string;
  updated_at: string;
  arrival_place: Place;
}

// Enhanced ticket type with all API data
export interface OrderTicket {
  id: string;
  ticket_number: string;
  order_id: string;
  bus_schedule_id: string;
  dropoff_id: string;
  price: string;
  ticket_type: string;
  departure_date: string; // YYYY-MM-DD
  departure_time: string; // HH:MM:SS
  created_at: string;
  updated_at: string;
  busSchedule: BusSchedule;
  busDropoff: BusDropoff;
  arrival_time: string; // HH:MM:SS
  arrival_date: string; // YYYY-MM-DD
}

export interface Order {
  id: string; // Order ID
  customer_name: string;
  customer_phone: string;
  customer_email?: string; // Optional email field
  total_amount: string;
  status: string;
  created_at: string;
  updated_at: string;
  tickets: OrderTicket[];
}

export interface CreateOrderResponse extends Order {}

export interface GetOrderResponse extends Order {}

export interface OrdersResponse {
  orders: Order[];
  total: number;
  page: number;
  limit: number;
}
