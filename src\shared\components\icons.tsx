export const OrdersIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M18 4L6 4V20H18V4ZM9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9H11C11.5523 9 12 8.55228 12 8C12 7.44772 11.5523 7 11 7H9ZM8 12C8 11.4477 8.44772 11 9 11H13C13.5523 11 14 11.4477 14 12C14 12.5523 13.5523 13 13 13H9C8.44772 13 8 12.5523 8 12ZM9 15C8.44772 15 8 15.4477 8 16C8 16.5523 8.44772 17 9 17H15C15.5523 17 16 16.5523 16 16C16 15.4477 15.5523 15 15 15H9Z"
        fill="#2D5BFF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4 4C4 2.89543 4.89543 2 6 2H18C19.1046 2 20 2.89543 20 4V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V4ZM18 4L6 4V20H18V4Z"
        fill="#2D5BFF"
      />
    </svg>
  );
};

export const PackIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M2 3C2 2.44772 2.45267 2 3.01108 2H5.1674C6.17373 2 7.02692 2.73186 7.16923 3.71716L7.49897 6H19.9772C21.2532 6 22.2103 7.15465 21.96 8.39223L20.3423 16.3922C20.1533 17.3271 19.3234 18 18.3594 18H8.94336C7.93704 18 7.08385 17.2681 6.94153 16.2828L5.62115 7.14142L5.1674 4H3.01108C2.45267 4 2 3.55228 2 3ZM7.78785 8L8.94336 16L18.3594 16L19.9772 8H7.78785ZM10.0886 20.5C10.0886 21.3284 9.40961 22 8.57201 22C7.7344 22 7.05539 21.3284 7.05539 20.5C7.05539 19.6716 7.7344 19 8.57201 19C9.40961 19 10.0886 19.6716 10.0886 20.5ZM18.6828 22C19.5204 22 20.1994 21.3284 20.1994 20.5C20.1994 19.6716 19.5204 19 18.6828 19C17.8452 19 17.1662 19.6716 17.1662 20.5C17.1662 21.3284 17.8452 22 18.6828 22Z"
        fill="white"
      />
    </svg>
  );
};

export const YoutubeIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M23 7.8c0-1.3-1-2.4-2.4-2.4C18.4 5 12 5 12 5s-6.4 0-8.6.4C2 5.4 1 6.5 1 7.8v8.4c0 1.3 1 2.4 2.4 2.4C5.6 19 12 19 12 19s6.4 0 8.6-.4c1.4 0 2.4-1.1 2.4-2.4V7.8z"
        fill="#7C7B7B"
      />
      <path d="M9 15l6-3-6-3v6z" fill="white" />
    </svg>
  );
};

export const FacebookIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.8c-.16 1.56-.86 5.24-1.16 6.96-.14.72-.4 1-.66 1.02-.56.05-1-.38-1.54-.74-.84-.56-1.32-.92-2.14-1.48-.94-.64-.34-1 .2-1.6.14-.16 2.54-2.32 2.58-2.52.01-.04.01-.18-.07-.26-.08-.08-.2-.05-.28-.03-.12.03-2.16 1.38-6.1 4.04-.58.4-1.1.6-1.56.58-.52-.02-1.52-.3-2.26-.54-.9-.3-1.62-.46-1.56-.98.04-.28.38-.56 1.02-.86 4-1.74 6.68-2.88 8.02-3.44 3.82-1.58 4.62-1.86 5.14-1.86.12 0 .38.02.56.16.14.12.18.28.2.4-.02.1-.02.24-.04.42z"
        fill="#7C7B7B"
      />
    </svg>
  );
};

export const TikTokIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M16.6 5.82s.51.5 0 0A4.278 4.278 0 015.6 7v8.5a3.5 3.5 0 003.5 3.5 3.5 3.5 0 003.5-3.5 3.5 3.5 0 00-3.5-3.5c-.36 0-.7.07-1.01.18V9.97A6.475 6.475 0 019.1 9.5a6.5 6.5 0 006.5 6.5 6.5 6.5 0 006.5-6.5V7.87a8.72 8.72 0 003.9.89v-3a5.787 5.787 0 01-4.9-4.94h-4z"
        fill="#747474"
      />
    </svg>
  );
};

export const CalendarIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.10959 1.77778C6.10959 1.34822 5.7784 1 5.36986 1C4.96132 1 4.63014 1.34822 4.63014 1.77778V2.38272H3.72603C2.77277 2.38272 2 3.19524 2 4.19753V7.30864V13.1852C2 14.1875 2.77277 15 3.72603 15H12.274C13.2272 15 14 14.1875 14 13.1852V7.30864V4.19753C14 3.19524 13.2272 2.38272 12.274 2.38272H11.3699V1.77778C11.3699 1.34822 11.0387 1 10.6301 1C10.2216 1 9.89041 1.34822 9.89041 1.77778V2.38272H6.10959V1.77778ZM12.5205 6.53086V4.19753C12.5205 4.05435 12.4102 3.93827 12.274 3.93827H11.3699V4.54321C11.3699 4.97276 11.0387 5.32099 10.6301 5.32099C10.2216 5.32099 9.89041 4.97276 9.89041 4.54321V3.93827H6.10959V4.54321C6.10959 4.97276 5.7784 5.32099 5.36986 5.32099C4.96132 5.32099 4.63014 4.97276 4.63014 4.54321V3.93827H3.72603C3.58985 3.93827 3.47945 4.05435 3.47945 4.19753V6.53086H12.5205ZM3.47945 8.08642H12.5205V13.1852C12.5205 13.3284 12.4102 13.4444 12.274 13.4444H3.72603C3.58985 13.4444 3.47945 13.3284 3.47945 13.1852V8.08642Z"
        fill="#7C7B7B"
      />
    </svg>
  );
};

export const ArrowIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8 2.575C5.00385 2.575 2.575 5.00385 2.575 8C2.575 10.9961 5.00385 13.425 8 13.425C10.9961 13.425 13.425 10.9961 13.425 8C13.425 5.00385 10.9961 2.575 8 2.575ZM8.44815 10.6589C8.13949 10.3525 8.13768 9.85384 8.44409 9.54519L9.16545 8.81855H5.201C4.76608 8.81855 4.41351 8.46597 4.41351 8.03105C4.41351 7.59613 4.76608 7.24355 5.201 7.24355H9.2271L8.44409 6.45481C8.13768 6.14616 8.13949 5.64754 8.44815 5.34113C8.75681 5.03471 9.25542 5.03653 9.56184 5.34519L10.9122 6.70544C11.6235 7.42193 11.6235 8.57808 10.9122 9.29457L9.56184 10.6548C9.25542 10.9635 8.75681 10.9653 8.44815 10.6589Z"
        fill="#2D5BFF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8 2.575C5.00385 2.575 2.575 5.00385 2.575 8C2.575 10.9961 5.00385 13.425 8 13.425C10.9961 13.425 13.425 10.9961 13.425 8C13.425 5.00385 10.9961 2.575 8 2.575ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1Z"
        fill="#2D5BFF"
      />
    </svg>
  );
};

export const ChangeTripIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="12"
    viewBox="0 0 14 12"
    fill="none"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.4019 9.69451C9.68191 11.6528 7.72655 12.0445 6.23576 11.9962C5.23112 11.9637 4.27666 11.8491 3.29778 11.4543C2.31974 11.0599 1.3734 10.4089 0.334833 9.38084C0.0373214 9.08633 0.0471137 8.61815 0.356705 8.33513C0.666296 8.05211 1.15845 8.06142 1.45596 8.35594C2.39457 9.28508 3.17294 9.7974 3.90466 10.0925C4.63554 10.3872 5.37591 10.4883 6.28865 10.5179C7.31364 10.5511 8.83875 10.3283 11.1544 8.73294L10.28 8.73239C9.83889 8.73211 9.48157 8.41006 9.48188 8.01306C9.48218 7.61606 9.84 7.29445 10.2811 7.29472L12.1365 7.29588C13.1646 7.29653 13.998 8.04656 13.9987 8.97198L14 10.6493C14.0003 11.0463 13.643 11.3684 13.2019 11.3686C12.7608 11.3689 12.403 11.0473 12.4027 10.6503L12.4019 9.69451ZM1.59731 1.34969C1.597 0.952686 1.23918 0.631081 0.798093 0.63136C0.357008 0.63164 -0.000310244 0.953699 2.02138e-07 1.3507L0.00131281 3.02802C0.0020369 3.95344 0.835366 4.70347 1.86355 4.70412L3.71891 4.70528C4.16 4.70555 4.51782 4.38394 4.51812 3.98694C4.51843 3.58994 4.16111 3.26789 3.72002 3.26761L2.84557 3.26706C5.16125 1.67172 6.68636 1.44891 7.71135 1.4821C8.62409 1.51165 9.36446 1.61277 10.0953 1.90752C10.8271 2.2026 11.6054 2.71492 12.544 3.64406C12.8416 3.93857 13.3337 3.94789 13.6433 3.66487C13.9529 3.38185 13.9627 2.91367 13.6652 2.61916C12.6266 1.59107 11.6803 0.940104 10.7022 0.545685C9.72334 0.150928 8.76888 0.036334 7.76424 0.00380325C6.27345 -0.0444679 4.31809 0.347151 1.59806 2.30549L1.59731 1.34969Z"
      fill="#2D5BFF"
    />
  </svg>
);

export const CodeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="12"
    viewBox="0 0 14 12"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.333008 0H0.999674V12H0.333008V0ZM4.33301 0H3.66634V12H4.33301V0ZM8.99967 0H10.333V12H8.99967V0ZM10.9997 0H13.6663V12H10.9997V0ZM8.33301 0H7.66634V12H8.33301V0ZM1.66634 0H2.99967V12H1.66634V0ZM6.99967 0H4.99967V12H6.99967V0Z"
      fill="#7C7B7B"
    />
  </svg>
);
