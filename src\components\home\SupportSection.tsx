import React, { memo, useState, useCallback } from "react";
import { PhoneIcon, TicketIcon } from "@heroicons/react/24/solid";
import { FeatureItem } from "./FeatureItem";
import { Skeleton } from "@/shared/components/skeleton";
import { Container } from "@/shared/components/Container";

export const SupportSection: React.FC = memo(() => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(true);
  }, []);

  return (
    <section className="w-full py-[60px] lg:py-[100px] bg-white">
      <Container>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Left Content */}
          <div className="order-2 lg:order-1">
            <div className="space-y-8">
              <div className="space-y-3">
                <h2 className="text-[#181818] font-mulish font-extrabold lg:text-[40px] text-[20px] lg:text-[40px] xl:text-[48px] leading-[1.2]">
                  Bạn cần vé xe?
                  <br />
                  Đội ngũ hỗ trợ sẵn sàng giúp bạn!
                </h2>
                <p className="text-[#5C5C5C] font-mulish font-bold text-base leading-[1.25]">
                  Đặt vé online dễ dàng, nhận vé ngay tại nhà! Khám phá trải
                  nghiệm đặt vé thông minh với hệ thống hiện đại nhất.
                </p>
              </div>

              <div className="space-y-8">
                <FeatureItem
                  icon={
                    <PhoneIcon
                      className="w-full h-full text-white"
                      strokeWidth={2}
                    />
                  }
                  title="Hỗ trợ nhanh chóng"
                  description="Nhận tư vấn nhanh chóng, cập nhật ưu đãi độc quyền và thông tin vé mọi lúc."
                  bgColor="#2D5BFF"
                />

                <FeatureItem
                  icon={
                    <TicketIcon
                      className="w-full h-full text-white"
                      strokeWidth={2}
                    />
                  }
                  title="Đặt vé trực tuyến"
                  description="Tiện lợi 24/7, chọn ghế nhanh, giá rẻ hơn, thanh toán dễ dàng với nhiều phương thức."
                  bgColor="#FF7F37"
                />
              </div>
            </div>
          </div>

          {/* Right Content - Image with Pattern */}
          <div className="relative lg:justify-self-end order-1 lg:order-2 hidden lg:flex justify-center lg:justify-end">
            {/* Background Pattern */}
            <div className="absolute -right-4 top-8 w-32 h-32 lg:w-48 lg:h-48 opacity-20">
              <svg viewBox="0 0 192 191" fill="none" className="w-full h-full">
                <defs>
                  <pattern
                    id="dots"
                    patternUnits="userSpaceOnUse"
                    width="20"
                    height="20"
                  >
                    <circle cx="10" cy="10" r="2" fill="#D8DBEA" />
                  </pattern>
                </defs>
                <rect width="192" height="191" fill="url(#dots)" />
              </svg>
            </div>

            {/* Main Image Container */}
            <div className="relative">
              <div
                className="w-[300px] h-[360px] lg:w-[400px] lg:h-[480px] xl:w-[503px] xl:h-[600px] rounded-[10px_60px_10px_60px] lg:rounded-[10px_110px_10px_110px] overflow-hidden bg-gray-100"
                style={{
                  background:
                    "linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%)",
                }}
              >
                {!imageLoaded && (
                  <Skeleton className="w-full h-full rounded-[10px_60px_10px_60px] lg:rounded-[10px_110px_10px_110px]" />
                )}
                <img
                  src={
                    imageError ? "/placeholder-image.jpg" : "/assets/image.svg"
                  }
                  alt="Support representative"
                  className={`w-full h-full object-cover -translate-x-[30px] lg:-translate-x-[50px] transition-opacity duration-200 ${
                    imageLoaded ? "opacity-100" : "opacity-0"
                  }`}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
});

SupportSection.displayName = "SupportSection";
