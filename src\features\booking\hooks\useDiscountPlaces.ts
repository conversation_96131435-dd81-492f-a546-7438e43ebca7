import { useQuery } from "@tanstack/react-query";
import { getDiscountPlaces } from "@/api/strapiApi";
import type {
  StrapiDiscountPlacesResponse,
  StrapiApiError,
} from "@/shared/types/strapi";

// Query keys for discount places
export const discountPlacesKeys = {
  all: ["discountPlaces"] as const,
  list: () => [...discountPlacesKeys.all, "list"] as const,
};

/**
 * Hook to fetch discount places from Strapi
 */
export const useDiscountPlaces = () => {
  return useQuery<StrapiDiscountPlacesResponse, StrapiApiError>({
    queryKey: discountPlacesKeys.list(),
    queryFn: () => getDiscountPlaces(),
    staleTime: 10 * 60 * 1000, // 10 minutes (places don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
