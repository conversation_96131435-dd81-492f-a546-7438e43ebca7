{"name": "sieu-booking-app", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "sync-translations": "node scripts/sync-translations.js", "prepare": "husky"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "@tanstack/react-router": "^1.114.3", "@tanstack/react-router-devtools": "^1.114.3", "@tanstack/react-store": "^0.7.1", "@tanstack/router-plugin": "^1.114.3", "@types/file-saver": "^2.0.7", "@types/jszip": "^3.4.1", "@types/qrcode": "^1.5.5", "@types/qrcode.react": "^1.0.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "react-qr-code": "^2.0.15", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/html2canvas": "^1.0.0", "@types/node": "^22.15.23", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "husky": "^9.1.7", "jsdom": "^26.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.41.1"}, "overrides": {"vite": {"rollup": "npm:@rollup/wasm-node"}}}