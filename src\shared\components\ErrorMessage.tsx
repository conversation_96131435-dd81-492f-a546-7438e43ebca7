import React from "react";

interface ErrorMessageProps {
  message: string;
  onRetry: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = React.memo(
  ({ message, onRetry }) => (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#F4F5F7] px-4">
      <p className="text-red-500 text-center mb-4">{message}</p>
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-[#2D5BFF] text-white rounded-lg"
      >
        Thử lại
      </button>
    </div>
  )
);
