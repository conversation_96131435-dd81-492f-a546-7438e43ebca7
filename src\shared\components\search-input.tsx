import React, { forwardRef } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

export interface SearchInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  required?: boolean;
}

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  ({ className, error, label, required, ...props }, ref) => {
    return (
      <div className="relative">
        <div className="relative flex items-center">
          <span className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400">
            <MagnifyingGlassIcon className="w-5 h-5" />
          </span>
          <input
            ref={ref}
            className={cn(
              "w-full py-2 pl-11 pr-4 bg-white rounded-full border border-[#FF7F37] placeholder-gray-400 text-sm",
              "focus:outline-none focus:ring-2 focus:ring-[#FF7F37] focus:border-[#FF7F37]",
              "disabled:cursor-not-allowed disabled:opacity-50",
              error ? "border-red-500 focus:ring-red-500" : "border-[#FF7F37]",
              className
            )}
            {...props}
          />
        </div>
      </div>
    );
  }
);

SearchInput.displayName = "SearchInput";
