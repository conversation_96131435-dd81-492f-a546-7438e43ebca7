import { Store } from "@tanstack/react-store";
import type { CartItem } from "@/features/merchants/types";

export interface SimpleCartStore {
  items: Record<string, CartItem>;
}

export const simpleCartStore = new Store<SimpleCartStore>({
  items: {},
});

export const simpleCartActions = {
  addItem: (item: CartItem) => {
    simpleCartStore.setState((state) => {
      const existing = state.items[item.id];
      const quantity = (existing?.quantity || 0) + (item.quantity || 1);
      return {
        ...state,
        items: {
          ...state.items,
          [item.id]: { ...item, quantity },
        },
      };
    });
  },

  updateItemQuantity: (itemId: string, quantity: number) => {
    simpleCartStore.setState((state) => {
      if (!state.items[itemId]) return state;
      if (quantity <= 0) {
        const { [itemId]: _, ...rest } = state.items;
        return { ...state, items: rest };
      }
      return {
        ...state,
        items: {
          ...state.items,
          [itemId]: { ...state.items[itemId], quantity },
        },
      };
    });
  },

  removeItem: (itemId: string) => {
    simpleCartStore.setState((state) => {
      if (!state.items[itemId]) return state;
      const { [itemId]: _, ...rest } = state.items;
      return { ...state, items: rest };
    });
  },

  clearCart: () => {
    simpleCartStore.setState((state) => ({ ...state, items: {} }));
  },
};

export const simpleCartSelectors = {
  getQuantity: (itemId: string) => {
    return simpleCartStore.state.items[itemId]?.quantity || 0;
  },
  getAllItems: () => {
    return Object.values(simpleCartStore.state.items);
  },
};
