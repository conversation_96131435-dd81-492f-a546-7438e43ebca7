import React from "react";
import { clsx } from "clsx";
import type { RadioOption } from "@/shared/types/bus";

interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  options,
  value,
  onChange,
  className,
}) => {
  return (
    <div className={clsx("space-y-0", className)}>
      {options.map((option) => (
        <label
          key={option.value}
          className="flex items-center gap-4 px-4 py-[14px] cursor-pointer select-none hover:bg-gray-50 transition-colors"
        >
          <div className="relative">
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(e.target.value)}
              className="sr-only"
            />
            <div
              className={clsx(
                "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors",
                value === option.value
                  ? "border-[#2D5BFF] bg-white"
                  : "border-gray-350 bg-white"
              )}
            >
              {value === option.value && (
                <div className="w-3 h-3 rounded-full bg-[#2D5BFF]"></div>
              )}
            </div>
          </div>
          <span
            className={clsx(
              "text-base font-normal",
              value === option.value ? "text-[#2D5BFF]" : "text-gray-800"
            )}
          >
            {option.label}
            {option.count !== undefined && (
              <span className="text-gray-500 ml-1">({option.count})</span>
            )}
          </span>
        </label>
      ))}
    </div>
  );
};

export default RadioGroup;
