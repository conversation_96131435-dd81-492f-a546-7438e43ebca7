import { Badge } from "@/shared/components/badge";

const statusMap: Record<string, string> = {
  pending: "Chờ xác nhận",
  confirmed: "Đ<PERSON> xác nhận",
  cancelled: "Đ<PERSON> hủy",
  completed: "<PERSON><PERSON><PERSON> thành",
  processing: "<PERSON><PERSON> xử lý",
  failed: "<PERSON>h<PERSON><PERSON> bại",
  paid: "Đã thanh toán",
};

const statusColor: Record<string, string> = {
  pending: "bg-yellow-100 text-yellow-800",
  paid: "bg-green-100 text-green-800",
  cancelled: "bg-red-100 text-red-800",
  completed: "bg-blue-100 text-blue-800",
  processing: "bg-purple-100 text-purple-800",
  failed: "bg-gray-100 text-gray-800",
};

export const OrderStatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const key = status?.toLowerCase?.() || "";
  return (
    <Badge
      className={
        `${statusColor[key]} sm:w-full max-w-[160px] justify-center py-2 font-bold text-[14px]` ||
        "bg-gray-100 text-gray-800"
      }
      variant="secondary"
    >
      {statusMap[key] || status}
    </Badge>
  );
};
