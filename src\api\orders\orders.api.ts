import axiosInstance from "@/shared/api/axiosInstance";
import {
  CREATE_ORDER_API_ENDPOINT,
  GET_ORDER_BY_ID_API_ENDPOINT,
  GET_ALL_ORDERS_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";
import type {
  CreateOrderRequest,
  CreateOrderResponse,
  GetOrderResponse,
  Order,
} from "./orders.types";

// POST /api/v1/orders - Create a new order
const createOrder = async (
  orderData: CreateOrderRequest
): Promise<CreateOrderResponse> => {
  const response = await axiosInstance.post(
    CREATE_ORDER_API_ENDPOINT,
    orderData
  );
  return response.data;
};

// GET /api/v1/orders/:id - Get order by ID
const getOrderById = async (orderId: string): Promise<GetOrderResponse> => {
  const response = await axiosInstance.get(
    `${GET_ORDER_BY_ID_API_ENDPOINT}/${orderId}`
  );
  return response.data;
};

// GET /api/v1/orders - Get all orders with optional pagination
const getAllOrders = async (params?: {
  page?: number;
  limit?: number;
}): Promise<Order[]> => {
  const response = await axiosInstance.get(GET_ALL_ORDERS_API_ENDPOINT, {
    params,
  });
  return response.data;
};

export { createOrder, getOrderById, getAllOrders };
