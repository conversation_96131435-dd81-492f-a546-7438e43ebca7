import React from "react";
import { useListOrder } from "@/features/merchants/hooks/useListOrder";
import { orderStorage } from "@/shared/utils/localStorage.utils";
import OrderDetail from "./OrderDetail";
import { formatPrice } from "@/features/merchants/utils";

const getOrderStatusText = (status: string) => {
  switch (status) {
    case "pending":
      return "Chờ xác nhận";
    case "confirmed":
      return "Đã xác nhận";
    case "preparing":
      return "Đang chuẩn bị";
    case "ready":
      return "Sẵn sàng";
    case "completed":
      return "Hoàn thành";
    case "cancelled":
      return "Đã hủy";
    default:
      return status;
  }
};

const OrdersList: React.FC = () => {
  // Lấy orderIds từ localStorage
  const orderIds = orderStorage.getOrderHistory();
  const { data, isLoading, error } = useListOrder(orderIds);

  if (isLoading) {
    return (
      <div className="p-4 text-center text-gray-500"><PERSON><PERSON> tải đơn hàng...</div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        Lỗi tải đơn hàng: {error.message}
      </div>
    );
  }

  if (!data || !data.orders.length) {
    return (
      <div className="p-4 text-center text-gray-400">Chưa có đơn hàng nào.</div>
    );
    ``;
  }

  return (
    <div className="p-4 space-y-4">
      {data.orders.map((order) => (
        <div
          key={order.id}
          className="bg-white rounded-lg shadow p-4 flex flex-col gap-2 border border-gray-100"
        >
          <div className="flex items-center justify-between">
            <span className="font-semibold text-blue-700">
              #{order.order_number}
            </span>
            <span className="text-xs px-2 py-1 rounded bg-blue-50 text-blue-600 font-medium">
              {getOrderStatusText(order.status)}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              Tổng:{" "}
              <span className="font-semibold text-gray-900">
                {formatPrice(order.total_amount)}
              </span>
            </span>
            <span>
              Ngày đặt: {new Date(order.ordered_at).toLocaleString("vi-VN")}
            </span>
          </div>
          {/* <div className="text-sm text-gray-500">
            Nhà hàng:{" "}
            <span className="font-medium text-gray-800">
              {order.merchant_orders[0]?.merchant?.name || "-"}
            </span>
          </div> */}
          <OrderDetail order={order} />
        </div>
      ))}
    </div>
  );
};

export default OrdersList;
