import React, { useState, useCallback } from "react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  ChevronLeftIcon,
  PlusIcon,
  MinusIcon,
} from "@heroicons/react/16/solid";

// Types
interface FoodItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  quantity: number;
}

// Mock data
const mockFoodItem: FoodItem = {
  id: "1",
  name: "Bún riêu",
  description:
    "Bún riêu là món súp truyền thống của Việt Nam gồm nước luộc và bún. Có một số loại bún riêu, bao gồm bún riêu cua, bún riêu cá và bún riêu ốc",
  price: 40000,
  image: "/api/placeholder/390/219",
  quantity: 1,
};

export const MerchantsItemDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const [item, setItem] = useState(mockFoodItem);

  const handleQuantityChange = useCallback((newQuantity: number) => {
    if (newQuantity >= 0) {
      setItem((prev) => ({ ...prev, quantity: newQuantity }));
    }
  }, []);

  const handleDecrease = useCallback(() => {
    if (item.quantity > 0) {
      handleQuantityChange(item.quantity - 1);
    }
  }, [item.quantity, handleQuantityChange]);

  const handleIncrease = useCallback(() => {
    handleQuantityChange(item.quantity + 1);
  }, [item.quantity, handleQuantityChange]);

  const formatPrice = useCallback((price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    })
      .format(price)
      .replace("₫", "đ");
  }, []);

  const handleUpdateCart = useCallback(() => {
    // Navigate back to merchant detail
    navigate({ to: "/merchants" }); // Simplified navigation for now
  }, [navigate]);

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="bg-white px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate({ to: "/merchants" })}
            className="w-10 h-10 rounded-full flex items-center justify-center"
          >
            <ChevronLeftIcon className="w-6 h-6 text-[#7C7B7B]" />
          </button>

          <div className="flex flex-col items-center">
            <h1 className="text-sm font-bold text-[#181818]">Chi tiết món</h1>
          </div>

          <div className="w-10 h-10" />
        </div>
      </div>

      {/* Food Image */}
      <div className="h-[200px] flex items-center justify-center">
        <img
          src={item.image}
          alt={item.name}
          className="w-[390px] h-[219px] object-cover"
        />
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center gap-10 p-5">
        {/* Item Details */}
        <div className="flex flex-col gap-2 max-w-[305px]">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-bold text-[#181818] text-center">
              {item.name}
            </h2>
            <span className="text-lg font-bold text-[#181818] text-center">
              {formatPrice(item.price)}
            </span>
          </div>

          <p className="text-xs font-normal text-[#5C5C5C] leading-4 text-left">
            {item.description}
          </p>
        </div>

        {/* Quantity Controls */}
        <div className="flex items-center gap-3">
          <button
            onClick={handleDecrease}
            className="w-8 h-8 rounded-lg bg-[#EFEFEF] flex items-center justify-center"
            disabled={item.quantity === 0}
          >
            <MinusIcon className="w-4 h-4 text-[#7C7B7B]" />
          </button>

          <div className="w-12 h-8 rounded-lg bg-[#F8F8F8] flex items-center justify-center px-4">
            <span className="text-sm font-normal text-[#5C5C5C]">
              {item.quantity}
            </span>
          </div>

          <button
            onClick={handleIncrease}
            className="w-8 h-8 rounded-lg bg-[#2D5BFF] flex items-center justify-center"
          >
            <PlusIcon className="w-4 h-4 text-white" />
          </button>
        </div>
      </div>

      {/* Bottom Button */}
      <div className="bg-white p-4 rounded-t-[20px] shadow-lg">
        <button
          onClick={handleUpdateCart}
          className="w-full bg-[#2D5BFF] rounded-lg py-2.5 px-4 flex items-center justify-between"
        >
          <span className="text-xs font-bold text-white">
            Cập nhật giỏ hàng
          </span>
          <span className="text-xs font-bold text-white">
            {formatPrice(item.price * item.quantity)}
          </span>
        </button>
      </div>
    </div>
  );
};

export const Route = createFileRoute("/merchants/item-detail/$id")({
  component: MerchantsItemDetailPage,
});
