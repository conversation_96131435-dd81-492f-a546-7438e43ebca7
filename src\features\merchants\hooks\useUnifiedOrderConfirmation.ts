import { useState, useCallback, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useCreateOrder } from "@/features/merchants/hooks/useCreateOrder";
import { orderStorage } from "@/shared/utils/localStorage.utils";
import {
  DeliveryTypeEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";
import type {
  CustomerInfo,
  DeliveryOption,
} from "@/shared/types/order-confirmation.types";
import type { CreateOrderRequest } from "@/api/merchants/orders.types";
import {
  simpleCartActions,
  simpleCartSelectors,
} from "@/stores/simpleCartStore";
import { merchantsStore } from "@/stores/merchantsStore";

interface UseUnifiedOrderConfirmationProps {
  restaurantId?: string;
  tableId?: string;
  qr: string;
}

export const useUnifiedOrderConfirmation = ({
  tableId,
  qr,
}: UseUnifiedOrderConfirmationProps) => {
  const navigate = useNavigate();

  // Common hooks
  const createOrderMutation = useCreateOrder();

  // Common state
  const [isLoading, setIsLoading] = useState(true);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: "",
    phone: "",
  });
  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption>(
    DeliveryTypeEnum.TABLE_DELIVERY
  );
  const [note, setNote] = useState("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [paymentType, setPaymentType] = useState<PaymentTypeEnum>(
    PaymentTypeEnum.CASH
  );

  // Check cart data on component mount
  useEffect(() => {
    setIsLoading(false);
  }, []);

  // Common handlers
  const handleBackToMenu = useCallback(() => {
    navigate({ to: `/merchants`, search: { qr: qr ?? "" } });
  }, [navigate, qr]);

  const handleAddMoreItems = useCallback(() => {
    navigate({ to: `/merchants`, search: { qr: qr ?? "" } });
  }, [navigate, qr]);

  // Quantity change handler
  const handleQuantityChange = useCallback(
    (itemId: string, newQuantity: number) => {
      simpleCartActions.updateItemQuantity(itemId, newQuantity);
    },
    [simpleCartActions]
  );

  // Prepare order data
  const prepareOrderData = useCallback((): CreateOrderRequest => {
    const foodCourtTableId = tableId || "99bb7773-8b1e-4840-9740-e75577093a5e";
    const allItems = simpleCartSelectors.getAllItems().map((item) => ({
      food_item_id: item.id,
      quantity: item.quantity,
      special_instructions: undefined,
    }));
    return {
      food_court_table_id: foodCourtTableId,
      delivery_type: deliveryOption,
      special_instructions: note || undefined,
      payment_method: paymentType,
      items: allItems,
    };
  }, [deliveryOption, note, tableId, paymentType]);

  // Place order handler
  const handlePlaceOrder = useCallback(async () => {
    if (createOrderMutation.isPending) return;
    try {
      const orderData = prepareOrderData();
      const response = await createOrderMutation.mutateAsync(orderData);
      if (response?.id) {
        orderStorage.addToOrderHistory(response.id);
      }

      if (paymentType === PaymentTypeEnum.CARD) {
        const expiry = new Date(Date.now() + 10 * 60 * 1000);
        navigate({
          to: "/merchants/qr-payment",
          search: {
            qr: qr ?? undefined,
            expiry: expiry.toISOString(),
          },
        });
      } else {
        setShowSuccessPopup(true);

        simpleCartActions.clearCart();

        setTimeout(() => {
          setShowSuccessPopup(false);
          navigate({ to: "/merchants", search: { qr: qr ?? "" } });
        }, 2000);
      }
    } catch (error) {
      console.error("Failed to place order:", error);
    }
  }, [createOrderMutation, prepareOrderData, navigate, qr, paymentType]);

  // useEffect(() => {
  //   if (showSuccessPopup && paymentType === PaymentTypeEnum.CARD) {
  //     const expiry = new Date(Date.now() + 10 * 60 * 1000);
  //     navigate({
  //       to: "/merchants/qr-payment",
  //       search: {
  //         qr: qr ?? undefined,
  //         expiry: expiry.toISOString(),
  //       },
  //     });
  //   }
  // }, [showSuccessPopup, paymentType, navigate, qr]);

  // Form handlers
  const handleCustomerInfoChange = useCallback((info: CustomerInfo) => {
    setCustomerInfo(info);
  }, []);

  const handleDeliveryOptionChange = useCallback((option: DeliveryOption) => {
    setDeliveryOption(option);
  }, []);

  const handleNoteChange = useCallback((newNote: string) => {
    setNote(newNote);
  }, []);

  // Payment type handler
  const handlePaymentTypeChange = useCallback((type: PaymentTypeEnum) => {
    setPaymentType(type);
  }, []);

  // Computed values
  const totalPrice = simpleCartSelectors
    .getAllItems()
    .reduce((sum, item) => sum + item.price * item.quantity, 0);
  const restaurantName =
    merchantsStore.state.merchantData?.qrData?.restaurantName || "";

  return {
    isLoading,
    isPlacingOrder: createOrderMutation.isPending,
    customerInfo,
    deliveryOption,
    note,
    createOrderMutation,
    showSuccessPopup,
    totalPrice,
    restaurantName,
    handleBackToMenu,
    handleAddMoreItems,
    handleQuantityChange,
    handlePlaceOrder,
    handleCustomerInfoChange,
    handleDeliveryOptionChange,
    handleNoteChange,
    paymentType,
    handlePaymentTypeChange,
  };
};
