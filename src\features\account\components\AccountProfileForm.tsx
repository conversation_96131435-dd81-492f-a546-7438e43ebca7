import React, { useState, useEffect } from "react";
import { AvatarUpload } from "./AvatarUpload";
import { updateAvatar, updateProfile, deleteAvatar } from "@/api/auth/authApi";
import { useAuthState } from "@/shared/hooks/useAuth";
import { useQueryClient } from "@tanstack/react-query";
import { FormFieldSelect } from "@/shared/components/FormFieldSelect";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { CalendarIcon } from "@/shared";

const Toast: React.FC<{ show: boolean; message: string }> = ({
  show,
  message,
}) => (
  <div
    className={`fixed top-6 right-6 z-[9999] transition-all duration-300 ${show ? "opacity-100" : "opacity-0 pointer-events-none"}`}
  >
    <div className="bg-[#2d5bff] text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium">
      {message}
    </div>
  </div>
);

const formSchema = z.object({
  fullName: z
    .string()
    .min(1, "Họ và tên là bắt buộc")
    .max(50, "Họ và tên không vượt quá 50 ký tự")
    .trim(),
  email: z
    .string()
    .trim()
    .or(z.literal(""))
    .refine((val) => val === "" || /^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(val), {
      message: "Email không hợp lệ",
    }),
  phone: z.string(),
  birthday: z.string().optional().or(z.literal("")),
  gender: z.enum(["Nam", "Nữ", "Khác"]),
  address: z.string().optional().or(z.literal("")),
  job: z.string().optional().or(z.literal("")),
});

type FormValues = z.infer<typeof formSchema>;

export const AccountProfileForm: React.FC = () => {
  const { profile: userProfile } = useAuthState();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [avatarState, setAvatarState] = useState({
    avatarUrl: undefined as string | undefined,
    fileName: "",
    fileSize: "",
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      birthday: "",
      gender: "Nam",
      address: "",
      job: "",
    },
  });

  useEffect(() => {
    if (userProfile) {
      const gender = userProfile.metadata?.userSettings?.gender;
      // Validate gender before setting
      const validGender =
        gender === "Nam" || gender === "Nữ" || gender === "Khác"
          ? gender
          : "Nam";

      form.reset({
        fullName: userProfile.full_name || "",
        email: userProfile.email || "",
        phone: userProfile.phone_number || "",
        birthday: userProfile.metadata?.userSettings?.birthday || "",
        gender: validGender,
        address: userProfile.metadata?.userSettings?.address || "",
        job: userProfile.metadata?.userSettings?.job || "",
      });
      setAvatarState({
        avatarUrl:
          userProfile.avatar?.sizes?.medium ??
          userProfile.avatar?.sizes?.original,
        fileName: userProfile.avatar?.originalName || "",
        fileSize: "",
      });
    }
  }, [userProfile, form]);

  const handleAvatarUpload = async () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = async (e: any) => {
      const file = e.target.files[0];
      if (!file) return;
      const formData = new FormData();
      formData.append("file", file);
      try {
        setLoading(true);
        const res = await updateAvatar(formData);
        setAvatarState({
          avatarUrl: res.sizes?.medium || res.sizes?.original,
          fileName: file.name,
          fileSize: `${(file.size / 1024 / 1024).toFixed(2)} Mb`,
        });
        setShowToast(true);
        const timer = setTimeout(() => setShowToast(false), 2000);
        queryClient.invalidateQueries({ queryKey: ["auth", "profile"] });
        return () => clearTimeout(timer);
      } catch (err: any) {
        form.setError("root", {
          message: err?.response?.data?.message || "Upload avatar thất bại.",
        });
      } finally {
        setLoading(false);
      }
    };
    input.click();
  };

  const handleAvatarClear = async () => {
    try {
      setLoading(true);
      await deleteAvatar();
      setAvatarState({
        avatarUrl: undefined,
        fileName: "",
        fileSize: "",
      });
      setShowToast(true);
      const timer = setTimeout(() => setShowToast(false), 2000);
      queryClient.invalidateQueries({ queryKey: ["auth", "profile"] });
      return () => clearTimeout(timer);
    } catch (err: any) {
      form.setError("root", {
        message: err?.response?.data?.message || "Xóa avatar thất bại.",
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (values: FormValues) => {
    try {
      setLoading(true);
      await updateProfile({
        full_name: values.fullName,
        email: values.email,
        phone_number: values.phone,
        metadata: {
          userSettings: {
            birthday: values.birthday,
            gender: values.gender,
            address: values.address,
            job: values.job,
          },
        },
      });
      setShowToast(true);
      const timer = setTimeout(() => setShowToast(false), 2000);
      queryClient.invalidateQueries({ queryKey: ["auth", "profile"] });
      return () => clearTimeout(timer);
    } catch (err: any) {
      form.setError("root", {
        message: err?.response?.data?.message || "Cập nhật hồ sơ thất bại.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        className="bg-white rounded-xl max-w-5xl mx-auto flex flex-col gap-4 relative"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="font-bold text-[#181818] text-[16px] leading-[18px] text-center sm:text-start">
          Quản lý hồ sơ để bảo mật tài khoản
        </div>
        <div className="h-px w-full bg-[#ededed]" />
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-start">
          <AvatarUpload
            avatarUrl={avatarState.avatarUrl}
            // fileName={avatarState.fileName}
            // fileSize={avatarState.fileSize}
            onUpload={handleAvatarUpload}
            onClear={handleAvatarClear}
          />
          <div className="flex flex-col flex-1 w-full md:grid md:grid-cols-1 lg:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                    Họ và tên
                  </FormLabel>
                  <FormControl>
                    <input
                      className="text-sm text-[#181818] bg-transparent outline-none w-full focus:placeholder-gray-400 truncate"
                      placeholder="Nhập họ và tên"
                      maxLength={50}
                      value={field.value}
                      onChange={(e) => {
                        const val = e.target.value.slice(0, 50);
                        field.onChange(val);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                    Email
                  </FormLabel>
                  <FormControl>
                    <input
                      className="text-sm bg-transparent outline-none w-full focus:placeholder-gray-400"
                      placeholder="Nhập email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                    Điện thoại
                  </FormLabel>
                  <FormControl>
                    <input
                      className="text-sm text-[#181818] bg-transparent outline-none w-full focus:placeholder-gray-400"
                      placeholder="Nhập số điện thoại"
                      maxLength={20}
                      value={field.value}
                      onChange={(e) => {
                        const val = e.target.value.slice(0, 20);
                        field.onChange(val);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="birthday"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] h-[54px] relative gap-2 rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <div className="bg-[#EDEDED] absolute top-0 left-0 focus-within:bg-[#f0f0f0] flex items-center justify-center h-[54px] w-[54px] rounded-l-lg">
                    <CalendarIcon />
                  </div>
                  <div className="ml-[54px]">
                    <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                      Ngày sinh
                    </FormLabel>
                    <FormControl>
                      <input
                        type="date"
                        className="text-sm text-[#181818] bg-transparent outline-none w-full focus:placeholder-gray-400 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                        placeholder="Chọn ngày sinh"
                        max={new Date().toISOString().split("T")[0]}
                        min="1900-01-01"
                        {...field}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <FormFieldSelect
                      label="Giới tính"
                      value={field.value}
                      onChange={field.onChange}
                      options={[
                        { label: "Nam", value: "Nam" },
                        { label: "Nữ", value: "Nữ" },
                        { label: "Khác", value: "Khác" },
                      ]}
                      placeholder="Chọn giới tính"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                    Địa chỉ
                  </FormLabel>
                  <FormControl>
                    <input
                      className="text-sm text-[#181818] bg-transparent outline-none w-full focus:placeholder-gray-400"
                      placeholder="Nhập địa chỉ"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="job"
              render={({ field }) => (
                <FormItem className="bg-[#f8f8f8] rounded-lg px-4 py-1.5 transition-all focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50">
                  <FormLabel className="block text-xs font-bold text-[#8a8a8a] mb-0.5">
                    Nghề nghiệp
                  </FormLabel>
                  <FormControl>
                    <input
                      className="text-sm text-[#181818] bg-transparent outline-none w-full focus:placeholder-gray-400"
                      placeholder="Nhập nghề nghiệp"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="h-px w-full bg-[#ededed]" />
        <div className="flex flex-col gap-2 max-w-ld">
          <button
            type="submit"
            className="bg-[#2d5bff] text-white rounded-lg px-8 py-3 font-extrabold text-[18px] leading-[24px] tracking-wide w-full sm:w-40 self-start mt-2 hover:bg-[#2046c7] transition disabled:opacity-60"
            disabled={loading}
          >
            {loading ? "Đang lưu..." : "Lưu"}
          </button>
          {form.formState.errors.root && (
            <div className="text-red-600 text-sm mt-2">
              {form.formState.errors.root.message}
            </div>
          )}
        </div>
        <Toast show={showToast} message="Cập nhật thành công!" />
      </form>
    </Form>
  );
};
