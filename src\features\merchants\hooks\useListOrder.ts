import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import { getFoodCourtOrdersDetails } from "@/features/merchants/api";
import type { FoodCourtOrderDetailsResponse } from "@/features/merchants/api/orders.types";

export const foodCourtOrderQueryKeys = {
  all: ["food-court"] as const,
  orders: () => [...foodCourtOrderQueryKeys.all, "orders"] as const,
  orderList: (orderIds: string[]) =>
    [...foodCourtOrderQueryKeys.orders(), orderIds] as const,
};

/**
 * Hook to fetch detailed food court orders by IDs
 * @param orderIds - Array of order IDs
 * @param options - React Query options
 */
export const useListOrder = (
  orderIds: string[],
  options?: Omit<
    UseQueryOptions<FoodCourtOrderDetailsResponse, Error>,
    "queryKey" | "queryFn"
  >
) => {
  return useQuery<FoodCourtOrderDetailsResponse, Error>({
    queryKey: foodCourtOrderQueryKeys.orderList(orderIds),
    queryFn: () => getFoodCourtOrdersDetails(orderIds),
    enabled: orderIds.length > 0,
    ...options,
  });
};
