import { memo, useCallback } from "react";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import { Link } from "@tanstack/react-router";
import { useRouter } from "@tanstack/react-router";

const BreadcrumbSection = memo(() => {
  const router = useRouter();

  const handleBack = useCallback(() => {
    router.history.back();
  }, [router]);

  return (
    <div className="bg-booking-background py-3">
      <div className="flex items-center gap-2 mb-3">
        <Link to="/" className="text-base text-booking-muted">
          Trang chủ
        </Link>
        <ChevronRightIcon className="w-4 h-4 text-gray-400" />
        <button
          type="button"
          onClick={handleBack}
          className="text-base text-booking-muted cursor-pointer"
        >
          T<PERSON><PERSON> chuyến
        </button>
        <ChevronRightIcon className="w-4 h-4 text-gray-400" />
        <span className="text-base font-bold text-booking-text">
          Chi tiết vé
        </span>
      </div>
    </div>
  );
});

BreadcrumbSection.displayName = "BreadcrumbSection";

export default BreadcrumbSection;
