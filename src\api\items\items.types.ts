export interface Item {
  id: string;
  status: string;
  created_at: string;
  updated_at: string;
  description: string;
  display_order: number;
  food_category_id: string;
  image_url: string;
  images: string;
  is_available: true;
  is_highlighted: true;
  merchant_id: string;
  name: string;
  preparation_time_minutes: number;
  price: string;
}

export interface GetItemDetailResponse extends Item {}

export type ItemsResponse = Item[];
