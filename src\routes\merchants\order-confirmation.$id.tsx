import React, { useMemo } from "react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { LoadingSpinner } from "@/shared/components/LoadingSpinner";
import { useUnifiedOrderConfirmation } from "@/features/merchants/hooks/useUnifiedOrderConfirmation";
import {
  OrderHeader,
  CustomerInfoSection,
  DeliveryOptionsSection,
  PaymentOptionsSection,
  OrderNoteSection,
  BottomTotalSection,
  MultipleRestaurantOrderSummary,
} from "@/components/merchants-order-confirmation";
import { CheckCircleIcon } from "@heroicons/react/24/solid";
import { PaymentTypeEnum } from "@/features/merchants/constants";
import { simpleCartSelectors } from "@/stores/simpleCartStore";
import { merchantsStore } from "@/stores/merchantsStore";

// Shared UI Components
const SuccessPopup: React.FC<{ show: boolean }> = React.memo(({ show }) => {
  const navigate = useNavigate();
  const { qr } = Route.useSearch();

  const handleBackToHome = () => {
    navigate({ to: "/merchants", search: { qr: qr ?? "" } });
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-transparent">
      <div className="w-full bg-transparent">
        <div className="min-h-screen bg-white flex flex-col items-center justify-center px-6">
          <div className="flex flex-col items-center text-center space-y-6">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircleIcon className="w-12 h-12 text-green-600" />
            </div>

            {/* Success Message */}
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-gray-900">
                Đặt hàng thành công!
              </h1>
              <p className="text-gray-600">
                Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ xử lý đơn hàng của bạn sớm
                nhất có thể.
              </p>
            </div>

            {/* Action Button */}
            <button
              onClick={handleBackToHome}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Về trang chủ
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

const ErrorSection: React.FC<{ isError: boolean; message: string }> =
  React.memo(({ isError, message }) => {
    if (!isError) return null;

    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-3">
        <p className="text-red-600 text-sm">{message}</p>
      </div>
    );
  });

// Unified Order Confirmation Component
const OrderConfirmationContent: React.FC<{
  restaurantId: string;
  qr: string;
}> = React.memo(({ restaurantId, qr }) => {
  const {
    isLoading,
    isPlacingOrder,
    customerInfo,
    deliveryOption,
    note,
    createOrderMutation,
    showSuccessPopup,
    totalPrice,
    restaurantName,
    handleBackToMenu,
    handlePlaceOrder,
    handleCustomerInfoChange,
    handleDeliveryOptionChange,
    handleNoteChange,
    paymentType,
    handlePaymentTypeChange,
  } = useUnifiedOrderConfirmation({
    restaurantId,
    qr,
  });

  const items = simpleCartSelectors.getAllItems();
  const itemsByMerchant = useMemo(() => {
    return items.reduce(
      (acc, item) => {
        if (!acc[item.merchantId]) acc[item.merchantId] = [];
        acc[item.merchantId].push(item);
        return acc;
      },
      {} as Record<string, typeof items>
    );
  }, [items]);

  const isDisabled = useMemo(
    () => isPlacingOrder || showSuccessPopup,
    [isPlacingOrder, showSuccessPopup]
  );

  // Khi đặt hàng thành công và paymentType là CARD thì navigate sang màn qr-payment

  // Loading state
  if (isLoading) return <LoadingSpinner />;

  // Error state - no cart data
  // const hasNoData = !multipleRestaurantCartData || multipleRestaurantCartData.restaurants.length === 0;
  // if (hasNoData) {
  //   return (
  //     <ErrorMessage
  //       message="Không có dữ liệu giỏ hàng. Vui lòng quay lại chọn món."
  //       onRetry={handleBackToMenu}
  //     />
  //   );
  // }

  const totalItems = simpleCartSelectors
    .getAllItems()
    .reduce((sum, item) => sum + item.quantity, 0);
  function getMerchantName(merchantId: string): string {
    const merchants = merchantsStore.state.merchantData?.merchants;
    if (Array.isArray(merchants)) {
      return (
        merchants.find((m: any) => m.id === merchantId)?.name || merchantId
      );
    }
    return merchantId;
  }

  return (
    <div className="min-h-screen bg-[#F8F8F8] pb-32">
      <SuccessPopup show={showSuccessPopup} />

      <OrderHeader
        restaurantName={restaurantName}
        onBackToMenu={handleBackToMenu}
        disabled={isDisabled}
      />

      <div className="flex-1 p-3 space-y-2.5">
        <CustomerInfoSection
          customerInfo={customerInfo}
          onCustomerInfoChange={handleCustomerInfoChange}
          disabled={isDisabled}
        />

        <DeliveryOptionsSection
          deliveryOption={deliveryOption}
          onDeliveryOptionChange={handleDeliveryOptionChange}
          disabled={isDisabled}
        />

        {Object.entries(itemsByMerchant).map(([merchantId, merchantItems]) => (
          <div key={merchantId} className="mb-6">
            <MultipleRestaurantOrderSummary
              merchantName={getMerchantName(merchantId)}
              items={merchantItems}
              disabled={isDisabled}
            />
          </div>
        ))}

        <OrderNoteSection
          note={note}
          onNoteChange={handleNoteChange}
          disabled={isDisabled}
        />

        <ErrorSection
          isError={createOrderMutation.isError}
          message="Có lỗi xảy ra khi đặt đơn. Vui lòng thử lại."
        />
        <PaymentOptionsSection
          paymentType={paymentType ?? PaymentTypeEnum.CASH}
          onPaymentTypeChange={handlePaymentTypeChange}
          disabled={isDisabled}
        />
      </div>

      <BottomTotalSection
        buttonTitle="Đặt đơn"
        totalPrice={totalPrice}
        onPlaceOrder={handlePlaceOrder}
        isPlacingOrder={isDisabled}
        disabled={totalItems === 0 || showSuccessPopup}
      />
    </div>
  );
});

// Main Component
export const MerchantsOrderConfirmationPage: React.FC = () => {
  const { id } = Route.useParams();
  const { qr } = Route.useSearch();

  return <OrderConfirmationContent restaurantId={id} qr={qr ?? ""} />;
};

export const Route = createFileRoute("/merchants/order-confirmation/$id")({
  component: MerchantsOrderConfirmationPage,
  validateSearch: (search: Record<string, unknown>) => ({
    qr: typeof search.qr === "string" ? search.qr : undefined,
  }),
});
