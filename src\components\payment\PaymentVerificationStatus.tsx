import { DotLoader } from "@/components/DotLoader";

interface PaymentVerificationStatusProps {
  variant?: "blue" | "primary";
  className?: string;
}

export function PaymentVerificationStatus({
  variant = "primary",
  className = "",
}: PaymentVerificationStatusProps) {
  const bgColor = variant === "blue" ? "bg-[#2D5BFF]" : "bg-[#2563eb]";

  return (
    <div
      className={`${bgColor} h-full flex flex-col items-center justify-center py-6 px-4 rounded-2xl ${className}`}
    >
      <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mb-3">
        <DotLoader />
      </div>
      <div className="text-white text-lg font-bold mb-1">
        Đang xác minh thanh toán
      </div>
      <div className="text-white text-sm text-center opacity-80">
        <PERSON><PERSON><PERSON> chưa chuyể<PERSON>, bạn vui lòng làm theo{" "}
        <span className="font-bold underline">H<PERSON><PERSON>ng dẫn thanh toán</span> để
        hoàn tất giao dịch
      </div>
    </div>
  );
}
