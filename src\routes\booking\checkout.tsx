import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import { z } from "zod";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import { useContactFormStore } from "@/stores/contactFormStore";
import { useCreateOrder } from "@/shared/hooks/useOrders";
import type { CreateOrderRequest } from "@/api/orders";
import {
  BreadcrumbSection,
  ContactFormSection,
  TripDetailsSection,
  PriceSummary,
} from "@/features/booking/components";
import { CheckoutMobileHeader } from "@/features/booking/components/CheckoutMobileHeader";
import { Container } from "@/shared/components";
import { getTranslatedErrorMessage } from "@/shared/utils/errorMessageTranslator";
import { validateVietnamesePhoneNumber } from "@/shared/utils/phoneNumberUtils";
import { Route as PaymentInfoRoute } from "./payment-info";

// Define the expected search parameters for the checkout page (original schema)
const checkoutSearchSchema = z
  .object({
    scheduleId: z.string(),
    numTickets: z.number().optional().default(1),
    price: z.number(),
    fromPlaceId: z.number().optional(),
    toPlaceId: z.number().optional(),
    fromPlaceName: z.string().optional(),
    toPlaceName: z.string().optional(),
    departureDate: z.string().optional(),
    returnDate: z.string().optional(),
    busNumber: z.string().optional(),
    departureTime: z.string().optional(),
    arrivalTime: z.string().optional(),
    dropoffId: z.string().optional(),
    isRoundTrip: z.boolean().optional().default(false),
  })
  .catchall(z.any());

export const Route = createFileRoute("/booking/checkout")({
  validateSearch: checkoutSearchSchema,
  component: CheckoutPage,
});

function CheckoutPage() {
  const navigate = useNavigate();
  const searchParams = Route.useSearch(); // Get search params
  const { outboundTrip, returnTrip } = useSelectedTripsStore();
  const { formData, setFormData, clearFormData, isFormDataStale } =
    useContactFormStore();

  // Initialize form state with stored data or empty values
  const [customerName, setCustomerName] = useState(() => {
    // Chỉ khôi phục form data nếu không cũ
    return !isFormDataStale() ? formData.customerName : "";
  });
  const [customerPhone, setCustomerPhone] = useState(() => {
    return !isFormDataStale() ? formData.customerPhone : "";
  });
  const [customerEmail, setCustomerEmail] = useState(() => {
    return !isFormDataStale() ? formData.customerEmail : "";
  });
  const [acceptTerms, setAcceptTerms] = useState(() => {
    return !isFormDataStale() ? formData.acceptTerms : false;
  });

  const [submitMessage, setSubmitMessage] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    phone?: string;
    email?: string;
    terms?: string;
  }>({});

  const createOrderMutation = useCreateOrder();

  // Clear stale form data khi vào trang
  useEffect(() => {
    if (isFormDataStale()) {
      clearFormData();
    }
  }, [isFormDataStale, clearFormData]);

  // Save form data to store whenever it changes
  useEffect(() => {
    setFormData({
      customerName,
      customerPhone,
      customerEmail,
      acceptTerms,
    });
  }, [customerName, customerPhone, customerEmail, acceptTerms, setFormData]);

  // Clear form errors when user types
  const handleNameChange = (value: string) => {
    setCustomerName(value);
    if (formErrors.name && value.trim()) {
      setFormErrors((prev) => ({ ...prev, name: undefined }));
    }
  };

  const handlePhoneChange = (value: string) => {
    setCustomerPhone(value);
    if (formErrors.phone && value.trim()) {
      setFormErrors((prev) => ({ ...prev, phone: undefined }));
    }
  };

  const handleEmailChange = (value: string) => {
    setCustomerEmail(value);
    if (formErrors.email && value.trim()) {
      setFormErrors((prev) => ({ ...prev, email: undefined }));
    }
  };

  const handleAcceptTermsChange = (value: boolean) => {
    setAcceptTerms(value);
    if (formErrors.terms && value) {
      setFormErrors((prev) => ({ ...prev, terms: undefined }));
    }
  };

  const handleProceedToPayment = async () => {
    // Reset previous errors
    setFormErrors({});
    setSubmitMessage(null);

    // Validate form fields
    const errors: {
      name?: string;
      phone?: string;
      email?: string;
      terms?: string;
    } = {};

    if (!customerName.trim()) {
      errors.name = "Vui lòng nhập tên người đi";
    }

    // Use Vietnamese phone number validation
    const phoneValidation = validateVietnamesePhoneNumber(customerPhone);
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.errorMessage!;
    }

    // Email validation - optional but if provided, must be valid
    if (customerEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(customerEmail.trim())) {
        errors.email = "Email không hợp lệ";
      }
    }

    // Terms validation - required
    if (!acceptTerms) {
      errors.terms = "Vui lòng chấp nhận điều khoản đặt vé";
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    if (!outboundTrip) {
      setSubmitMessage("Vui lòng chọn ít nhất một chuyến đi.");
      return;
    }

    if (!outboundTrip.dropoffId || !outboundTrip.departureDate) {
      setSubmitMessage("Thiếu thông tin đặt vé quan trọng.");
      return;
    }

    // Get number of tickets from search params, default to 1
    const numTickets = searchParams.numTickets || 1;

    // Tạo tickets array từ các chuyến đã chọn
    const tickets = [];

    // Thêm chuyến đi
    tickets.push({
      bus_schedule_id: outboundTrip.scheduleId,
      dropoff_id: outboundTrip.dropoffId,
      departure_date: outboundTrip.departureDate,
      quantity: numTickets,
      ticket_type: "One Way",
    });

    // Thêm chuyến về nếu có
    if (returnTrip && returnTrip.dropoffId && returnTrip.departureDate) {
      tickets.push({
        bus_schedule_id: returnTrip.scheduleId,
        dropoff_id: returnTrip.dropoffId,
        departure_date: returnTrip.departureDate,
        quantity: numTickets,
        ticket_type: "Return",
      });
    }

    // Normalize phone number before sending to API
    const normalizedPhone =
      phoneValidation.normalizedNumber || customerPhone.trim();

    const orderData: CreateOrderRequest = {
      customer_name: customerName.trim(),
      customer_phone: normalizedPhone,
      customer_email: customerEmail.trim() || undefined,
      tickets: tickets,
    };

    try {
      const responseData = await createOrderMutation.mutateAsync(orderData);

      // Lưu form data trước khi navigate để có thể khôi phục khi back lại
      setFormData({
        customerName,
        customerPhone,
        customerEmail,
        acceptTerms,
      });

      // Navigate to payment success page with order data
      navigate({
        to: PaymentInfoRoute.to,
        search: {
          orderId: responseData?.id ?? "",
          numTickets: numTickets,
          customerName: customerName ?? "",
          customerPhone: customerPhone ?? "",
          customerEmail: customerEmail ?? "",
        },
      });
    } catch (error: any) {
      const translatedError = getTranslatedErrorMessage(error);
      setSubmitMessage(`Lỗi khi tạo đơn hàng: ${translatedError}`);
    }
  };

  return (
    <div className="w-full bg-gray-100">
      <div className="lg:hidden">
        <CheckoutMobileHeader
          fromPlaceId={searchParams.fromPlaceId}
          toPlaceId={searchParams.toPlaceId}
          fromLocation={searchParams.fromPlaceName}
          toLocation={searchParams.toPlaceName}
          departureDate={searchParams.departureDate}
          returnDate={searchParams.returnDate}
          numTickets={searchParams.numTickets}
          isRoundTrip={searchParams.isRoundTrip}
        />
      </div>
      <Container>
        {/* Mobile Header */}

        {/* Desktop Breadcrumb */}
        <div className="hidden lg:block">
          <BreadcrumbSection />
        </div>

        {/* Toast-like error/success message */}
        {submitMessage && (
          <div className="fixed z-50 max-w-md top-4 right-4">
            <div
              className={`p-4 rounded-lg shadow-lg border ${
                submitMessage.startsWith("Lỗi") ||
                submitMessage.startsWith("Thiếu") ||
                submitMessage.startsWith("Vui lòng")
                  ? "bg-red-50 text-red-600 border-red-200"
                  : "bg-green-50 text-green-600 border-green-200"
              }`}
            >
              {submitMessage}
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="py-4">
          {/* Mobile Layout - Single Column */}
          <div className="pb-40 space-y-4 lg:hidden">
            <ContactFormSection
              customerName={customerName}
              setCustomerName={handleNameChange}
              customerPhone={customerPhone}
              setCustomerPhone={handlePhoneChange}
              customerEmail={customerEmail}
              setCustomerEmail={handleEmailChange}
              isSubmitting={createOrderMutation.isPending}
              formErrors={formErrors}
              acceptTerms={acceptTerms}
              setAcceptTerms={handleAcceptTermsChange}
            />

            <TripDetailsSection
              customerName={customerName}
              customerPhone={customerPhone}
              customerEmail={customerEmail}
              acceptTerms={acceptTerms}
              searchParams={searchParams}
            />
          </div>

          {/* Desktop Layout - Two Column */}
          <div className="hidden lg:flex lg:gap-6">
            {/* Left Column */}
            <div className="w-2/3 space-y-4">
              <ContactFormSection
                customerName={customerName}
                setCustomerName={handleNameChange}
                customerPhone={customerPhone}
                setCustomerPhone={handlePhoneChange}
                customerEmail={customerEmail}
                setCustomerEmail={handleEmailChange}
                isSubmitting={createOrderMutation.isPending}
                formErrors={formErrors}
                acceptTerms={acceptTerms}
                setAcceptTerms={handleAcceptTermsChange}
              />

              <TripDetailsSection
                customerName={customerName}
                customerPhone={customerPhone}
                customerEmail={customerEmail}
                acceptTerms={acceptTerms}
                searchParams={searchParams}
              />
            </div>

            {/* Right Column - Price Summary */}
            <div className="w-1/3">
              <PriceSummary
                onPayment={handleProceedToPayment}
                isSubmitting={createOrderMutation.isPending}
                numTickets={searchParams.numTickets || 1}
              />
            </div>
          </div>

          {/* Mobile Sticky Price Summary */}
          <div className="lg:hidden">
            <PriceSummary
              onPayment={handleProceedToPayment}
              isSubmitting={createOrderMutation.isPending}
              numTickets={searchParams.numTickets || 1}
            />
          </div>
        </div>
      </Container>
    </div>
  );
}
