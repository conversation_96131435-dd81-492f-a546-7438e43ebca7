import {
  DeliveryTypeEnum,
  OrderStatusEnum,
  PaymentStatusEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";

export interface CreateOrderRequest {
  food_court_table_id: string;
  delivery_type: DeliveryTypeEnum;
  special_instructions?: string;
  payment_method: PaymentTypeEnum;
  items: OrderItemRequest[];
}

export interface OrderItemRequest {
  food_item_id: string;
  quantity: number;
  special_instructions?: string;
}

export interface CreateOrderResponse {
  id: string;
  user_id: number;
  food_court_table_id: string;
  order_number: string;
  delivery_type: string;
  special_instructions: string;
  total_amount: number;
  status: OrderStatusEnum;
  payment_status: PaymentStatusEnum;
  payment_method: string;
  ordered_at: string;
  estimated_ready_at: string;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}

// --- Food Court Order Details ---
export interface FoodCourtOrderDetailsResponse {
  orders: FoodCourtOrder[];
}

export interface FoodCourtOrder {
  id: string;
  order_number: string;
  delivery_type: string;
  special_instructions: string;
  total_amount: number;
  status: string;
  payment_status: string;
  payment_method: string;
  ordered_at: string;
  estimated_ready_at: string;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
  food_court_table: FoodCourtTable;
  merchant_orders: FoodCourtMerchantOrder[];
}

export interface FoodCourtTable {
  id: string;
  table_number: string;
}

export interface FoodCourtMerchantOrder {
  id: string;
  merchant_order_number: string;
  subtotal_amount: number;
  status: string;
  estimated_ready_at: string;
  ready_at: string;
  merchant: FoodCourtMerchantDetails;
  order_items: FoodCourtOrderItem[];
}

export interface FoodCourtMerchantDetails {
  id: string;
  name: string;
  description: string;
  cover_image: Record<string, unknown>;
  logo_image: Record<string, unknown>;
  contact_phone: string;
  contact_email: string;
  operating_hours_start: string;
  operating_hours_end: string;
}

export interface FoodCourtOrderItem {
  id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  special_instructions: string;
  food_item: FoodCourtOrderFoodItem;
}

export interface FoodCourtOrderFoodItem {
  id: string;
  name: string;
  description: string;
  price: number;
  preparation_time_minutes: number;
  food_category: FoodCourtOrderFoodCategory;
}

export interface FoodCourtOrderFoodCategory {
  id: string;
  name: string;
}
