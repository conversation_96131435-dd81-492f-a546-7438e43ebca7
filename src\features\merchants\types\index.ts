import type {
  FoodItem as Api<PERSON>oodItem,
  MenuCategory as ApiMenuCategory,
  FoodItemImage,
} from "@/api/merchants/food-court.types";

export type { Merchant } from "@/api/merchants/merchants.types";

// Extended types for UI
export interface MenuItem extends Omit<ApiFoodItem, "image_url"> {
  image_url: string;
  images?: FoodItemImage[];
}

export interface MenuCategory extends Omit<ApiMenuCategory, "food_items"> {
  food_items: MenuItem[];
}

export interface Restaurant {
  id: string;
  name: string;
  description: string;
  contact_phone: string;
  operating_hours_start: string;
  operating_hours_end: string;
  cover_image: string;
  logo_image: object | string;
  categories: MenuCategory[];
}

// Cart related types
export interface CartItem {
  id: string;
  merchantId: string;
  name: string;
  price: number;
  quantity: number;
  image_url: string;
  images?: FoodItemImage[];
  description?: string;
}

export interface CartData {
  restaurantId: string;
  restaurantName: string;
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
}

// Multiple restaurants cart data
export interface RestaurantCartData {
  restaurantId: string;
  restaurantName: string;
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
}

export interface MultipleRestaurantCartData {
  restaurants: RestaurantCartData[];
  totalItems: number;
  totalPrice: number;
}
