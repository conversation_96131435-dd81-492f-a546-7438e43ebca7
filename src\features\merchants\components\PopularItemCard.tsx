import type { Item } from "@/api/items/items.types";
import { useNavigate } from "@tanstack/react-router";

export default function PopularItemCard({ item }: { item: Item }) {
  const navigate = useNavigate();

  return (
    <div className="w-full flex justify-center">
      <div className="flex gap-4">
        <div>
          <div className="max-h-36 max-w-36 rounded-2xl mb-5 relative">
            <button
              onClick={() =>
                navigate({
                  to: "/merchants/update-items-detail/$idItem",
                  params: { idItem: item.id },
                })
              }
              className="rounded-lg bg-[#2D5BFF] w-8 h-8 text-xl absolute bottom-2 right-2"
            >
              <p className="text-center text-white">+</p>
            </button>
            <img
              className="max-h-36 max-w-36 rounded-2xl object-contain"
              src="assets/merchants/item1.png"
              alt="merchants Logo"
            />
          </div>

          <p className="text-sm text-[#181818]">{item.name}</p>
          <p className="text-sm font-bold text-[#181818]">{item.price}đ</p>
        </div>
      </div>
    </div>
  );
}
