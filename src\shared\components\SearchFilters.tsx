import React from "react";
import type { BusFilters } from "@/shared/types/bus";
import FilterSection from "@/components/filters/FilterSection";
import SortFilter from "@/components/filters/SortFilter";
import TimeFilter from "@/components/filters/TimeFilter";
import BusTypeFilter from "@/components/filters/BusTypeFilter";
import { useTranslation } from "@/shared/hooks/useTranslation";

interface SearchFiltersProps {
  onFiltersChange: (filters: BusFilters) => void;
  activeFilters: BusFilters;
  closeButton?: React.ReactNode;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  onFiltersChange,
  activeFilters,
  closeButton,
}) => {
  const { t } = useTranslation();

  const handleSortChange = (sortBy: typeof activeFilters.sortBy) => {
    onFiltersChange({ ...activeFilters, sortBy });
  };

  const handleTimeChange = (timeSlots: typeof activeFilters.timeSlots) => {
    onFiltersChange({ ...activeFilters, timeSlots });
  };

  const handleBusTypeChange = (busTypes: typeof activeFilters.busTypes) => {
    onFiltersChange({ ...activeFilters, busTypes });
  };

  return (
    <div className="w-full bg-white rounded-xl border border-[#EFEFEF] h-fit">
      {/* Header */}
      <div className="px-4 py-[14px] flex items-center justify-between">
        <h2 className="text-base font-mulish font-bold text-[#181818]">
          {t("filters.searchFilters")}
        </h2>
        {closeButton}
      </div>

      {/* Filter Content */}
      <div className="px-4 py-3 space-y-3">
        {/* Sort Section */}
        <FilterSection title={t("filters.sortLabel")}>
          <SortFilter
            value={activeFilters.sortBy}
            onChange={handleSortChange}
          />
        </FilterSection>

        {/* Divider */}
        <div className="h-px bg-[#EDEDED] w-full"></div>

        {/* Time Section */}
        <FilterSection title={t("filters.departureTime")}>
          <TimeFilter
            selectedTimes={activeFilters.timeSlots}
            onChange={handleTimeChange}
          />
        </FilterSection>

        {/* Divider */}
        <div className="h-px bg-[#EDEDED] w-full"></div>

        {/* Bus Type Section */}
        <FilterSection title={t("filters.busType")}>
          <BusTypeFilter
            selectedTypes={activeFilters.busTypes}
            onChange={handleBusTypeChange}
          />
        </FilterSection>
      </div>
    </div>
  );
};

export default SearchFilters;
