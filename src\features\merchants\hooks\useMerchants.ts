import { useQuery } from "@tanstack/react-query";
import { getAllMerchants, getMerchantById } from "@/api/merchants/mechants.api";
import type { MerchantsResponse } from "@/api/merchants/merchants.types";

// Get order by ID query
export const useGetMerchantById = (
  merchantId: string,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: ["merchant", merchantId],
    queryFn: () => getMerchantById(merchantId),
    enabled: enabled && !!merchantId,
  });
};

// Get all orders query
export const useGetAllMerchants = (params?: {
  page?: number;
  limit?: number;
}) => {
  return useQuery<MerchantsResponse>({
    queryKey: ["merchants", params],
    queryFn: () => getAllMerchants(params),
  });
};
