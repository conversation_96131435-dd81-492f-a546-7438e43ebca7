import { memo, useState } from "react";
import { TruckIcon } from "@heroicons/react/24/outline";

interface BusImageProps {
  src?: string;
  alt: string;
  className?: string;
}

const BusImage = memo(({ src, alt, className = "" }: BusImageProps) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [placeholderError, setPlaceholderError] = useState(false);

  const handleImageError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handlePlaceholderError = () => {
    setPlaceholderError(true);
  };

  const handlePlaceholderLoad = () => {
    // Placeholder loaded successfully
  };

  // If no src provided or error occurred, show placeholder
  if (!src || hasError) {
    if (placeholderError) {
      // Show icon fallback if even placeholder fails
      return (
        <div
          className={`w-full h-full bg-gray-100 flex items-center justify-center border border-gray-200 rounded ${className}`}
        >
          <div className="text-center p-4">
            <TruckIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <span className="text-xs text-gray-500">Xe buýt</span>
          </div>
        </div>
      );
    }

    return (
      <div
        className={`w-full h-full bg-gray-100 flex items-center justify-center border border-gray-200 rounded ${className}`}
      >
        <img
          src="/placeholder-bus.jpg"
          alt={alt}
          className="w-full h-full object-cover"
          onError={handlePlaceholderError}
          onLoad={handlePlaceholderLoad}
        />
      </div>
    );
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center border border-gray-200 rounded">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mx-auto mb-2"></div>
            <span className="text-xs text-gray-500">Đang tải...</span>
          </div>
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className="w-full h-full object-cover"
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: isLoading ? "none" : "block" }}
      />
    </div>
  );
});

BusImage.displayName = "BusImage";

export { BusImage };
