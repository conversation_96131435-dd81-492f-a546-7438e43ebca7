import React from "react";
import { Dialog, DialogContent } from "@/shared/components/dialog";

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  confirmText?: string;
  onConfirm?: () => void;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen,
  onClose,
  title = "Lỗi",
  confirmText = "Đóng",
  onConfirm,
}) => {
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    } else {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-white rounded-2xl shadow-[0px_0px_20px_0px_rgba(132,132,132,0.2)] p-8 max-w-[400px] w-full">
        <div className="flex flex-col items-start space-y-6">
          {/* Title */}
          <p className="text-lg font-bold text-[#181818]">{title}</p>

          {/* Message */}
          <p className="text-base text-[#666666] font-mulish mb-8 leading-6">
            Thông tin vé của bạn không được tìm thấy, vui lòng kiểm tra lại
            thông tin vừa nhập hoặc liên hệ Tổng đài{" "}
            <span className="text-blue-500">1900 xxx</span> để được hỗ trợ thêm.
          </p>

          {/* Confirm Button */}
          <button
            onClick={handleConfirm}
            className="w-full bg-[#ECF0FF] cursor-pointer text-blue-600 text-[16px] font-extrabold rounded-lg py-2.5 px-8 tracking-wide transition-colors duration-200"
          >
            {confirmText}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
