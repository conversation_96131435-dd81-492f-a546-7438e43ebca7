import { createFileRoute } from "@tanstack/react-router";
import { useMemo, useEffect } from "react";
import { SearchFormProvider } from "@/contexts/SearchFormContext";
import { SearchPageContent } from "@/features/booking/components/SearchPageContent";
import { searchPageSearchSchema } from "@/schemas/searchPageSchema";
import { useContactFormStore } from "@/stores/contactFormStore";

export const Route = createFileRoute("/booking/search")({
  validateSearch: searchPageSearchSchema,
  component: SearchPage,
});

function SearchPage() {
  const searchParams = Route.useSearch();
  const { isFormDataStale, clearFormData } = useContactFormStore();

  // Clear stale form data khi vào trang
  useEffect(() => {
    if (isFormDataStale()) {
      clearFormData();
    }
  }, [isFormDataStale, clearFormData]);

  // Prepare initial values for the search form
  const initialFormValues = useMemo(
    () => ({
      fromLocation: searchParams.fromPlaceName || "",
      toLocation: searchParams.toPlaceName || "",
      departureDate: searchParams.departureDate || "",
      returnDate: searchParams.returnDate || "",
      numTickets: searchParams.numTickets || 1,
      isRoundTrip: searchParams.isRoundTrip || false,
    }),
    [searchParams]
  );

  return (
    <SearchFormProvider variant="search-page" initialValues={initialFormValues}>
      <SearchPageContent searchParams={searchParams} />
    </SearchFormProvider>
  );
}

export default SearchPage;
