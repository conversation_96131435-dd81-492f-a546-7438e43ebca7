import React from "react";
import { PhoneIcon } from "@heroicons/react/16/solid";
import type { Restaurant } from "@/features/merchants/types";

interface RestaurantInfoProps {
  restaurant: Restaurant;
}

export const RestaurantInfo: React.FC<RestaurantInfoProps> = React.memo(
  ({ restaurant }) => {
    // const operatingStatus = getOperatingStatus(
    //   restaurant.operating_hours_start,
    //   restaurant.operating_hours_end
    // );

    return (
      <div className="px-4 pt-5">
        {/* Restaurant Name and Contact Info */}
        <div className="mb-1">
          <h1 className="text-xl font-bold text-[#181818] mb-2">
            {restaurant.name}
          </h1>

          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center gap-1">
              <PhoneIcon className="w-4 h-4 text-[#7C7B7B]" />
              <span className="text-sm font-normal text-[#181818]">
                {restaurant.contact_phone}
              </span>
            </div>

            {/* <span
              className="text-sm font-normal"
              style={{ color: operatingStatus.color }}
            >
              {operatingStatus.status}
            </span> */}
          </div>
        </div>
      </div>
    );
  }
);

RestaurantInfo.displayName = "RestaurantInfo";

// Add default export for better module resolution
export default RestaurantInfo;
