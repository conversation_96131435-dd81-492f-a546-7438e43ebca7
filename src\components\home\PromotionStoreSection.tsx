import React, { memo, useCallback, useMemo, useState } from "react";
import { CardCarousel, type CardItem } from "@/shared/components/CardCarousel";
import { Skeleton } from "@/shared/components/skeleton";
import { useDiscountFlow } from "@/features/booking/hooks/useDiscountFlow";
import { BaseCard } from "@/shared/components";
import { useNavigate } from "@tanstack/react-router";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

interface StorePromotionData extends CardItem {
  city: string;
}

// Mock data as fallback when API fails or returns empty
const STORE_PROMOTION_DATA: StorePromotionData[] = [
  {
    id: "1",
    image: "/mock-images/card-news.png",
    title:
      "TPBank | Nghỉ lễ vi vu, TPBank giảm giá ngay 30% khi đặt Vé xe khách",
    timeAgo: "2 giờ trước",
    city: "hanoi",
  },
  {
    id: "2",
    image: "/mock-images/card-news-1.png",
    title: "Mua Vé xe rẻ - Vui vẻ vi vu cùng My Viettel",
    timeAgo: "2 giờ trước",
    city: "hanoi",
  },
  {
    id: "3",
    image: "/mock-images/card-news-2.png",
    title:
      "Combo hè di chuyển: Giảm tới 200.000 đồng vé tàu - xe trên Agribank Plus",
    timeAgo: "3 ngày trước",
    city: "hanoi",
  },
  {
    id: "4",
    image: "/mock-images/card-news-3.png",
    title:
      "Nhận hoàn tiền lên đến 50% khi đặt vé xe khách trên ứng dụng Agribank E-Mobile",
    timeAgo: "21/05/2025",
    city: "hanoi",
  },
  {
    id: "5",
    image: "/mock-images/card-news-2.png",
    title:
      "Combo hè di chuyển: Giảm tới 200.000 đồng vé tàu - xe trên Agribank Plus",
    timeAgo: "3 ngày trước",
    city: "hcm",
  },
  {
    id: "6",
    image: "/mock-images/card-news-3.png",
    title:
      "Nhận hoàn tiền lên đến 50% khi đặt vé xe khách trên ứng dụng Agribank E-Mobile",
    timeAgo: "21/05/2025",
    city: "danang",
  },
];

// Store Promotion Card Component
const StorePromotionCard: React.FC<{
  id: string;
  image: string;
  title: string;
  timeAgo: string;
  onClick?: () => void;
}> = memo(({ id, image, title, timeAgo, onClick }) => {
  return (
    <BaseCard
      id={id}
      image={image}
      title={title}
      timeAgo={timeAgo}
      onClick={onClick}
      borderColor="EDEDED"
      titleSize="14px"
      titleLines={4}
      className="bg-white"
    />
  );
});

StorePromotionCard.displayName = "StorePromotionCard";

// Custom Title Component with City Tabs
const CustomTitleWithTabs: React.FC<{
  cities: Array<{ id: string; name: string; place_id: number }>;
  selectedCity: string;
  onCityChange: (cityId: string, placeId: number) => void;
  isLoading?: boolean;
}> = memo(({ cities, selectedCity, onCityChange, isLoading }) => {
  return (
    <div className="flex-col gap-3 items-center">
      <h2 className="text-[#181818] font-mulish font-extrabold text-[20px] sm:text-[20px] md:text-[24px] lg:text-[32px] leading-[1.2]">
        Ưu đãi từ nhà xe
      </h2>

      {/* City Tabs */}
      <div className="flex">
        {isLoading ? (
          <div className="flex gap-2">
            <Skeleton className="w-20 h-8" />
            <Skeleton className="w-24 h-8" />
            <Skeleton className="w-20 h-8" />
          </div>
        ) : (
          cities.map((city) => (
            <button
              key={city.id}
              onClick={() => onCityChange(city.id, city.place_id)}
              className={`px-3 sm:px-4 py-2 text-[14px] font-bold transition-colors relative ${
                selectedCity === city.id
                  ? "text-[#181818]"
                  : "text-[#5C5C5C] hover:text-[#181818]"
              }`}
            >
              {city.name}
              {selectedCity === city.id && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#2D5BFF] rounded-t"></div>
              )}
            </button>
          ))
        )}
      </div>
    </div>
  );
});

CustomTitleWithTabs.displayName = "CustomTitleWithTabs";

export const PromotionStoreSection: React.FC = memo(() => {
  const navigate = useNavigate();
  const [selectedCity, setSelectedCity] = useState("hanoi");
  const [selectedPlaceId, setSelectedPlaceId] = useState<number | undefined>();

  // Fetch discount places and operators
  const {
    discountPlaces,
    discountBusOperators,
    isLoadingPlaces,
    isLoadingOperators,
  } = useDiscountFlow(selectedPlaceId);

  // Transform discount places to city tabs format
  const cities = useMemo(() => {
    if (!discountPlaces || discountPlaces.length === 0) {
      // Fallback cities when API fails
      return [
        { id: "hanoi", name: "Hà Nội", place_id: 0 },
        { id: "hcm", name: "Tp.Hồ Chí Minh", place_id: 0 },
        { id: "danang", name: "Đà Nẵng", place_id: 0 },
      ];
    }

    return discountPlaces.map((place) => ({
      id: place.place_id.toString(),
      name: place.name,
      place_id: place.place_id,
    }));
  }, [discountPlaces]);

  // Set default selected place when places are loaded
  React.useEffect(() => {
    if (cities.length > 0 && !selectedPlaceId) {
      const firstCity = cities[0];
      setSelectedCity(firstCity.id);
      if (firstCity.place_id > 0) {
        setSelectedPlaceId(firstCity.place_id);
      }
    }
  }, [cities, selectedPlaceId]);

  // Transform bus operators to store promotion format
  const filteredPromotions: StorePromotionData[] = useMemo(() => {
    if (!discountBusOperators || discountBusOperators.length === 0) {
      // Use mock data filtered by selected city when API fails
      return STORE_PROMOTION_DATA.filter(
        (promo) => promo.city === selectedCity
      );
    }

    return discountBusOperators.map((operator) => ({
      id: operator.id.toString(),
      image: getStrapiImageUrl(
        operator.image_background,
        "/mock-images/card-news.png"
      ),
      title: operator.title,
      timeAgo: new Date(operator.created_at).toLocaleDateString("vi-VN"),
      city: selectedCity,
    }));
  }, [discountBusOperators, selectedCity]);

  const handleCityChange = useCallback((cityId: string, placeId: number) => {
    setSelectedCity(cityId);
    if (placeId > 0) {
      setSelectedPlaceId(placeId);
    } else {
      setSelectedPlaceId(undefined);
    }
  }, []);

  const handlePromotionClick = useCallback(
    (promotionId: string) => {
      navigate({ to: "/store-promotion/$id", params: { id: promotionId } });
    },
    [navigate]
  );

  const renderStorePromotionCard = useCallback(
    (item: CardItem) => {
      return (
        <StorePromotionCard
          id={item.id}
          image={item.image}
          title={item.title}
          timeAgo={item.timeAgo}
          onClick={() => handlePromotionClick(item.id)}
        />
      );
    },
    [handlePromotionClick]
  );

  return (
    <CardCarousel
      className="mt-10"
      title={
        <CustomTitleWithTabs
          cities={cities}
          selectedCity={selectedCity}
          onCityChange={handleCityChange}
          isLoading={isLoadingPlaces}
        />
      }
      items={filteredPromotions}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 4,
      }}
      renderCard={renderStorePromotionCard}
      isLoading={isLoadingOperators}
    />
  );
});

PromotionStoreSection.displayName = "PromotionStoreSection";
