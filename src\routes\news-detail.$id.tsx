import { createFileRoute, useParams } from "@tanstack/react-router";
import { Container } from "@/shared/components/Container";
import { MarkdownContent } from "@/shared/components/MarkdownContent";
import { useArticleById } from "@/shared/hooks/useArticles";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";
import { LoadingSpinner } from "@/shared/components/LoadingSpinner";

export const Route = createFileRoute("/news-detail/$id")({
  component: NewsDetail,
});

function NewsDetail() {
  const { id } = useParams({ from: "/news-detail/$id" });
  const { data: article, isLoading } = useArticleById(id);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <Container>
          <div className="text-center py-20">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Không tìm thấy bài viết
            </h1>
            <p className="text-gray-600 mb-6">
              Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
            </p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-2 lg:pb-20 lg:pt-5">
      <Container>
        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl lg:text-3xl font-bold text-[#181818] mb-4 leading-tight">
              {article.title}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>
                Ngày xuất bản:{" "}
                {new Date(article.published_at).toLocaleDateString("vi-VN")}
              </span>
              <span>
                Ngày cập nhật:{" "}
                {new Date(article.updated_at).toLocaleDateString("vi-VN")}
              </span>
            </div>
          </div>

          {/* Main Image */}
          <div className="relative w-full h-64 lg:h-96 bg-gray-200">
            <img
              src={getStrapiImageUrl(
                article.image_background,
                "/mock-images/image-2.png"
              )}
              alt={article.title}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Content Body */}
          <div className="p-6">
            {/* Main Content */}
            {article.content && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Nội dung bài viết
                </h2>
                <MarkdownContent content={article.content} />
              </div>
            )}

            {/* Published Date */}
            {article.published_at && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thời gian xuất bản
                </h2>
                <p className="text-gray-700">
                  {new Date(article.published_at).toLocaleDateString("vi-VN")}
                </p>
              </div>
            )}

            {/* Created Date */}
            {article.created_at && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Ngày tạo
                </h2>
                <p className="text-gray-700">
                  {new Date(article.created_at).toLocaleDateString("vi-VN")}
                </p>
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
}
