import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { DocumentArrowDownIcon, HomeIcon } from "@heroicons/react/24/outline";
import NoScanInfoSection from "@/components/merchants-order-confirmation/NoScanInfoSection";
import { formatPrice } from "@/lib/utils";
import React, { useEffect, useState, useMemo } from "react";
import { VietQRSteps } from "@/components/merchants-order-confirmation/VietQRPopup";
import {
  simpleCartActions,
  simpleCartSelectors,
} from "@/stores/simpleCartStore";

// Mock data
const vietQRData = {
  accountNumber: "**********",
  accountName: "CHUTAIKHOAN",
  bank: "VIETINBANK",
  qrImage: "/assets/qr-fake.png",
  content: "663FGCS",
};

export const Route = createFileRoute("/merchants/check-payment-status")({
  component: CheckPaymentStatusPage,
  validateSearch: (search: Record<string, unknown>) => ({
    qr: typeof search.qr === "string" ? search.qr : undefined,
    expiry: typeof search.expiry === "string" ? search.expiry : undefined,
  }),
});

function formatCountdown(expiry: string | undefined) {
  if (!expiry) return "";
  const expiryDate = new Date(expiry);
  const now = new Date();
  const diff = expiryDate.getTime() - now.getTime();
  if (diff <= 0) return "Đã hết hạn";
  const minutes = Math.floor(diff / 60000);
  const seconds = Math.floor((diff % 60000) / 1000);
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
}

function formatExpiryText(expiry: string | undefined) {
  if (!expiry) return "";
  const d = new Date(expiry);
  const h = d.getHours().toString().padStart(2, "0");
  const m = d.getMinutes().toString().padStart(2, "0");
  const weekday = [
    "Chủ nhật",
    "Thứ 2",
    "Thứ 3",
    "Thứ 4",
    "Thứ 5",
    "Thứ 6",
    "Thứ 7",
  ][d.getDay()];
  const day = d.getDate();
  const month = d.getMonth() + 1;
  return `Mã sẽ hết hạn vào lúc ${h}:${m} ${weekday}, ${day}/${month}`;
}

// DotLoader animation component
const DotLoader: React.FC = () => {
  const [active, setActive] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      setActive((prev) => (prev + 1) % 3);
    }, 300);
    return () => clearInterval(interval);
  }, []);
  return (
    <div className="flex gap-1 items-end h-6">
      {[0, 1, 2].map((i) => (
        <span
          key={i}
          className={`block w-2 h-2 rounded-full bg-[#2D5BFF] transition-transform duration-200 ${
            active === i ? "-translate-y-1 scale-110" : "opacity-60"
          }`}
          style={{
            transform: active === i ? "translateY(-6px) scale(1.2)" : "none",
          }}
        />
      ))}
    </div>
  );
};

function CheckPaymentStatusPage() {
  const navigate = useNavigate();

  // Extract qr and expiry param from search
  const { qr, expiry } = Route.useSearch();

  // Countdown state
  const [countdown, setCountdown] = useState<string>("");

  useEffect(() => {
    if (!expiry) return;
    const updateCountdown = () => {
      setCountdown(formatCountdown(expiry));
    };
    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);
    return () => {
      clearInterval(interval);
    };
  }, [expiry]);

  // Memoized expiry text for VietQR section
  const expiryText = useMemo(() => formatExpiryText(expiry), [expiry]);

  return (
    <div className="min-h-screen bg-[#F8F8F8] pb-28 flex flex-col">
      {/* Verifying Payment Section */}
      <div className="bg-[#2D5BFF] flex flex-col items-center py-6 px-4">
        <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mb-3">
          <DotLoader />
        </div>
        <div className="text-white text-lg font-bold mb-1">
          Đang xác minh thanh toán
        </div>
        <div className="text-white text-sm text-center opacity-80">
          Nếu chưa chuyển khoản, bạn vui lòng làm theo Hướng dẫn thanh toán để
          hoàn tất giao dịch
        </div>
      </div>
      {/* Red warning if payment not successful */}
      <div className="bg-[#FEF5F5] text-[#FF3030] text-sm font-medium mt-2 mx-4 py-2 text-center">
        Nếu giao dịch không thành công, vé sẽ huỷ sau{" "}
        <span className="font-bold">{countdown || "--:--"}</span>
      </div>
      {/* Payment Info Card */}
      <div className="bg-white rounded-xl border border-[#EDEDED] mx-4 mt-2 p-4 flex flex-col gap-2">
        <div className="text-base font-bold text-[#181818] mb-2">
          Thông tin thanh toán
        </div>
        <div className="flex justify-between items-center text-sm">
          <span className="text-[#5C5C5C]">Trạng thái</span>
          <span className="text-[#2D5BFF] font-bold">Đang xác minh</span>
        </div>
        <div className="flex justify-between items-center text-sm">
          <span className="text-[#5C5C5C]">Phương thức thanh toán</span>
          <span className="text-[#181818] font-semibold">
            Chuyển khoản Online
          </span>
        </div>
        <div className="flex justify-between items-center text-sm">
          <span className="text-[#5C5C5C]">Tổng tiền</span>
          <span className="text-[#181818] font-bold">
            {formatPrice(
              simpleCartSelectors
                .getAllItems()
                .reduce((sum, item) => sum + item.price * item.quantity, 0)
            )}
            đ
          </span>
        </div>
      </div>
      {/* Orange warning */}
      <div className="bg-[#FFF0D6] border border-[#FF7F37] rounded-xl mx-4 mt-3 p-3 flex items-start gap-2 text-[#5C5C5C] text-xs">
        <span className="mt-0.5">
          <svg width="16" height="16" fill="none" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="8" fill="#FF7F37" />
            <path
              d="M8 4.667V8.667"
              stroke="#fff"
              strokeWidth="1.2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <circle cx="8" cy="11.333" r="0.667" fill="#fff" />
          </svg>
        </span>
        <span>
          Nếu giao dịch chưa được xác minh trong vòng 5 phút sau khi chuyển
          khoản, vui lòng liên hệ{" "}
          <span className="font-bold text-[#2D5BFF]">190088888</span> để được xử
          lý kịp thời.
        </span>
      </div>
      {/* VietQR Section (reuse UI) */}
      <div className="bg-white rounded-2xl shadow-xl p-0 flex flex-col relative w-auto mx-auto mt-4">
        <div className="pt-6 px-6 text-center">
          <h2 className="text-[18px] font-bold text-[#181818] leading-tight">
            Thanh toán Online
          </h2>
          {expiryText && (
            <div className="mt-2 text-center">
              <span className="inline-block rounded bg-[#FEF5F5] text-xs text-[#FF3030] font-normal px-3 py-1">
                {expiryText}
              </span>
            </div>
          )}
        </div>
        <div className="flex flex-row gap-4 mt-4 px-6 justify-center">
          {/* Steps */}
          <VietQRSteps className="py-2 px-4 w-[160px]" />
          {/* QR + Logo */}
          <div className="flex flex-col items-center justify-center bg-white border-4 border-[#2D5BFF] rounded-xl shadow w-full h-[240px] relative p-4">
            <img
              src={vietQRData.qrImage}
              alt="VietQR"
              className="w-full h-full object-contain rounded-lg bg-gray-100"
            />
          </div>
        </div>
        {/* Download Button (secondary) */}
        <div className="flex justify-center mt-4 px-6">
          <button
            className="w-full flex items-center justify-center gap-2 bg-[#ECF0FF] text-[#2D5BFF] font-extrabold rounded-xl py-3 text-base shadow-sm hover:bg-[#ECF0FF] focus:outline-none focus:ring-2 focus:ring-[#2D5BFF] transition-colors"
            onClick={() => {
              // Download QR image
              const link = document.createElement("a");
              link.href = vietQRData.qrImage;
              link.download = "vietqr.png";
              link.click();
            }}
            aria-label="Lưu mã QR về máy"
            type="button"
          >
            <DocumentArrowDownIcon className="w-5 h-5 text-[#2D5BFF]" />
            Lưu mã QR về máy
          </button>
        </div>
        {/* Info Card - NoScanInfoSection */}
        <div className="mt-4 mb-6 px-6">
          <NoScanInfoSection
            bank={vietQRData.bank}
            accountNumber={vietQRData.accountNumber}
            accountName={vietQRData.accountName}
            amount={simpleCartSelectors
              .getAllItems()
              .reduce((sum, item) => sum + item.price * item.quantity, 0)}
            content={vietQRData.content}
            onCopyAccount={() => {}}
            onCopyAmount={() => {}}
            onCopyContent={() => {}}
          />
        </div>
      </div>
      {/* Bottom Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/90 rounded-t-2xl shadow-[0_0_20px_0_rgba(132,132,132,0.2)] px-4 py-4 flex gap-3 z-50">
        <button
          className="flex-1 flex items-center justify-center gap-2 bg-white border border-[#2D5BFF] text-[#2D5BFF] font-extrabold rounded-xl py-3 px-2 shadow-sm hover:bg-[#ECF0FF] focus:outline-none focus:ring-2 focus:ring-[#2D5BFF] transition-colors"
          onClick={() => {
            navigate({
              to: "/merchants/orders",
              search: { qr: qr ?? undefined },
            });
            simpleCartActions.clearCart();
          }}
          aria-label="Đơn hàng"
          type="button"
        >
          <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
            <path
              d="M4.167 6.667A2.5 2.5 0 0 1 6.667 4.167h6.666a2.5 2.5 0 0 1 2.5 2.5v8.333a2.5 2.5 0 0 1-2.5 2.5H6.667a2.5 2.5 0 0 1-2.5-2.5V6.667Z"
              stroke="#2D5BFF"
              strokeWidth="1.5"
            />
            <path
              d="M7.5 9.167h5M7.5 12.5h2.5"
              stroke="#2D5BFF"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
          Đơn hàng
        </button>
        <button
          className="flex-1 flex items-center justify-center gap-2 bg-[#2D5BFF] text-white font-extrabold rounded-xl py-3 px-2 shadow-sm hover:bg-[#1e40af] focus:outline-none focus:ring-2 focus:ring-[#2D5BFF] transition-colors"
          onClick={() => {
            navigate({ to: "/merchants", search: { qr: qr ?? undefined } });
            simpleCartActions.clearCart();
          }}
          aria-label="Về trang chủ"
          type="button"
        >
          <HomeIcon className="w-5 h-5 text-white" />
          Về trang chủ
        </button>
      </div>
    </div>
  );
}
