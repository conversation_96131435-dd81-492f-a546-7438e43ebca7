# Food Court Creation Flow - Frontend Developer Guide

## 🎯 Overview

This document provides a complete guide for implementing the food court creation flow in the frontend application. It covers the API endpoints, data flow, and implementation examples for creating merchants, food categories, and food items.

## 📊 Creation Flow Diagram

```mermaid
flowchart TD
    A[Start: Create Food Court Setup] --> B{Merchants Exist?}

    B -->|No| C[Create Merchants First]
    B -->|Yes| D{Food Categories Exist?}

    C --> C1[POST /food-court/merchants]
    C1 --> C2[Required: name, description, contact_phone, operating_hours]
    C2 --> C3[Optional: cover_image, logo_image uploads]
    C3 --> D

    D -->|No| E[Create Food Categories]
    D -->|Yes| F[Ready to Create Food Items]

    E --> E1[POST /food-court/food-categories]
    E1 --> E2[Required: name]
    E2 --> E3[Optional: description, icon_url, color_code, display_order]
    E3 --> F

    F --> G[Create Food Items]
    G --> G1[POST /food-court/food-items]
    G1 --> G2[Required Fields]
    G2 --> G3[merchant_id: UUID<br/>food_category_id: UUID<br/>name: string<br/>price: number]

    G1 --> G4[Optional Fields]
    G4 --> G5[description: string<br/>image_url: string<br/>is_available: boolean<br/>preparation_time_minutes: number<br/>display_order: number]

    G3 --> H[Food Item Created Successfully]
    G5 --> H

    H --> I{Add Images?}
    I -->|Yes| J[POST /food-court/food-items/:id/images]
    I -->|No| K[Food Item Setup Complete]
    J --> J1[Upload multiple images with different sizes]
    J1 --> K

    K --> L{Create More Items?}
    L -->|Yes| G
    L -->|No| M[Food Court Menu Ready]

    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style G1 fill:#fff3e0
    style C1 fill:#fce4ec
    style E1 fill:#f3e5f5
```

## 📋 API Reference

### Base URL
```
https://your-api-domain.com/api/v1
```

### Prerequisites Setup Order:
1. **Merchants** (must exist first)
2. **Food Categories** (must exist first)
3. **Food Items** (requires both merchant_id and food_category_id)

---

## 1. 🏪 Merchants Management

### Endpoints

| Method | Endpoint | Purpose | Required Fields |
|--------|----------|---------|----------------|
| `GET` | `/food-court/merchants` | List all merchants | None |
| `POST` | `/food-court/merchants` | Create merchant | `name`, `description`, `contact_phone`, `operating_hours_start`, `operating_hours_end` |
| `GET` | `/food-court/merchants/:id` | Get single merchant | `id` (UUID) |
| `PATCH` | `/food-court/merchants/:id` | Update merchant | Any merchant fields |

### Create Merchant Example

**Request:**
```http
POST /api/v1/food-court/merchants
Content-Type: application/json

{
  "name": "Pho House",
  "description": "Traditional Vietnamese cuisine",
  "contact_phone": "+84123456789",
  "operating_hours_start": "08:00",
  "operating_hours_end": "22:00"
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Pho House",
  "description": "Traditional Vietnamese cuisine",
  "contact_phone": "+84123456789",
  "operating_hours_start": "08:00",
  "operating_hours_end": "22:00",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

---

## 2. 🏷️ Food Categories Management

### Endpoints

| Method | Endpoint | Purpose | Required Fields |
|--------|----------|---------|----------------|
| `GET` | `/food-court/food-categories` | List all categories | None |
| `POST` | `/food-court/food-categories` | Create category | `name` |
| `GET` | `/food-court/food-categories/:id` | Get single category | `id` (UUID) |
| `PATCH` | `/food-court/food-categories/:id` | Update category | Any category fields |
| `DELETE` | `/food-court/food-categories/:id` | Delete category | `id` (UUID) |

### Create Food Category Example

**Request:**
```http
POST /api/v1/food-court/food-categories
Content-Type: application/json

{
  "name": "Main Dishes",
  "description": "Primary meals and entrees",
  "icon_url": "https://example.com/icons/main-dishes.png",
  "color_code": "#FF5722",
  "display_order": 1,
  "is_active": true
}
```

**Response:**
```json
{
  "id": "456e7890-e89b-12d3-a456-************",
  "name": "Main Dishes",
  "description": "Primary meals and entrees",
  "icon_url": "https://example.com/icons/main-dishes.png",
  "color_code": "#FF5722",
  "display_order": 1,
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `isActive` | boolean | Filter by active status |

**Example:**
```http
GET /api/v1/food-court/food-categories?isActive=true
```

---

## 3. 🍜 Food Items Management

### Endpoints

| Method | Endpoint | Purpose | Required Fields |
|--------|----------|---------|----------------|
| `GET` | `/food-court/food-items` | List all food items | None |
| `POST` | `/food-court/food-items` | Create food item | `merchant_id`, `food_category_id`, `name`, `price` |
| `GET` | `/food-court/food-items/:id` | Get single food item | `id` (UUID) |
| `PATCH` | `/food-court/food-items/:id` | Update food item | Any food item fields |
| `DELETE` | `/food-court/food-items/:id` | Delete food item | `id` (UUID) |

### Create Food Item Example

**Request:**
```http
POST /api/v1/food-court/food-items
Content-Type: application/json

{
  "merchant_id": "123e4567-e89b-12d3-a456-************",
  "food_category_id": "456e7890-e89b-12d3-a456-************",
  "name": "Pho Bo",
  "description": "Traditional Vietnamese beef noodle soup",
  "price": 85000,
  "is_available": true,
  "preparation_time_minutes": 15,
  "display_order": 1
}
```

**Response:**
```json
{
  "id": "789a0123-e89b-12d3-a456-************",
  "merchant_id": "123e4567-e89b-12d3-a456-************",
  "food_category_id": "456e7890-e89b-12d3-a456-************",
  "name": "Pho Bo",
  "description": "Traditional Vietnamese beef noodle soup",
  "price": 85000,
  "is_available": true,
  "preparation_time_minutes": 15,
  "display_order": 1,
  "images": null,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `merchantId` | UUID | Filter by merchant ID |
| `categoryId` | UUID | Filter by category ID |
| `isAvailable` | boolean | Filter by availability |

**Examples:**
```http
GET /api/v1/food-court/food-items?merchantId=123e4567-e89b-12d3-a456-************
GET /api/v1/food-court/food-items?categoryId=456e7890-e89b-12d3-a456-************
GET /api/v1/food-court/food-items?isAvailable=true
```

---

## 4. 📸 Food Item Images Management

### Endpoints

| Method | Endpoint | Purpose | Required Fields |
|--------|----------|---------|----------------|
| `POST` | `/food-court/food-items/:id/images` | Upload food item image | `file` (multipart) |
| `GET` | `/food-court/food-items/:id/images` | Get all food item images | `id` (UUID) |
| `DELETE` | `/food-court/food-items/:id/images/:imageId` | Delete specific image | `id`, `imageId` (UUIDs) |
| `DELETE` | `/food-court/food-items/:id/images` | Delete all images | `id` (UUID) |
| `PATCH` | `/food-court/food-items/:id/images/reorder` | Reorder images | `imageIds` array |

### Upload Image Example

**Request:**
```http
POST /api/v1/food-court/food-items/789a0123-e89b-12d3-a456-************/images
Content-Type: multipart/form-data

file: [image file]
generateWebP: true
```

**Response:**
```json
{
  "id": "img_123e4567-e89b-12d3-a456-************",
  "originalName": "pho-bo.jpg",
  "sizes": {
    "thumbnail": "https://storage.com/images/thumbnail_pho-bo.jpg",
    "small": "https://storage.com/images/small_pho-bo.jpg",
    "medium": "https://storage.com/images/medium_pho-bo.jpg",
    "large": "https://storage.com/images/large_pho-bo.jpg",
    "original": "https://storage.com/images/original_pho-bo.jpg"
  },
  "uploadedAt": "2024-01-01T00:00:00Z"
}
```

---

## 💻 Frontend Implementation Examples

### 1. Setup Phase - Check Prerequisites

```javascript
// Check if merchants exist
const checkMerchants = async () => {
  try {
    const response = await fetch('/api/v1/food-court/merchants');
    const merchants = await response.json();
    return merchants.length > 0 ? merchants : null;
  } catch (error) {
    console.error('Error fetching merchants:', error);
    return null;
  }
};

// Check if categories exist
const checkCategories = async () => {
  try {
    const response = await fetch('/api/v1/food-court/food-categories');
    const categories = await response.json();
    return categories.length > 0 ? categories : null;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return null;
  }
};
```

### 2. Create Merchant

```javascript
const createMerchant = async (merchantData) => {
  try {
    const response = await fetch('/api/v1/food-court/merchants', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(merchantData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating merchant:', error);
    throw error;
  }
};

// Usage
const newMerchant = await createMerchant({
  name: "Pho House",
  description: "Traditional Vietnamese cuisine",
  contact_phone: "+84123456789",
  operating_hours_start: "08:00",
  operating_hours_end: "22:00"
});
```

### 3. Create Food Category

```javascript
const createFoodCategory = async (categoryData) => {
  try {
    const response = await fetch('/api/v1/food-court/food-categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating food category:', error);
    throw error;
  }
};

// Usage
const newCategory = await createFoodCategory({
  name: "Main Dishes",
  description: "Primary meals and entrees",
  color_code: "#FF5722",
  display_order: 1
});
```

### 4. Create Food Item

```javascript
const createFoodItem = async (foodItemData) => {
  try {
    const response = await fetch('/api/v1/food-court/food-items', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(foodItemData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating food item:', error);
    throw error;
  }
};

// Usage
const newFoodItem = await createFoodItem({
  merchant_id: selectedMerchant.id,
  food_category_id: selectedCategory.id,
  name: "Pho Bo",
  description: "Traditional Vietnamese beef noodle soup",
  price: 85000,
  preparation_time_minutes: 15
});
```

### 5. Upload Food Item Images

```javascript
const uploadFoodItemImage = async (foodItemId, imageFile) => {
  try {
    const formData = new FormData();
    formData.append('file', imageFile);
    formData.append('generateWebP', 'true');

    const response = await fetch(`/api/v1/food-court/food-items/${foodItemId}/images`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Usage
const imageUploadResult = await uploadFoodItemImage(newFoodItem.id, selectedImageFile);
```

### 6. Complete Flow Implementation

```javascript
const setupFoodCourtItem = async (formData) => {
  try {
    // 1. Ensure merchant exists
    let merchant = formData.merchant;
    if (!merchant.id) {
      merchant = await createMerchant(merchant);
    }

    // 2. Ensure category exists
    let category = formData.category;
    if (!category.id) {
      category = await createFoodCategory(category);
    }

    // 3. Create food item
    const foodItem = await createFoodItem({
      merchant_id: merchant.id,
      food_category_id: category.id,
      name: formData.name,
      description: formData.description,
      price: formData.price,
      preparation_time_minutes: formData.preparationTime
    });

    // 4. Upload images if provided
    if (formData.images && formData.images.length > 0) {
      const imageUploadPromises = formData.images.map(image =>
        uploadFoodItemImage(foodItem.id, image)
      );
      await Promise.all(imageUploadPromises);
    }

    return foodItem;

  } catch (error) {
    console.error('Error in complete setup flow:', error);
    throw error;
  }
};
```

---

## ⚠️ Important Notes for Frontend

### Data Types & Validation

- **UUID Format**: All IDs are UUIDs (e.g., `123e4567-e89b-12d3-a456-************`), not integers
- **Required Dependencies**: Food items cannot be created without valid `merchant_id` and `food_category_id`
- **Price Format**: Prices are in VND (Vietnamese Dong), stored as numbers (e.g., 85000)
- **Time Format**: Operating hours use 24-hour format (e.g., "08:00", "22:00")
- **Color Codes**: Must be in hex format (e.g., "#FF5722")

### Image Upload Specifications

- **Supported Formats**: JPEG, PNG, WebP
- **Upload Method**: Uses multipart/form-data
- **Generated Sizes**: thumbnail, small, medium, large, original
- **WebP Generation**: Optional, controlled by `generateWebP` parameter

### Error Handling

- **HTTP Status Codes**:
  - `200` - Success
  - `201` - Created
  - `400` - Bad Request (validation errors)
  - `404` - Not Found
  - `409` - Conflict (duplicate entries)

### Best Practices

1. **Always validate relationships** before creating food items
2. **Handle errors gracefully** with user-friendly messages
3. **Show loading states** during API calls
4. **Implement optimistic updates** where appropriate
5. **Cache merchant and category data** to reduce API calls

---

## 🧪 Test Endpoints

### Health Check
```http
GET /api/v1/food-court/admin/test
GET /api/v1/food-court/food-categories/admin/test
```

These endpoints return a simple success message to verify the API is working correctly.

---

## 📚 Additional Resources

- API Base URL: `https://your-api-domain.com/api/v1`
- Swagger Documentation: `https://your-api-domain.com/api/docs`
- Support: Contact backend team for API issues

---

*Last Updated: 2024-06-05*
*Version: 1.0*