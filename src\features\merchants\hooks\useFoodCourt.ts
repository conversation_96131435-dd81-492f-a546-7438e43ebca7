import { useQuery } from "@tanstack/react-query";
import type { UseQueryOptions } from "@tanstack/react-query";
import {
  getMerchantMenu,
  getMerchantMenuById,
} from "@/api/merchants/mechants.api";
import type {
  FoodCourtMenuResponse,
  GetMerchantMenuParams,
  FoodCourtApiError,
} from "@/api/merchants/food-court.types";

// Query Keys
export const foodCourtQueryKeys = {
  all: ["food-court"] as const,
  merchants: () => [...foodCourtQueryKeys.all, "merchants"] as const,
  merchant: (id: string) => [...foodCourtQueryKeys.merchants(), id] as const,
  menu: (merchantId: string) =>
    [...foodCourtQueryKeys.merchant(merchantId), "menu"] as const,
};

/**
 * Hook to fetch merchant menu with categories and food items
 * @param params - Parameters containing merchantId
 * @param options - React Query options
 * @returns UseQuery result with menu data
 */
export const useGetMerchantMenu = (
  params: GetMerchantMenuParams,
  options?: Omit<
    UseQueryOptions<FoodCourtMenuResponse, FoodCourtApiError>,
    "queryKey" | "queryFn"
  >
) => {
  return useQuery({
    queryKey: foodCourtQueryKeys.menu(params.merchantId),
    queryFn: () => getMerchantMenu(params),
    enabled: !!params.merchantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook to fetch merchant menu by ID (convenience hook)
 * @param merchantId - The merchant ID
 * @param options - React Query options
 * @returns UseQuery result with menu data
 */
export const useGetMerchantMenuById = (
  merchantId: string,
  options?: Omit<
    UseQueryOptions<FoodCourtMenuResponse, FoodCourtApiError>,
    "queryKey" | "queryFn"
  >
) => {
  return useQuery({
    queryKey: foodCourtQueryKeys.menu(merchantId),
    queryFn: () => getMerchantMenuById(merchantId),
    enabled: !!merchantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};
