import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

i18n
  // Load translation using http backend
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    lng: "vi", // Force default language to Vietnamese
    fallbackLng: "vi",
    preload: ["vi", "en"], // Preload both languages
    debug: import.meta.env.DEV,

    // Language detection options - only from localStorage, ignore browser settings
    detection: {
      order: ["localStorage"], // Only check localStorage, ignore navigator/htmlTag
      caches: ["localStorage"],
      lookupLocalStorage: "i18nextLng",
    },

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    // Default namespace
    defaultNS: "common",
    ns: ["common"],

    // Backend options for loading translations
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },

    // React options
    react: {
      useSuspense: true, // Re-enable suspense
    },
  });

// Debug language changes
if (import.meta.env.DEV) {
  i18n.on("languageChanged", () => {
    // Removed console.log("🌍 Language changed to:", lng);
  });

  i18n.on("initialized", () => {
    // Removed console.log("🌍 i18n initialized with language:", i18n.language);
  });

  i18n.on("loaded", () => {
    // Removed console.log("🌍 Resources loaded:", loaded);
  });

  i18n.on("failedLoading", (lng, ns, msg) => {
    console.error("❌ Failed to load:", lng, ns, msg);
  });
}

// Ensure default language is Vietnamese for new users
i18n.on("initialized", () => {
  // If no language preference is stored, ensure it's set to Vietnamese
  const storedLang = localStorage.getItem("i18nextLng");
  if (!storedLang) {
    localStorage.setItem("i18nextLng", "vi");
    if (i18n.language !== "vi") {
      i18n.changeLanguage("vi");
    }
  }
});

export default i18n;
