import React from "react";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

export const MerchantsOrderSuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const { qr } = Route.useSearch();

  const handleBackToHome = () => {
    navigate({ to: "/merchants", search: { qr: qr ?? "" } });
  };

  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center px-6">
      <div className="flex flex-col items-center text-center space-y-6">
        {/* Success Icon */}
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircleIcon className="w-12 h-12 text-green-600" />
        </div>

        {/* Success Message */}
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">
            Đặt hàng thành công!
          </h1>
          <p className="text-gray-600">
            Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ xử lý đơn hàng của bạn sớm nhất
            có thể.
          </p>
        </div>

        {/* Action Button */}
        <button
          onClick={handleBackToHome}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Về trang chủ
        </button>
      </div>
    </div>
  );
};

export const Route = createFileRoute("/merchants/order-success")({
  component: MerchantsOrderSuccessPage,
  validateSearch: (search: Record<string, unknown>) => ({
    qr: typeof search.qr === "string" ? search.qr : undefined,
  }),
});
