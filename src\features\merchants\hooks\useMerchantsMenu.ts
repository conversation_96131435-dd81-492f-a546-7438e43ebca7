import { useState, useEffect } from "react";
import { useQRInfo } from "@/shared/hooks/useQRInfo";
import type { Merchant, MenuCategory } from "@/features/merchants/types";
import type { QRMerchant } from "@/api/merchants/qr.types";
import { merchantsActions } from "@/stores/merchantsStore";

export type MerchantWithMenu = Omit<Merchant, "categories"> & {
  categories: MenuCategory[];
};

export type UseMerchantsMenuProps = {
  qr: string;
};

export function useMerchantsMenu({ qr }: UseMerchantsMenuProps) {
  const [merchants, setMerchants] = useState<MerchantWithMenu[]>([]);
  const [selectedMerchantId, setSelectedMerchantId] = useState<string | null>(
    null
  );
  const [activeCategory, setActiveCategory] = useState<string>("");
  const [restaurant, setRestaurant] = useState<MerchantWithMenu | null>(null);
  const [error, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const qrInfo = useQRInfo({ qrCode: qr, enabled: !!qr });
  // Populate merchants from qrInfo
  useEffect(() => {
    setIsLoading(qrInfo?.isLoading ?? false);
    setError(qrInfo?.error ?? null);
    if (qrInfo?.data?.merchants) {
      const CATEGORY_KEYS = [
        { id: "food", name: "Đồ ăn" },
        { id: "drink", name: "Đồ uống" },
        { id: "toppings", name: "Topping" },
      ];
      const qrMerchants: MerchantWithMenu[] = qrInfo.data.merchants.map(
        (qrMerchant: QRMerchant): MerchantWithMenu => {
          // All items are now in qrMerchant.food_items (flat array)
          const allItems = Array.isArray(qrMerchant.food_items)
            ? qrMerchant.food_items.map((item: any) => ({
                ...item,
                tag: item.tag || "food", // fallback if missing
              }))
            : [];

          // Map to fixed categories
          const categories = CATEGORY_KEYS.map((cat) => ({
            id: cat.id,
            name: cat.name,
            description: "",
            icon_url: "",
            color_code: "",
            food_items: allItems.filter((item: any) => item.tag === cat.id),
          }));

          return {
            ...qrMerchant,
            display_order: 0,
            categories,
          };
        }
      );
      setMerchants(qrMerchants);
      merchantsActions.setMerchantData(qrInfo.data);
    } else {
      setMerchants([]);
    }
  }, [qrInfo?.data, qrInfo?.isLoading, qrInfo?.error]);

  // Set default selected merchant
  useEffect(() => {
    if (!selectedMerchantId && merchants.length > 0) {
      setSelectedMerchantId(merchants[0].id);
    }
  }, [merchants, selectedMerchantId]);

  // Set restaurant and default active category
  useEffect(() => {
    if (selectedMerchantId && merchants.length > 0) {
      const found = merchants.find((m) => m.id === selectedMerchantId) || null;
      setRestaurant(found);
      if (found && found.categories.length > 0 && !activeCategory) {
        setActiveCategory(found.categories[0].id);
      }
    }
  }, [selectedMerchantId, merchants]);

  return {
    merchants,
    isLoading,
    error,
    selectedMerchantId,
    setSelectedMerchantId,
    restaurant,
    activeCategory,
    setActiveCategory,
  };
}
