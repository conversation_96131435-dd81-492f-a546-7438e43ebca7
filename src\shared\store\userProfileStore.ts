import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { UserProfile as ApiUserProfile } from "@/shared/types/auth";

interface UserProfileStore {
  profile: ApiUserProfile | null;
  setProfile: (profile: ApiUserProfile | null) => void;
  clearProfile: () => void;
}

export const useUserProfileStore = create<UserProfileStore>()(
  devtools(
    (set) => ({
      profile: null,
      setProfile: (profile) => set({ profile }),
      clearProfile: () => set({ profile: null }),
    }),
    {
      name: "user-profile-store",
    }
  )
);
