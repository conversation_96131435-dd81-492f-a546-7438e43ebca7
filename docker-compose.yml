version: "3.8"
services:
  # Frontend React App
  sieu-booking-app:
    image: ${DOCKER_IMAGE} # Đ<PERSON><PERSON> từ .env file
    ports:
      - "8088:80"
    environment:
      - NODE_ENV=${NODE_ENV} # Đ<PERSON><PERSON> từ .env file
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
networks:
  default:
    name: sieu-booking-network
