<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <link rel="icon" type="image/svg+xml" href="/assets/logo_gtech.svg?v=2" />
    <meta name="theme-color" content="#000000" />
    <!-- <meta
      name="description"
      content="Web site created using create-tsrouter-app"
    /> -->
    <link
      rel="apple-touch-icon"
      type="image/svg+xml"
      href="/assets/logo_gtech.svg?v=2"
    />
    <link rel="manifest" href="/manifest.json" />
    <!-- Google Fonts - Mulish -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap"
      rel="stylesheet"
    />
    <!-- Google Fonts - Inter & JetBrains Mono for ticket generation -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <title>GTech Ecommerce</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
