import React, { useCallback } from "react";
import { PlusIcon, MinusIcon } from "@heroicons/react/16/solid";
import { formatPrice, getRandomFoodImage } from "@/features/merchants/utils";
import type { MenuItem } from "@/features/merchants/types";
import { useStore } from "@tanstack/react-store";
import { simpleCartActions, simpleCartStore } from "@/stores/simpleCartStore";

interface MenuItemCardProps {
  item: MenuItem;
  highlight?: boolean;
  merchantId: string;
  merchantName: string;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = React.memo(
  ({ item, highlight = false, merchantId }) => {
    const quantity = useStore(
      simpleCartStore,
      (state) => state.items[item.id]?.quantity || 0
    );

    const handleDecrease = useCallback(() => {
      if (quantity > 1) {
        simpleCartActions.updateItemQuantity(item.id, quantity - 1);
      } else if (quantity === 1) {
        simpleCartActions.removeItem(item.id);
      }
    }, [item.id, quantity]);

    const handleIncrease = useCallback(() => {
      simpleCartActions.addItem({ ...item, merchantId, quantity: 1 });
    }, [item, merchantId]);

    return (
      <div
        className={
          `flex items-center gap-4 py-0 transition-all duration-300 ` +
          (highlight
            ? "border-2 border-[#FF7F37] shadow-lg bg-orange-50"
            : "border border-transparent")
        }
      >
        <img
          src={
            item?.images?.[0]?.sizes?.small ||
            getRandomFoodImage(item.image_url) ||
            "/assets/merchants/image-9.png"
          }
          alt={item.name}
          className="w-[88px] h-[88px] rounded-xl object-cover"
          onError={(e) => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = "/assets/merchants/image-9.png";
          }}
        />

        <div className="flex-1 flex flex-col justify-between gap-2">
          <div className="flex-1">
            <h3 className="text-sm font-normal text-[#181818] leading-[18px] mb-1">
              {item.name}
            </h3>
            {item.description && (
              <p className="text-xs font-normal text-[#747474] leading-4 line-clamp-2 max-h-8">
                {item.description}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-bold text-[#181818]">
              {formatPrice(item.price)}
            </span>

            {quantity === 0 ? (
              <button
                onClick={handleIncrease}
                className="w-8 h-8 rounded-lg bg-[#2D5BFF] flex items-center justify-center"
              >
                <PlusIcon className="w-4 h-4 text-white" />
              </button>
            ) : (
              <div className="flex items-center gap-3">
                <button
                  onClick={handleDecrease}
                  className="w-8 h-8 rounded-lg bg-[#EFEFEF] flex items-center justify-center"
                >
                  <MinusIcon className="w-4 h-4 text-[#7C7B7B]" />
                </button>

                <div className="w-12 h-8 rounded-lg bg-[#F8F8F8] flex items-center justify-center px-4">
                  <span className="text-sm font-normal text-[#5C5C5C]">
                    {quantity}
                  </span>
                </div>

                <button
                  onClick={handleIncrease}
                  className="w-8 h-8 rounded-lg bg-[#2D5BFF] flex items-center justify-center"
                >
                  <PlusIcon className="w-4 h-4 text-white" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);
