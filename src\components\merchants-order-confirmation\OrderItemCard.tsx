import React, { useCallback, useState } from "react";
import { PlusIcon, MinusIcon } from "@heroicons/react/16/solid";
import { formatPrice } from "@/features/merchants/utils";
import type { CartItem } from "@/features/merchants/types";
import { useStore } from "@tanstack/react-store";
import { simpleCartStore, simpleCartActions } from "@/stores/simpleCartStore";

interface OrderItemCardProps {
  item: CartItem;
  disabled?: boolean;
}

export const OrderItemCard: React.FC<OrderItemCardProps> = React.memo(
  ({ item, disabled = false }) => {
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const quantity = useStore(
      simpleCartStore,
      (state) => state.items[item.id]?.quantity || 0
    );

    const handleDecrease = useCallback(() => {
      if (disabled) return;
      if (quantity > 1) {
        simpleCartActions.updateItemQuantity(item.id, quantity - 1);
      } else if (quantity === 1) {
        setShowDeleteModal(true);
      }
    }, [item.id, quantity, disabled]);

    const handleIncrease = useCallback(() => {
      if (!disabled) {
        simpleCartActions.addItem({ ...item, quantity: 1 });
      }
    }, [item, disabled]);

    const handleConfirmDelete = useCallback(() => {
      simpleCartActions.removeItem(item.id);
      setShowDeleteModal(false);
    }, [item.id]);

    const handleCancelDelete = useCallback(() => {
      setShowDeleteModal(false);
    }, []);

    return (
      <>
        <div className="flex items-center gap-4 py-4">
          <div className="flex-1 flex flex-col justify-between gap-2">
            <div className="flex-1">
              <h3 className="text-sm font-bold text-[#181818] leading-[18px] mb-1">
                {item.name}
              </h3>
              {item.description && (
                <p className="text-xs font-normal text-[#747474] leading-4 line-clamp-2">
                  {item.description}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-bold text-[#181818]">
                {formatPrice(item.price)}
              </span>

              {quantity === 0 ? (
                <button
                  onClick={handleIncrease}
                  disabled={disabled}
                  className="w-8 h-8 rounded-lg bg-[#2D5BFF] flex items-center justify-center disabled:bg-gray-300"
                >
                  <PlusIcon className="w-4 h-4 text-white" />
                </button>
              ) : (
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleDecrease}
                    disabled={disabled}
                    className="w-8 h-8 rounded-lg bg-[#EFEFEF] flex items-center justify-center disabled:bg-gray-200"
                  >
                    <MinusIcon className="w-4 h-4 text-[#7C7B7B]" />
                  </button>

                  <div className="w-12 h-8 rounded-lg bg-[#F8F8F8] flex items-center justify-center px-4">
                    <span className="text-sm font-normal text-[#5C5C5C]">
                      {quantity}
                    </span>
                  </div>

                  <button
                    onClick={handleIncrease}
                    disabled={disabled}
                    className="w-8 h-8 rounded-lg bg-[#2D5BFF] flex items-center justify-center disabled:bg-gray-300"
                  >
                    <PlusIcon className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 opacity-100 transition-opacity duration-200"
            onClick={handleCancelDelete}
          >
            <div
              className="bg-white rounded-xl p-6 mx-4 max-w-sm w-full transform scale-100 transition-transform duration-200"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-bold text-restaurant-text-primary mb-2">
                Xóa món ăn
              </h3>
              <p className="text-sm text-restaurant-text-tertiary mb-6">
                Bạn có chắc chắn muốn xóa "
                <span className="font-medium">{item.name}</span>" khỏi giỏ hàng
                không?
              </p>

              <div className="flex gap-3">
                <button
                  onClick={handleCancelDelete}
                  className="flex-1 py-3 px-4 rounded-lg border border-restaurant-border-light bg-white hover:bg-restaurant-background-light transition-colors duration-200"
                  disabled={disabled}
                >
                  <span className="text-sm font-normal text-restaurant-text-tertiary">
                    Hủy
                  </span>
                </button>

                <button
                  onClick={handleConfirmDelete}
                  className="flex-1 py-3 px-4 rounded-lg bg-red-400 hover:bg-red-600 transition-colors duration-200"
                  disabled={disabled}
                >
                  <span className="text-sm font-bold text-white">Xóa</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }
);

OrderItemCard.displayName = "OrderItemCard";
