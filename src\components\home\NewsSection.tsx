import React, { memo, useCallback, useMemo } from "react";
import { CardCarousel, type CardItem } from "@/shared/components/CardCarousel";
import { NewsCard } from "./NewsCard";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { formatTimeAgo } from "@/lib/utils";
import { useArticles } from "@/shared/hooks/useArticles";
import { useNavigate } from "@tanstack/react-router";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

interface NewsSectionProps {
  isLoading?: boolean;
}

export const NewsSection: React.FC<NewsSectionProps> = memo(
  ({ isLoading: propLoading = false }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { data: articles, isLoading: isLoadingArticles } = useArticles();

    // Mock data as fallback when API fails or returns empty
    const MOCK_NEWS: CardItem[] = useMemo(
      () => [
        {
          id: "1",
          image: "/mock-images/image-2.png",
          title: "BST SÓNG: <PERSON><PERSON>n định trong mắt – Tĩnh lặng trong tâm",
          timeAgo: formatTimeAgo(2, "hours", t),
        },
        {
          id: "2",
          image: "/mock-images/image-3.png",
          title: "CHẮP CÁNH HY VỌNG – ƯƠM MẦM YÊU THƯƠNG",
          timeAgo: formatTimeAgo(2, "hours", t),
        },
        {
          id: "3",
          image: "/mock-images/image-7.png",
          title: "Cánh rừng hy vọng – Hành trình xanh bền vững",
          timeAgo: formatTimeAgo(2, "hours", t),
        },
        {
          id: "4",
          image: "/mock-images/image-8.png",
          title: "WHITE CHIC FW COLLECTION 2024 – INFINITY",
          timeAgo: formatTimeAgo(2, "hours", t),
        },
        {
          id: "5",
          image: "/mock-images/image-3.png",
          title: "Xu hướng thời trang bền vững trong năm 2024",
          timeAgo: formatTimeAgo(3, "hours", t),
        },
        {
          id: "6",
          image: "/mock-images/image-2.png",
          title: "Nghệ thuật sống tối giản và những giá trị cốt lõi",
          timeAgo: formatTimeAgo(5, "hours", t),
        },
      ],
      [t]
    );

    // Transform Strapi articles to CardItem format
    const transformedNews: CardItem[] = useMemo(() => {
      if (!articles || articles.length === 0) {
        return MOCK_NEWS;
      }

      return articles.map((article) => ({
        id: article.id.toString(),
        image: getStrapiImageUrl(
          article.image_background,
          "/mock-images/image-2.png"
        ),
        title: article.title,
        timeAgo: new Date(article.published_at).toLocaleDateString("vi-VN"),
      }));
    }, [articles, MOCK_NEWS]);

    const handleNewsClick = useCallback(
      (newsId: string) => {
        navigate({ to: "/news-detail/$id", params: { id: newsId } });
      },
      [navigate]
    );

    const renderNewsCard = useCallback(
      (item: CardItem) => {
        return (
          <NewsCard
            id={item.id}
            image={item.image}
            title={item.title}
            timeAgo={item.timeAgo}
            onHover={true}
            onClick={() => handleNewsClick(item.id)}
          />
        );
      },
      [handleNewsClick]
    );

    // Combine loading states
    const isLoading = propLoading || isLoadingArticles;

    return (
      <CardCarousel
        title={t("home.news")}
        items={transformedNews}
        itemsPerView={{
          mobile: 1,
          tablet: 2,
          desktop: 4,
        }}
        renderCard={renderNewsCard}
        isLoading={isLoading}
      />
    );
  }
);

NewsSection.displayName = "NewsSection";
