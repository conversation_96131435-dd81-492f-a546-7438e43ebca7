import { memo, useState } from "react";
import { Button } from "@/shared/components/button";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";

interface PriceSummaryProps {
  onPayment?: () => void;
  isSubmitting: boolean;
  numTickets: number;
  buttonTitle?: string;
  disableMobileUI?: boolean;
}

const PriceSummary = memo(
  ({
    onPayment,
    isSubmitting,
    numTickets,
    buttonTitle = "Đặt vé",
    disableMobileUI = false,
  }: PriceSummaryProps) => {
    const { outboundTrip, returnTrip } = useSelectedTripsStore();
    const [isExpanded, setIsExpanded] = useState(false);

    // Calculate prices based on number of tickets
    const outboundTotalPrice = outboundTrip
      ? outboundTrip.price * numTickets
      : 0;
    const returnTotalPrice = returnTrip ? returnTrip.price * numTickets : 0;
    const totalPrice = outboundTotalPrice + returnTotalPrice;

    return (
      <>
        {/* Spacer for sticky content - dynamic height based on expanded state */}
        <div className={`lg:h-0 ${isExpanded ? "h-64" : "h-32"}`}></div>

        {/* MOBILE UI - Sticky Bottom Container with Single Expand/Collapse */}
        {!disableMobileUI && (
          <div className="lg:hidden bg-white fixed bottom-0 left-0 right-0 z-50">
            <div className="bg-[#F8F8F8] p-4 pt-0">
              <div className="bg-white border border-[#EDEDED] rounded-xl p-3">
                <div className="space-y-3">
                  {/* Total with Collapse Icon */}
                  <div
                    className="flex justify-between items-center cursor-pointer"
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    <span className="text-base text-[#747474]">Tổng tiền</span>
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-bold text-[#FF7F37]">
                        {totalPrice.toLocaleString("vi-VN")}đ
                      </span>
                      <button className="w-4 h-4 text-[#7C7B7B]">
                        {isExpanded ? (
                          <ChevronUpIcon className="w-4 h-4" />
                        ) : (
                          <ChevronDownIcon className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Expanded Details */}
                  {isExpanded && (
                    <div className="space-y-3">
                      {/* Outbound Trip */}
                      {outboundTrip && (
                        <div className="bg-[#F8F8F8] rounded-lg p-3">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-bold text-[#181818]">
                                Chiều đi
                              </span>
                              <span className="text-sm font-bold text-[#181818]">
                                {outboundTotalPrice.toLocaleString("vi-VN")}đ
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-[#747474] text-sm">
                                Giá vé
                              </span>
                              <span className="font-bold text-[#181818] text-sm">
                                {outboundTrip.price.toLocaleString("vi-VN")}đ
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-[#747474] text-sm">
                                Số vé
                              </span>
                              <span className="text-[#181818] text-sm">
                                {numTickets}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Return Trip */}
                      {returnTrip && (
                        <div className="bg-[#F8F8F8] rounded-lg p-3">
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-bold text-[#181818]">
                                Chiều về
                              </span>
                              <span className="text-sm font-bold text-[#181818]">
                                {returnTotalPrice.toLocaleString("vi-VN")}đ
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-[#747474] text-sm">
                                Giá vé
                              </span>
                              <span className="font-bold text-[#181818] text-sm">
                                {returnTrip.price.toLocaleString("vi-VN")}đ
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-[#747474] text-sm">
                                Số vé
                              </span>
                              <span className="text-[#181818] text-sm">
                                {numTickets}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Payment Button */}
                  <Button
                    onClick={onPayment}
                    disabled={isSubmitting}
                    className={`w-full font-extrabold text-lg py-6 rounded-lg ${
                      isSubmitting
                        ? "bg-[#EFEFEF] text-[#8A8A8A] cursor-not-allowed hover:bg-[#EFEFEF]"
                        : "bg-[#FF7F37] text-white hover:bg-[#FF7F37]/90"
                    }`}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Đặt vé"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* DESKTOP UI - Static Container with Separate Sections (Figma Design) */}
        <div className="hidden w-full lg:block min-w-[300px]">
          <div className="space-y-2">
            {/* Tickets Summary Section */}
            <div className="bg-white border border-[#EDEDED] rounded-xl p-4">
              <div className="space-y-4">
                {/* Header */}
                <div className="space-y-0.5">
                  <h3 className="text-lg font-bold text-[#181818]">
                    Giá vé tạm tính
                  </h3>
                  <p className="text-xs text-[#5C5C5C]">
                    {outboundTrip && returnTrip && (
                      <>
                        <span className="font-bold">
                          {outboundTrip.departureLocation}
                        </span>
                        {" đi "}
                        <span className="font-bold">
                          {outboundTrip.arrivalLocation}
                        </span>
                        {" (khứ hồi)"}
                      </>
                    )}
                    {outboundTrip && !returnTrip && (
                      <>
                        <span className="font-bold">
                          {outboundTrip.departureLocation}
                        </span>
                        {" đi "}
                        <span className="font-bold">
                          {outboundTrip.arrivalLocation}
                        </span>
                      </>
                    )}
                  </p>
                </div>

                {/* Outbound Trip */}
                {outboundTrip && (
                  <div className="bg-[#F8F8F8] rounded-lg p-3">
                    <div className="space-y-2">
                      {/* Trip Header */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bold text-[#181818]">
                          Chiều đi
                        </span>
                        <span className="text-sm font-bold text-[#181818]">
                          {outboundTotalPrice.toLocaleString("vi-VN")}đ
                        </span>
                      </div>

                      {/* Trip Details */}
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-[#747474] w-[77px]">
                            Giá vé
                          </span>
                          <span className="text-sm font-bold text-[#181818] text-right flex-1">
                            {outboundTrip.price.toLocaleString("vi-VN")}đ
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-[#747474] w-[77px]">
                            Số vé
                          </span>
                          <span className="text-sm text-[#181818] text-right flex-1">
                            {numTickets}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Return Trip */}
                {returnTrip && (
                  <div className="bg-[#F8F8F8] rounded-lg p-3">
                    <div className="space-y-2">
                      {/* Trip Header */}
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bold text-[#181818]">
                          Chiều về
                        </span>
                        <span className="text-sm font-bold text-[#181818]">
                          {returnTotalPrice.toLocaleString("vi-VN")}đ
                        </span>
                      </div>

                      {/* Trip Details */}
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-[#747474] w-[77px]">
                            Giá vé
                          </span>
                          <span className="text-sm font-bold text-[#181818] text-right flex-1">
                            {returnTrip.price.toLocaleString("vi-VN")}đ
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-[#747474] w-[77px]">
                            Số vé
                          </span>
                          <span className="text-sm text-[#181818] text-right flex-1">
                            {numTickets}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Total Section */}
            <div className="bg-white border border-[#EDEDED] rounded-xl p-4">
              <div className="flex justify-between items-center">
                <span className="text-base text-[#747474]">Tổng tiền</span>
                <span className="text-sm font-bold text-[#FF7F37]">
                  {totalPrice.toLocaleString("vi-VN")}đ
                </span>
              </div>
            </div>

            {/* Payment Button Section */}
            {onPayment && (
              <div className="bg-white border border-[#EDEDED] rounded-xl p-4">
                <Button
                  onClick={onPayment}
                  disabled={isSubmitting}
                  className={`w-full font-extrabold text-lg py-6 rounded-lg ${
                    isSubmitting
                      ? "bg-[#EFEFEF] text-[#8A8A8A] cursor-not-allowed hover:bg-[#EFEFEF]"
                      : "bg-[#FF7F37] text-white hover:bg-[#FF7F37]/90"
                  }`}
                >
                  {isSubmitting ? "Đang xử lý..." : buttonTitle}
                </Button>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
);

PriceSummary.displayName = "PriceSummary";

export default PriceSummary;
