// places

const VERSION = "/api/v1";

export const GET_ALL_PLACES_API_ENDPOINT = `${VERSION}/places`;
export const SEARCH_PLACES_API_ENDPOINT = `${VERSION}/places/search`;

// bus-schedules
export const GET_ALL_BUS_SCHEDULES_API_ENDPOINT = `${VERSION}/bus-schedules`;
export const SEARCH_BUS_SCHEDULES_API_ENDPOINT = `${VERSION}/bus-schedules/search`;
export const GET_BUS_SCHEDULE_BY_ID_API_ENDPOINT = `${VERSION}/bus-schedules`;
export const CREATE_BUS_SCHEDULE_API_ENDPOINT = `${VERSION}/bus-schedules`;
export const UPDATE_BUS_SCHEDULE_API_ENDPOINT = `${VERSION}/bus-schedules`;

// orders
export const CREATE_ORDER_API_ENDPOINT = `${VERSION}/orders`;
export const GET_ORDER_BY_ID_API_ENDPOINT = `${VERSION}/orders`;
export const GET_ALL_ORDERS_API_ENDPOINT = `${VERSION}/orders`;

// auth
export const AUTH_SIGNUP_API_ENDPOINT = `${VERSION}/auth/signup`;
export const AUTH_VERIFY_OTP_API_ENDPOINT = `${VERSION}/auth/verify-otp`;
export const AUTH_SET_PASSWORD_API_ENDPOINT = `${VERSION}/auth/set-password`;
export const AUTH_SIGNIN_API_ENDPOINT = `${VERSION}/auth/signin`;
export const AUTH_PROFILE_API_ENDPOINT = `${VERSION}/auth/profile`;
export const AUTH_ADMIN_TEST_API_ENDPOINT = `${VERSION}/auth/admin/test`;
export const AUTH_FORGOT_PASSWORD_API_ENDPOINT = `${VERSION}/auth/forgot-password`;
export const AUTH_FORGOT_PASSWORD_VERIFY_OTP_API_ENDPOINT = `${VERSION}/auth/verify-reset-otp`;
export const AUTH_RESET_PASSWORD_API_ENDPOINT = `${VERSION}/auth/reset-password`;
export const AUTH_UPDATE_PASSWORD_API_ENDPOINT = `${VERSION}/auth/update-password`;
export const AUTH_UPDATE_AVATAR_API_ENDPOINT = `${VERSION}/auth/avatar`;
export const AUTH_GET_AVATAR_API_ENDPOINT = `${VERSION}/auth/avatar`;
export const AUTH_DELETE_AVATAR_API_ENDPOINT = `${VERSION}/auth/avatar`;
export const AUTH_UPDATE_PROFILE_API_ENDPOINT = `${VERSION}/auth/metadata`;

// fleets
export const CREATE_FLEET_API_ENDPOINT = `${VERSION}/fleets`;

// merchants
export const GET_ALL_MERCHANT_API_ENDPOINT = `${VERSION}/food-court/merchants`;
export const GET_MERCHANT_BY_ID_API_ENDPOINT = `${VERSION}/food-court/merchants`;

//items
export const GET_ALL_ITEM_API_ENDPOINT = `${VERSION}/food-court/food-items`;
export const GET_ITEM_BY_ID_API_ENDPOINT = `${VERSION}/food-court/food-items`;
