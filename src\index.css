@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #262626;
  --card: #ffffff;
  --card-foreground: #262626;
  --popover: #ffffff;
  --popover-foreground: #262626;
  --primary: #3b3b3b;
  --primary-foreground: #fafafa;
  --secondary: #f8f8f8;
  --secondary-foreground: #3b3b3b;
  --muted: #f8f8f8;
  --muted-foreground: #8c8c8c;
  --accent: #f8f8f8;
  --accent-foreground: #3b3b3b;
  --destructive: #dc2626;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #a3a3a3;
  --chart-1: #f97316;
  --chart-2: #10b981;
  --chart-3: #3b82f6;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;
  --sidebar: #fafafa;
  --sidebar-foreground: #262626;
  --sidebar-primary: #3b3b3b;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #f8f8f8;
  --sidebar-accent-foreground: #3b3b3b;
  --sidebar-border: #e5e5e5;
  --sidebar-ring: #a3a3a3;
}

.dark {
  --background: #262626;
  --foreground: #fafafa;
  --card: #3b3b3b;
  --card-foreground: #fafafa;
  --popover: #3b3b3b;
  --popover-foreground: #fafafa;
  --primary: #e5e5e5;
  --primary-foreground: #3b3b3b;
  --secondary: #525252;
  --secondary-foreground: #fafafa;
  --muted: #525252;
  --muted-foreground: #a3a3a3;
  --accent: #525252;
  --accent-foreground: #fafafa;
  --destructive: #ef4444;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: #8c8c8c;
  --chart-1: #3b82f6;
  --chart-2: #10b981;
  --chart-3: #ef4444;
  --chart-4: #8b5cf6;
  --chart-5: #f97316;
  --sidebar: #3b3b3b;
  --sidebar-foreground: #fafafa;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #fafafa;
  --sidebar-accent: #525252;
  --sidebar-accent-foreground: #fafafa;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: #8c8c8c;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      "Mulish",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue",
      sans-serif;
  }

  /* Global focus styles reset - override browser defaults */
  *:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Reset select focus styles specifically */
  select:focus {
    outline: none !important;
    box-shadow: none !important;
    border: none !important;
  }

  /* Reset input focus styles */
  input:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Reset button focus styles */
  button:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  /* Reset div with tabindex focus styles */
  div[tabindex]:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}

/* Hide scrollbar and improve scrolling */
.scrollbar-hide {
  /* Hide scrollbar for all browsers */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */

  /* Smooth scrolling */
  scroll-behavior: smooth;

  /* iOS momentum scrolling */
  -webkit-overflow-scrolling: touch;

  /* Improve scrolling performance */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  /* Better touch handling */
  touch-action: pan-x;

  /* Reduce scroll snap friction */
  overscroll-behavior-x: contain;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
  width: 0;
  height: 0;
}

/* Improve scrolling on mobile */
@media (max-width: 768px) {
  .scrollbar-hide {
    /* Enhanced touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x proximity;
    overscroll-behavior: contain;
  }
}
