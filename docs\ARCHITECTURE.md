# 🏗️ Kiến trúc dự án Sieu Booking App

## 📁 Cấu trúc thư mục

```
src/
├── shared/           # Shared utilities, components, hooks
│   ├── components/   # UI components dùng chung (Button, Input, etc.)
│   ├── hooks/        # Custom hooks dùng chung
│   ├── utils/        # Utility functions
│   ├── types/        # Shared types
│   ├── constants/    # Constants dùng chung
│   ├── api/          # Base API config (axios, endpoints)
│   └── index.ts      # Export tất cả shared modules
├── features/
│   ├── merchants/    # 🏪 Domain: Nhà hàng/cửa hàng
│   │   ├── components/  # Components riêng cho merchants
│   │   ├── hooks/       # Hooks riêng cho merchants
│   │   ├── api/         # API calls cho merchants
│   │   ├── types/       # Types cho merchants
│   │   ├── constants/   # Constants cho merchants
│   │   ├── stores/      # State management cho merchants
│   │   ├── routes/      # Route components cho merchants
│   │   └── index.ts     # Export merchants feature
│   └── booking/      # 🚌 Domain: Đặt vé xe bus
│       ├── components/  # Components riêng cho booking
│       ├── hooks/       # Hooks riêng cho booking
│       ├── api/         # API calls cho booking
│       ├── types/       # Types cho booking
│       ├── constants/   # Constants cho booking
│       ├── stores/      # State management cho booking
│       ├── routes/      # Route components cho booking
│       └── index.ts     # Export booking feature
├── routes/           # Root routes và layout chung
├── locales/          # Internationalization
└── lib/              # External library configs
```

## 🎯 Nguyên tắc thiết kế

### 1. **Feature-based Architecture**

- Mỗi feature (merchants, booking) được tổ chức độc lập
- Dễ dàng thêm/xóa features mà không ảnh hưởng đến nhau
- Code được nhóm theo business domain thay vì technical layer

### 2. **Shared Module Pattern**

- Tất cả code dùng chung được đặt trong `src/shared/`
- UI components, utilities, types được tái sử dụng
- Tránh code duplication

### 3. **Clear Import Paths**

```typescript
// ✅ Good - Clear feature imports
import { MerchantCard, PopularMerchants } from "@/merchants";
import { BusCard, SearchPageContent } from "@/booking";
import { Button, Input } from "@/shared/components";

// ❌ Bad - Unclear imports
import MerchantCard from "../../../components/merchants/MerchantCard";
```

### 4. **TypeScript Path Mapping**

```json
{
  "paths": {
    "@/*": ["./src/*"],
    "@/shared/*": ["./src/shared/*"],
    "@/features/*": ["./src/features/*"],
    "@/merchants/*": ["./src/features/merchants/*"],
    "@/booking/*": ["./src/features/booking/*"]
  }
}
```

## 🔧 Quy tắc phát triển

### 1. **Component Organization**

- **Shared components**: UI primitives (Button, Input, Modal)
- **Feature components**: Business logic components
- **Page components**: Route-level components

### 2. **API Organization**

- **Shared API**: Base axios config, common endpoints
- **Feature API**: Domain-specific API calls
- **Types**: Request/Response types cho từng feature

### 3. **State Management**

- **Shared stores**: Global state (user, theme, language)
- **Feature stores**: Domain-specific state
- **Component state**: Local component state

### 4. **Hooks Organization**

- **Shared hooks**: Generic hooks (useLocalStorage, useDebounce)
- **Feature hooks**: Business logic hooks
- **API hooks**: TanStack Query hooks

## 📦 Import/Export Patterns

### Feature Exports

```typescript
// src/features/merchants/index.ts
export * from "./components";
export * from "./hooks";
export * from "./api";
export * from "./types";
export * from "./constants";
export * from "./stores";
```

### Component Exports

```typescript
// src/features/merchants/components/index.ts
export { PopularMerchantCard } from "./PopularMerchantCard";
export { default as MerchantCard } from "./MerchantCard";
export { MenuList } from "./MenuList";
```

### Usage Examples

```typescript
// ✅ Feature-level imports
import { PopularMerchants, MerchantCard, useMerchantData } from "@/merchants";

// ✅ Shared imports
import { Button, Input, LoadingSpinner } from "@/shared/components";
import { formatCurrency, debounce } from "@/shared/utils";

// ✅ Specific imports when needed
import { SearchPageContent } from "@/booking/components";
```

## 🚀 Lợi ích của cấu trúc mới

1. **Scalability**: Dễ dàng thêm features mới
2. **Maintainability**: Code được tổ chức rõ ràng theo domain
3. **Reusability**: Shared components và utilities
4. **Developer Experience**: Import paths rõ ràng, dễ tìm code
5. **Team Collaboration**: Các team có thể làm việc độc lập trên từng feature
6. **Testing**: Dễ dàng test từng feature riêng biệt

## 🔄 Migration Guide

### Cập nhật imports

```typescript
// Before
import MerchantCard from "../../../components/merchants/MerchantCard";
import { Button } from "../../../components/ui/button";

// After
import { MerchantCard } from "@/merchants";
import { Button } from "@/shared/components";
```

### Thêm feature mới

1. Tạo thư mục trong `src/features/new-feature/`
2. Tạo cấu trúc con: components, hooks, api, types, etc.
3. Tạo index.ts để export
4. Cập nhật tsconfig.json paths nếu cần
5. Import và sử dụng: `import { ... } from '@/new-feature';`
