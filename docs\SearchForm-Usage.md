# Reusable SearchForm Component

The `SearchForm` component has been refactored to be completely reusable without requiring any props. It uses React Context to manage its state and can be used anywhere in the application with different variants for different layouts.

## Quick Start

### 1. Basic Usage (Homepage)

```tsx
import { SearchForm } from "../components/home/<USER>";
import { SearchFormProvider } from "../contexts/SearchFormContext";

function MyPage() {
  return (
    <SearchFormProvider>
      <div className="my-page">
        <h1>My Custom Page</h1>
        <SearchForm />
      </div>
    </SearchFormProvider>
  );
}
```

### 2. Search Page Usage (Compact Layout)

```tsx
import { SearchForm } from "../components/home/<USER>";
import { SearchFormProvider } from "../contexts/SearchFormContext";

function SearchPage() {
  const initialValues = {
    fromLocation: "Hồ Chí Minh",
    toLocation: "Hà Nội",
    departureDate: "2024-12-25",
    numTickets: 2,
  };

  return (
    <SearchFormProvider variant="search-page" initialValues={initialValues}>
      <div className="search-page">
        <SearchForm />
        {/* Other search page content */}
      </div>
    </SearchFormProvider>
  );
}
```

### 3. Interacting with Form State

```tsx
import { useSearchForm } from "../contexts/SearchFormContext";

function MyComponent() {
  const { state, setFromLocation, setToLocation, handleSubmit } =
    useSearchForm();

  return (
    <div>
      <p>From: {state.fromLocation}</p>
      <p>To: {state.toLocation}</p>
      <button onClick={() => setFromLocation("Hồ Chí Minh")}>
        Set From Location
      </button>
    </div>
  );
}
```

### 4. Using Convenience Actions

```tsx
import { useSearchFormActions } from "../hooks/useSearchFormActions";

function QuickSearchButtons() {
  const { quickSearch, resetForm, isFormValid, variant } =
    useSearchFormActions();

  return (
    <div>
      <button
        onClick={() => quickSearch("Hồ Chí Minh", "Hà Nội", "2024-12-25", 2)}
      >
        Quick Search: HCM → HN
      </button>

      <button onClick={resetForm}>Reset Form</button>

      <p>Form is {isFormValid() ? "valid" : "invalid"}</p>
      <p>Current variant: {variant}</p>
    </div>
  );
}
```

## Variants

The SearchForm component supports two variants:

### `homepage` (Default)

- Floating form with full styling
- Positioned absolutely at the bottom center
- Includes guide link and full spacing
- Best for hero sections and landing pages

### `search-page`

- Compact horizontal layout
- Full width with responsive design
- Optimized for search results pages
- Smaller buttons and tighter spacing

## API Reference

### SearchFormProvider

The context provider that manages all search form state.

**Props:**

- `children: React.ReactNode` - Components that need access to search form state
- `variant?: "homepage" | "search-page"` - Layout variant (default: "homepage")
- `initialValues?: Partial<SearchFormState>` - Initial form values

### useSearchForm()

Direct access to the search form context.

**Returns:**

- `state: SearchFormState` - Current form state
- `variant: SearchFormVariant` - Current layout variant
- `setFromLocation: (value: string) => void`
- `setToLocation: (value: string) => void`
- `setIsRoundTrip: (value: boolean) => void`
- `setDepartureDate: (value: string) => void`
- `setReturnDate: (value: string) => void`
- `setNumTickets: (value: number) => void`
- `swapLocations: () => void`
- `resetForm: () => void`
- `handleSubmit: (e: React.FormEvent) => void`
- `places: Place[]` - Available places
- `filteredFromPlaces: Place[]` - Filtered departure places
- `filteredToPlaces: Place[]` - Filtered destination places
- `isLoading: boolean` - Loading state
- `error: any` - Error state

### useSearchFormActions()

Convenience hook with additional utility methods.

**Additional Methods:**

- `setSearchRoute: (from: string, to: string) => void`
- `setDates: (departure: string, return?: string) => void`
- `quickSearch: (from: string, to: string, date: string, tickets?: number) => void`
- `isFormValid: () => boolean`
- `getSearchParams: () => SearchParams | null`

### SearchFormState

```tsx
interface SearchFormState {
  fromLocation: string;
  toLocation: string;
  isRoundTrip: boolean;
  departureDate: string;
  returnDate: string;
  numTickets: number;
}
```

### SearchFormVariant

```tsx
type SearchFormVariant = "homepage" | "search-page";
```

## Architecture Benefits

### 🔄 **Reusability**

- No props required
- Can be used in any component tree
- Self-contained state management
- Multiple layout variants

### 🎯 **Type Safety**

- Fully typed with TypeScript
- IntelliSense support
- Compile-time error checking

### ⚡ **Performance**

- Optimized with `useMemo` and `useCallback`
- Memoized components with `React.memo`
- Efficient re-renders

### 🧪 **Testability**

- Easy to mock context for testing
- Isolated state management
- Clear separation of concerns

### 🔧 **Maintainability**

- Single source of truth for form state
- Centralized business logic
- Easy to extend and modify

## Migration Guide

### Before (Props-based)

```tsx
<SearchForm
  fromLocation={fromLocation}
  toLocation={toLocation}
  onFromLocationChange={setFromLocation}
  onToLocationChange={setToLocation}
  isRoundTrip={isRoundTrip}
  onRoundTripChange={setIsRoundTrip}
  filteredFromPlaces={filteredFromPlaces}
  filteredToPlaces={filteredToPlaces}
  places={places}
/>
```

### After (Context-based)

```tsx
{
  /* Homepage variant */
}
<SearchFormProvider>
  <SearchForm />
</SearchFormProvider>;

{
  /* Search page variant */
}
<SearchFormProvider variant="search-page" initialValues={searchData}>
  <SearchForm />
</SearchFormProvider>;
```

## Best Practices

1. **Provider Placement**: Place `SearchFormProvider` at the highest level where search functionality is needed
2. **Variant Selection**: Use `homepage` for hero sections, `search-page` for search results
3. **Initial Values**: Pass `initialValues` when you need to populate the form with existing data
4. **State Access**: Use `useSearchFormActions()` for most use cases, `useSearchForm()` for direct access
5. **Form Validation**: Always check `isFormValid()` before programmatic submissions
6. **Error Handling**: The form handles loading and error states automatically
7. **Performance**: The context is optimized, but avoid unnecessary re-renders by using the hooks appropriately

## Layout Differences

### Homepage Variant

- Floating form positioned absolutely
- Full padding and spacing
- Large buttons and inputs
- Includes guide link
- Best for hero sections

### Search Page Variant

- Compact horizontal layout
- Responsive flex design
- Smaller buttons and inputs
- No guide link
- Optimized for search results pages

## Examples

See `src/components/examples/ReusableSearchExample.tsx` for a complete working example.
