import { useEffect, useState, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { lookupTickets } from "../api/lookupTickets";
import type { LookupTicketResponse } from "../api/lookupTickets";
import { MobileTicketCard, DesktopTicketCard } from "@/routes/payment-success";
import { Container, ErrorModal } from "@/shared";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { downloadSingleTicketImage } from "@/shared/utils/ticket-html.utils";

const lookupSchema = z.object({
  phone_number: z.string().min(1, "Vui lòng nhập số điện thoại"),
  ticket_number: z.string().min(1, "Vui lòng nhập mã vé"),
});

type LookupFormData = z.infer<typeof lookupSchema>;

export default function LookUpTickets() {
  const [searchParams, setSearchParams] = useState<LookupFormData | null>(null);
  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

  const form = useForm<LookupFormData>({
    resolver: zodResolver(lookupSchema),
    defaultValues: {
      phone_number: "",
      ticket_number: "",
    },
  });

  const {
    data: ticket,
    isLoading,
    isError,
  } = useQuery<LookupTicketResponse>({
    queryKey: ["lookup-tickets", searchParams],
    queryFn: () => lookupTickets(searchParams!),
    enabled: !!searchParams,
  });

  // Show error modal when there's an error
  useEffect(() => {
    if (isError && searchParams) {
      setIsErrorModalOpen(true);
    }
  }, [isError, searchParams]);

  useEffect(() => {
    if (ticket) {
      setIsSuccessModalOpen(true);
    }
  }, [ticket]);

  const onSubmit = (data: LookupFormData) => {
    setSearchParams({
      ...data,
      phone_number: data.phone_number,
    });

    if (ticket) {
      setIsSuccessModalOpen(true);
    }
  };

  const handleDownloadSingleTicket = useCallback(
    async (ticket: any, isReturnTicket: boolean) => {
      if (!ticket || !ticket.order) {
        alert("Không tìm thấy thông tin vé hoặc đơn hàng.");
        return;
      }

      try {
        await downloadSingleTicketImage(ticket, ticket.order, isReturnTicket);
      } catch (error) {
        console.error("Lỗi khi tải vé:", error);
        alert("Có lỗi xảy ra khi tải vé. Vui lòng thử lại.");
      }
    },
    []
  );

  return (
    <Container className="bg-gray-100 min-h-[600px] pt-10">
      <div className="bg-white rounded-2xl p-8">
        <h1 className="text-2xl font-bold text-center mb-8">
          Tra cứu thông tin đặt vé
        </h1>
        <div className="max-w-2xl mx-auto mb-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Nhập số điện thoại"
                          type="tel"
                          className="h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="ticket_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Nhập mã vé"
                          className="h-12"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-center">
                <Button
                  type="submit"
                  className="min-w-[120px] h-12"
                  disabled={isLoading}
                >
                  Tra cứu
                </Button>
              </div>
            </form>
          </Form>
        </div>

        {isLoading && searchParams && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}

        {ticket && isSuccessModalOpen && (
          <div className="mt-8 flex flex-col gap-8 items-center">
            {/* Mobile Card */}
            <div className="block md:hidden w-full">
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                {/* Backdrop */}
                <div
                  className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fade-in"
                  onClick={() => setIsSuccessModalOpen(false)}
                />
                {/* Modal content */}
                <div className="relative bg-white rounded-2xl shadow-2xl max-w-screen-2xl w-[90vw] p-8 flex flex-col gap-6 max-h-[90vh] overflow-y-auto animate-fade-in">
                  {/* Close button */}
                  <button
                    className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 focus:outline-none"
                    onClick={() => setIsSuccessModalOpen(false)}
                  >
                    <XMarkIcon className="w-6 h-6 text-gray-500" />
                  </button>
                  <div className="flex flex-col gap-4 w-full mt-6">
                    <MobileTicketCard
                      ticket={ticket as any}
                      orderData={ticket.order as any}
                      isReturn={ticket.ticket_type === "Return"}
                      onDownloadTicket={handleDownloadSingleTicket}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Desktop Card */}
            <div className="hidden md:block w-full">
              <div className="fixed inset-0 z-50 flex items-center justify-center">
                {/* Backdrop */}
                <div
                  className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fade-in"
                  onClick={() => setIsSuccessModalOpen(false)}
                />
                {/* Modal content */}
                <div className="relative bg-white rounded-2xl shadow-2xl max-w-screen-2xl w-[90vw] p-8 flex flex-col gap-6 max-h-[90vh] overflow-y-auto animate-fade-in">
                  {/* Close button */}
                  <button
                    className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 focus:outline-none"
                    onClick={() => setIsSuccessModalOpen(false)}
                  >
                    <XMarkIcon className="w-6 h-6 text-gray-500" />
                  </button>
                  <div className="flex flex-col gap-4 w-full mt-6">
                    <div className="hidden md:block">
                      <DesktopTicketCard
                        ticket={ticket as any}
                        orderData={ticket.order as any}
                        isReturn={ticket.ticket_type === "Return"}
                        onDownloadTicket={handleDownloadSingleTicket}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {searchParams && !isLoading && !ticket && !isError && (
          <div className="text-center text-gray-500 py-8">
            Không tìm thấy vé phù hợp với thông tin đã nhập.
          </div>
        )}
      </div>

      {/* Error Modal */}
      <ErrorModal
        isOpen={isErrorModalOpen}
        onClose={() => setIsErrorModalOpen(false)}
        title="Không tìm thấy dữ liệu vé"
        confirmText="Đồng ý"
      />
    </Container>
  );
}
