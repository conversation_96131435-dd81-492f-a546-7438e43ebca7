import { Link } from "@tanstack/react-router";
import { useMemo } from "react";
import { useSearchBusSchedules } from "@/features/booking/hooks/useBusSchedules";
import type { SearchBusSchedulesParams } from "@/features/booking/api/bus-schedules";
import type { Bus, RouteInfo, SearchParams } from "@/shared/types/bus";
import SearchFilters from "@/shared/components/SearchFilters";
import SearchResults from "@/shared/components/SearchResults";
import useFilters from "@/features/booking/hooks/useFilters";
import { SearchForm } from "@/components/home/<USER>";
import { SearchMobileHeader } from "@/shared/components/SearchMobileHeader";
import { convertFiltersToAPIParams } from "@/lib/utils";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { processBusSchedules } from "@/shared/utils/processBusSchedules";
import { updateBusSchedulesWithTranslations } from "@/features/booking/utils/busScheduleHelpers";
import { Container } from "@/shared/components";
import { subMinutes, format, parse, isSameDay } from "date-fns";

interface SearchPageContentProps {
  searchParams: {
    fromPlaceId?: number;
    toPlaceId?: number;
    fromPlaceName?: string;
    toPlaceName?: string;
    departureDate?: string;
    returnDate?: string;
    numTickets?: number;
    isRoundTrip?: boolean;
    focusDate?: "departure" | "return"; // Which date picker to focus
  };
}

export const SearchPageContent: React.FC<SearchPageContentProps> = ({
  searchParams,
}) => {
  const { t } = useTranslation();

  // Use the filters hook
  const { filters, setFilters } = useFilters([]);

  // Create search mobile header data with formatted dates
  const mobileHeaderData = useMemo(() => {
    return {
      fromLocation: searchParams.fromPlaceName || "",
      toLocation: searchParams.toPlaceName || "",
      departureDate: searchParams.departureDate || "",
      returnDate: searchParams.returnDate || "",
      numTickets: searchParams.numTickets || 1,
    };
  }, [searchParams]);

  // Prepare search parameters for departure trip API
  const departureBusSearchParams: SearchBusSchedulesParams | null =
    useMemo(() => {
      // Check if required parameters are available
      if (
        !searchParams.fromPlaceId ||
        !searchParams.toPlaceId ||
        !searchParams.departureDate
      ) {
        return null;
      }

      // Kiểm tra xem ngày hiện tại có trùng với departureDate không
      const currentDate = new Date();
      const departureDate = parse(
        searchParams.departureDate,
        "yyyy-MM-dd",
        new Date()
      );

      let departureTimeFrom: string;
      if (isSameDay(currentDate, departureDate)) {
        // Nếu là ngày hôm nay, lấy giờ hiện tại trừ 15 phút
        const nowMinus15 = subMinutes(currentDate, 15);
        departureTimeFrom = format(nowMinus15, "HH:mm");
      } else {
        // Nếu không phải ngày hôm nay, set về 00:00
        departureTimeFrom = "00:00";
      }

      const baseParams = {
        fromPlaceId: searchParams.fromPlaceId,
        toPlaceId: searchParams.toPlaceId,
        departureDate: searchParams.departureDate,
        numTickets: searchParams.numTickets || 1,
        departureTimeFrom,
      };

      // Add filter parameters
      const filterParams = convertFiltersToAPIParams(filters);

      return {
        ...baseParams,
        ...filterParams,
      };
    }, [searchParams, filters]);

  // Prepare search parameters for return trip API
  const returnBusSearchParams: SearchBusSchedulesParams | null = useMemo(() => {
    // Only prepare return search if we have return date and it's a round trip in URL params
    if (
      !searchParams.isRoundTrip ||
      !searchParams.returnDate ||
      !searchParams.fromPlaceId ||
      !searchParams.toPlaceId
    ) {
      return null;
    }

    // Kiểm tra xem ngày hiện tại có trùng với returnDate không
    const currentDate = new Date();
    const returnDate = parse(searchParams.returnDate, "yyyy-MM-dd", new Date());

    let departureTimeFrom: string;
    if (isSameDay(currentDate, returnDate)) {
      // Nếu là ngày hôm nay, lấy giờ hiện tại trừ 15 phút
      const nowMinus15 = subMinutes(currentDate, 15);
      departureTimeFrom = format(nowMinus15, "HH:mm");
    } else {
      // Nếu không phải ngày hôm nay, set về 00:00
      departureTimeFrom = "00:00";
    }

    const baseParams = {
      // Swap from and to for return trip
      fromPlaceId: searchParams.toPlaceId,
      toPlaceId: searchParams.fromPlaceId,
      departureDate: searchParams.returnDate, // Use returnDate from URL params as departureDate for the return trip
      numTickets: searchParams.numTickets || 1,
      departureTimeFrom,
    };

    // Add filter parameters
    const filterParams = convertFiltersToAPIParams(filters);

    return {
      ...baseParams,
      ...filterParams,
    };
  }, [searchParams, filters]);

  // Use TanStack Query hook for departure trip
  const {
    data: departureApiSchedules = [],
    isLoading: departureLoading,
    error: departureError,
    isError: isDepartureError,
  } = useSearchBusSchedules(
    departureBusSearchParams,
    !!departureBusSearchParams // Only fetch if we have valid parameters
  );

  // Use TanStack Query hook for return trip
  const { data: returnApiSchedules = [], isLoading: returnLoading } =
    useSearchBusSchedules(
      returnBusSearchParams,
      !!returnBusSearchParams // Only fetch if we have valid parameters
    );

  // Process departure API data using the helper function
  const departureBusSchedules: Bus[] = useMemo(() => {
    const schedules = processBusSchedules({
      schedules: departureApiSchedules,
      date: searchParams.departureDate || "",
      fromPlaceName: searchParams.fromPlaceName || "Quảng Phú (Cư Mgar)",
      toPlaceName: searchParams.toPlaceName || "BX Trung tâm Đà Nẵng",
      isReturnTrip: false,
    });
    return updateBusSchedulesWithTranslations(schedules, t);
  }, [
    departureApiSchedules,
    searchParams.departureDate,
    searchParams.fromPlaceName,
    searchParams.toPlaceName,
    t,
  ]);

  // Process return API data using the helper function
  const returnBusSchedules: Bus[] = useMemo(() => {
    const schedules = processBusSchedules({
      schedules: returnApiSchedules,
      date: searchParams.returnDate || "",
      fromPlaceName: searchParams.fromPlaceName || "Quảng Phú (Cư Mgar)",
      toPlaceName: searchParams.toPlaceName || "BX Trung tâm Đà Nẵng",
      isReturnTrip: true,
    });
    return updateBusSchedulesWithTranslations(schedules, t);
  }, [
    returnApiSchedules,
    searchParams.returnDate,
    searchParams.fromPlaceName,
    searchParams.toPlaceName,
    t,
  ]);

  // Create route info for the header
  const routeInfo: RouteInfo = {
    from: searchParams.fromPlaceName || "Bình Định",
    to: searchParams.toPlaceName || "Đà Nẵng",
    date: searchParams.departureDate || "",
    totalBuses: departureBusSchedules.length,
  };

  const searchParamsForCards: SearchParams = {
    fromPlaceId: searchParams.fromPlaceId,
    toPlaceId: searchParams.toPlaceId,
    fromPlaceName: searchParams.fromPlaceName,
    toPlaceName: searchParams.toPlaceName,
    departureDate: searchParams.departureDate,
    returnDate: searchParams.returnDate,
    numTickets: searchParams.numTickets,
    isRoundTrip: searchParams.isRoundTrip,
  };

  // Handle missing required parameters
  if (!departureBusSearchParams) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center py-5 bg-yellow-100 p-8 rounded-lg max-w-md">
          <p className="text-lg text-yellow-700 mb-4">
            {t("search.missingSearchInfo")}
          </p>
          <Link
            to="/"
            search={{}}
            className="text-indigo-600 hover:text-indigo-800 font-medium py-2 px-6 border border-indigo-600 rounded-md hover:bg-indigo-50 transition-colors duration-200 ease-in-out"
          >
            &laquo; {t("search.newSearch")}
          </Link>
        </div>
      </div>
    );
  }

  if (isDepartureError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center py-5 bg-red-100 p-8 rounded-lg max-w-md">
          <p className="text-lg text-red-700 mb-4">
            Lỗi:{" "}
            {departureError?.message || "Không thể tải thông tin chuyến xe"}
          </p>
          <Link
            to="/"
            search={{}}
            className="text-indigo-600 hover:text-indigo-800 font-medium py-2 px-6 border border-indigo-600 rounded-md hover:bg-indigo-50 transition-colors duration-200 ease-in-out"
          >
            &laquo; {t("search.newSearch")}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header - Show on mobile/tablet for filter modal */}
      <div className="sticky top-0 z-50 bg-white shadow-sm">
        <SearchMobileHeader
          {...mobileHeaderData}
          filters={filters}
          setFilters={setFilters}
        />
      </div>

      {/* Main Content Container */}
      <Container className="px-2">
        {/* Desktop Search Form */}
        <div className="hidden pt-10 lg:block">
          <SearchForm />
        </div>

        {/* Main content area - Responsive Layout */}
        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6 mt-4 lg:mt-0 pb-10">
          {/* Filters - Hidden on mobile, compact sidebar on desktop */}
          <div className="hidden lg:block lg:w-[240px] xl:w-[280px] lg:flex-shrink-0">
            <SearchFilters
              activeFilters={filters}
              onFiltersChange={setFilters}
            />
          </div>

          {/* Results - Full width on mobile, flexible on desktop */}

          <div className="flex-1 min-w-0">
            <SearchResults
              departureBuses={departureBusSchedules}
              returnBuses={returnBusSchedules}
              isDepartureLoading={departureLoading}
              isReturnLoading={returnLoading}
              route={routeInfo}
              searchParams={searchParamsForCards}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};
