import { Store } from "@tanstack/react-store";
import type {
  MultipleRestaurantCartData,
  RestaurantCartData,
  CartItem,
} from "@/features/merchants/types";

interface CartStore {
  multipleRestaurantCartData: MultipleRestaurantCartData | null;
}

export const cartStore = new Store<CartStore>({
  multipleRestaurantCartData: null,
});

// Helper functions
const calculateRestaurantTotals = (
  items: CartItem[]
): { totalItems: number; totalPrice: number } => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  return { totalItems, totalPrice };
};

const calculateGlobalTotals = (
  restaurants: RestaurantCartData[]
): { totalItems: number; totalPrice: number } => {
  const totalItems = restaurants.reduce(
    (sum, restaurant) => sum + restaurant.totalItems,
    0
  );
  const totalPrice = restaurants.reduce(
    (sum, restaurant) => sum + restaurant.totalPrice,
    0
  );
  return { totalItems, totalPrice };
};

// Actions để update store
export const cartActions = {
  addItemToRestaurant: (
    restaurantId: string,
    restaurantName: string,
    item: CartItem
  ) => {
    cartStore.setState((state) => {
      const currentMultiCart = state.multipleRestaurantCartData;
      const restaurants = currentMultiCart?.restaurants || [];

      const existingRestaurantIndex = restaurants.findIndex(
        (r) => r.restaurantId === restaurantId
      );

      if (existingRestaurantIndex >= 0) {
        // Restaurant exists, update items
        const existingRestaurant = restaurants[existingRestaurantIndex];
        const existingItemIndex = existingRestaurant.items.findIndex(
          (i) => i.id === item.id
        );

        let updatedItems;
        if (existingItemIndex >= 0) {
          // Item exists, update quantity
          updatedItems = existingRestaurant.items.map((i, idx) =>
            idx === existingItemIndex
              ? { ...i, quantity: i.quantity + item.quantity }
              : i
          );
        } else {
          // New item, add to restaurant
          updatedItems = [...existingRestaurant.items, item];
        }

        const { totalItems, totalPrice } =
          calculateRestaurantTotals(updatedItems);

        const updatedRestaurant: RestaurantCartData = {
          ...existingRestaurant,
          items: updatedItems,
          totalItems,
          totalPrice,
        };

        const updatedRestaurants = restaurants.map((r, idx) =>
          idx === existingRestaurantIndex ? updatedRestaurant : r
        );

        const { totalItems: globalTotalItems, totalPrice: globalTotalPrice } =
          calculateGlobalTotals(updatedRestaurants);

        return {
          ...state,
          multipleRestaurantCartData: {
            restaurants: updatedRestaurants,
            totalItems: globalTotalItems,
            totalPrice: globalTotalPrice,
          },
        };
      } else {
        // New restaurant
        const { totalItems, totalPrice } = calculateRestaurantTotals([item]);

        const newRestaurant: RestaurantCartData = {
          restaurantId,
          restaurantName,
          items: [item],
          totalItems,
          totalPrice,
        };

        const updatedRestaurants = [...restaurants, newRestaurant];
        const { totalItems: globalTotalItems, totalPrice: globalTotalPrice } =
          calculateGlobalTotals(updatedRestaurants);

        return {
          ...state,
          multipleRestaurantCartData: {
            restaurants: updatedRestaurants,
            totalItems: globalTotalItems,
            totalPrice: globalTotalPrice,
          },
        };
      }
    });
  },

  updateItemQuantityInRestaurant: (
    restaurantId: string,
    itemId: string,
    quantity: number
  ) => {
    cartStore.setState((state) => {
      if (!state.multipleRestaurantCartData) return state;

      const restaurants = state.multipleRestaurantCartData.restaurants
        .map((restaurant) => {
          if (restaurant.restaurantId !== restaurantId) return restaurant;

          const updatedItems = restaurant.items
            .map((item) => (item.id === itemId ? { ...item, quantity } : item))
            .filter((item) => item.quantity > 0);

          const { totalItems, totalPrice } =
            calculateRestaurantTotals(updatedItems);

          return {
            ...restaurant,
            items: updatedItems,
            totalItems,
            totalPrice,
          };
        })
        .filter((restaurant) => restaurant.items.length > 0); // Remove empty restaurants

      if (restaurants.length === 0) {
        return {
          ...state,
          multipleRestaurantCartData: null,
        };
      }

      const { totalItems: globalTotalItems, totalPrice: globalTotalPrice } =
        calculateGlobalTotals(restaurants);

      return {
        ...state,
        multipleRestaurantCartData: {
          restaurants,
          totalItems: globalTotalItems,
          totalPrice: globalTotalPrice,
        },
      };
    });
  },

  removeRestaurant: (restaurantId: string) => {
    cartStore.setState((state) => {
      if (!state.multipleRestaurantCartData) return state;

      const restaurants = state.multipleRestaurantCartData.restaurants.filter(
        (restaurant) => restaurant.restaurantId !== restaurantId
      );

      if (restaurants.length === 0) {
        return {
          ...state,
          multipleRestaurantCartData: null,
        };
      }

      const { totalItems: globalTotalItems, totalPrice: globalTotalPrice } =
        calculateGlobalTotals(restaurants);

      return {
        ...state,
        multipleRestaurantCartData: {
          restaurants,
          totalItems: globalTotalItems,
          totalPrice: globalTotalPrice,
        },
      };
    });
  },

  clearMultipleRestaurantCart: () => {
    cartStore.setState((state) => ({
      ...state,
      multipleRestaurantCartData: null,
    }));
  },

  clearAllCarts: () => {
    cartStore.setState((state) => ({
      ...state,
      multipleRestaurantCartData: null,
    }));
  },
};
