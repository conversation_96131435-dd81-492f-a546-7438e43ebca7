import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createFleet,
  getFleets,
  getFleetById,
  updateFleet,
  deleteFleet,
} from "@/features/booking/api/fleetsApi";
import type { CreateFleetRequest, ApiError } from "@/shared/types/auth";

// Query keys
export const fleetKeys = {
  all: ["fleets"] as const,
  lists: () => [...fleetKeys.all, "list"] as const,
  list: (filters: string) => [...fleetKeys.lists(), { filters }] as const,
  details: () => [...fleetKeys.all, "detail"] as const,
  detail: (id: number) => [...fleetKeys.details(), id] as const,
};

// Get all fleets query
export const useFleets = () => {
  return useQuery({
    queryKey: fleetKeys.lists(),
    queryFn: () => getFleets(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get fleet by ID query
export const useFleetById = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: fleetKeys.detail(id),
    queryFn: () => getFleetById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create fleet mutation
export const useCreateFleet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFleetRequest) => createFleet(data),
    onSuccess: () => {
      // Invalidate fleets list to refetch
      queryClient.invalidateQueries({ queryKey: fleetKeys.lists() });
    },
    onError: (error: ApiError) => {
      console.error("Create fleet failed:", error);
    },
  });
};

// Update fleet mutation
export const useUpdateFleet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: Partial<CreateFleetRequest>;
    }) => updateFleet(id, data),
    onSuccess: (updatedFleet) => {
      // Update the specific fleet in cache
      queryClient.setQueryData(fleetKeys.detail(updatedFleet.id), updatedFleet);
      // Invalidate fleets list to refetch
      queryClient.invalidateQueries({ queryKey: fleetKeys.lists() });
    },
    onError: (error: ApiError) => {
      console.error("Update fleet failed:", error);
    },
  });
};

// Delete fleet mutation
export const useDeleteFleet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteFleet(id),
    onSuccess: (_, deletedId) => {
      // Remove the specific fleet from cache
      queryClient.removeQueries({ queryKey: fleetKeys.detail(deletedId) });
      // Invalidate fleets list to refetch
      queryClient.invalidateQueries({ queryKey: fleetKeys.lists() });
    },
    onError: (error: ApiError) => {
      console.error("Delete fleet failed:", error);
    },
  });
};
