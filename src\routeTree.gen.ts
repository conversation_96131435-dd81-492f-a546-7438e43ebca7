/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TicketSearchImport } from './routes/ticket-search'
import { Route as TestTicketImport } from './routes/test-ticket'
import { Route as ScheduleImport } from './routes/schedule'
import { Route as RegisterImport } from './routes/register'
import { Route as PaymentSuccessImport } from './routes/payment-success'
import { Route as NewsImport } from './routes/news'
import { Route as LookUpTicketsImport } from './routes/look-up-tickets'
import { Route as LoginImport } from './routes/login'
import { Route as ContactImport } from './routes/contact'
import { Route as AuthImport } from './routes/auth'
import { Route as AccountManagementImport } from './routes/account-management'
import { Route as AboutMeImport } from './routes/about-me'
import { Route as IndexImport } from './routes/index'
import { Route as MerchantsIndexImport } from './routes/merchants/index'
import { Route as StorePromotionIdImport } from './routes/store-promotion.$id'
import { Route as PromotionIdImport } from './routes/promotion.$id'
import { Route as NewsDetailIdImport } from './routes/news-detail.$id'
import { Route as MerchantsQrPaymentImport } from './routes/merchants/qr-payment'
import { Route as MerchantsOrdersImport } from './routes/merchants/orders'
import { Route as MerchantsOrderSuccessImport } from './routes/merchants/order-success'
import { Route as MerchantsCheckPaymentStatusImport } from './routes/merchants/check-payment-status'
import { Route as BookingSearchImport } from './routes/booking/search'
import { Route as BookingPaymentVerificationImport } from './routes/booking/payment-verification'
import { Route as BookingPaymentInfoImport } from './routes/booking/payment-info'
import { Route as BookingCheckoutImport } from './routes/booking/checkout'
import { Route as MerchantsUpdateItemsDetailIdItemImport } from './routes/merchants/update-items-detail.$idItem'
import { Route as MerchantsOrderConfirmationIdImport } from './routes/merchants/order-confirmation.$id'
import { Route as MerchantsItemDetailIdImport } from './routes/merchants/item-detail.$id'

// Create/Update Routes

const TicketSearchRoute = TicketSearchImport.update({
  id: '/ticket-search',
  path: '/ticket-search',
  getParentRoute: () => rootRoute,
} as any)

const TestTicketRoute = TestTicketImport.update({
  id: '/test-ticket',
  path: '/test-ticket',
  getParentRoute: () => rootRoute,
} as any)

const ScheduleRoute = ScheduleImport.update({
  id: '/schedule',
  path: '/schedule',
  getParentRoute: () => rootRoute,
} as any)

const RegisterRoute = RegisterImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRoute,
} as any)

const PaymentSuccessRoute = PaymentSuccessImport.update({
  id: '/payment-success',
  path: '/payment-success',
  getParentRoute: () => rootRoute,
} as any)

const NewsRoute = NewsImport.update({
  id: '/news',
  path: '/news',
  getParentRoute: () => rootRoute,
} as any)

const LookUpTicketsRoute = LookUpTicketsImport.update({
  id: '/look-up-tickets',
  path: '/look-up-tickets',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const ContactRoute = ContactImport.update({
  id: '/contact',
  path: '/contact',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const AccountManagementRoute = AccountManagementImport.update({
  id: '/account-management',
  path: '/account-management',
  getParentRoute: () => rootRoute,
} as any)

const AboutMeRoute = AboutMeImport.update({
  id: '/about-me',
  path: '/about-me',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsIndexRoute = MerchantsIndexImport.update({
  id: '/merchants/',
  path: '/merchants/',
  getParentRoute: () => rootRoute,
} as any)

const StorePromotionIdRoute = StorePromotionIdImport.update({
  id: '/store-promotion/$id',
  path: '/store-promotion/$id',
  getParentRoute: () => rootRoute,
} as any)

const PromotionIdRoute = PromotionIdImport.update({
  id: '/promotion/$id',
  path: '/promotion/$id',
  getParentRoute: () => rootRoute,
} as any)

const NewsDetailIdRoute = NewsDetailIdImport.update({
  id: '/news-detail/$id',
  path: '/news-detail/$id',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsQrPaymentRoute = MerchantsQrPaymentImport.update({
  id: '/merchants/qr-payment',
  path: '/merchants/qr-payment',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsOrdersRoute = MerchantsOrdersImport.update({
  id: '/merchants/orders',
  path: '/merchants/orders',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsOrderSuccessRoute = MerchantsOrderSuccessImport.update({
  id: '/merchants/order-success',
  path: '/merchants/order-success',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsCheckPaymentStatusRoute =
  MerchantsCheckPaymentStatusImport.update({
    id: '/merchants/check-payment-status',
    path: '/merchants/check-payment-status',
    getParentRoute: () => rootRoute,
  } as any)

const BookingSearchRoute = BookingSearchImport.update({
  id: '/booking/search',
  path: '/booking/search',
  getParentRoute: () => rootRoute,
} as any)

const BookingPaymentVerificationRoute = BookingPaymentVerificationImport.update(
  {
    id: '/booking/payment-verification',
    path: '/booking/payment-verification',
    getParentRoute: () => rootRoute,
  } as any,
)

const BookingPaymentInfoRoute = BookingPaymentInfoImport.update({
  id: '/booking/payment-info',
  path: '/booking/payment-info',
  getParentRoute: () => rootRoute,
} as any)

const BookingCheckoutRoute = BookingCheckoutImport.update({
  id: '/booking/checkout',
  path: '/booking/checkout',
  getParentRoute: () => rootRoute,
} as any)

const MerchantsUpdateItemsDetailIdItemRoute =
  MerchantsUpdateItemsDetailIdItemImport.update({
    id: '/merchants/update-items-detail/$idItem',
    path: '/merchants/update-items-detail/$idItem',
    getParentRoute: () => rootRoute,
  } as any)

const MerchantsOrderConfirmationIdRoute =
  MerchantsOrderConfirmationIdImport.update({
    id: '/merchants/order-confirmation/$id',
    path: '/merchants/order-confirmation/$id',
    getParentRoute: () => rootRoute,
  } as any)

const MerchantsItemDetailIdRoute = MerchantsItemDetailIdImport.update({
  id: '/merchants/item-detail/$id',
  path: '/merchants/item-detail/$id',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/about-me': {
      id: '/about-me'
      path: '/about-me'
      fullPath: '/about-me'
      preLoaderRoute: typeof AboutMeImport
      parentRoute: typeof rootRoute
    }
    '/account-management': {
      id: '/account-management'
      path: '/account-management'
      fullPath: '/account-management'
      preLoaderRoute: typeof AccountManagementImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/contact': {
      id: '/contact'
      path: '/contact'
      fullPath: '/contact'
      preLoaderRoute: typeof ContactImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/look-up-tickets': {
      id: '/look-up-tickets'
      path: '/look-up-tickets'
      fullPath: '/look-up-tickets'
      preLoaderRoute: typeof LookUpTicketsImport
      parentRoute: typeof rootRoute
    }
    '/news': {
      id: '/news'
      path: '/news'
      fullPath: '/news'
      preLoaderRoute: typeof NewsImport
      parentRoute: typeof rootRoute
    }
    '/payment-success': {
      id: '/payment-success'
      path: '/payment-success'
      fullPath: '/payment-success'
      preLoaderRoute: typeof PaymentSuccessImport
      parentRoute: typeof rootRoute
    }
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterImport
      parentRoute: typeof rootRoute
    }
    '/schedule': {
      id: '/schedule'
      path: '/schedule'
      fullPath: '/schedule'
      preLoaderRoute: typeof ScheduleImport
      parentRoute: typeof rootRoute
    }
    '/test-ticket': {
      id: '/test-ticket'
      path: '/test-ticket'
      fullPath: '/test-ticket'
      preLoaderRoute: typeof TestTicketImport
      parentRoute: typeof rootRoute
    }
    '/ticket-search': {
      id: '/ticket-search'
      path: '/ticket-search'
      fullPath: '/ticket-search'
      preLoaderRoute: typeof TicketSearchImport
      parentRoute: typeof rootRoute
    }
    '/booking/checkout': {
      id: '/booking/checkout'
      path: '/booking/checkout'
      fullPath: '/booking/checkout'
      preLoaderRoute: typeof BookingCheckoutImport
      parentRoute: typeof rootRoute
    }
    '/booking/payment-info': {
      id: '/booking/payment-info'
      path: '/booking/payment-info'
      fullPath: '/booking/payment-info'
      preLoaderRoute: typeof BookingPaymentInfoImport
      parentRoute: typeof rootRoute
    }
    '/booking/payment-verification': {
      id: '/booking/payment-verification'
      path: '/booking/payment-verification'
      fullPath: '/booking/payment-verification'
      preLoaderRoute: typeof BookingPaymentVerificationImport
      parentRoute: typeof rootRoute
    }
    '/booking/search': {
      id: '/booking/search'
      path: '/booking/search'
      fullPath: '/booking/search'
      preLoaderRoute: typeof BookingSearchImport
      parentRoute: typeof rootRoute
    }
    '/merchants/check-payment-status': {
      id: '/merchants/check-payment-status'
      path: '/merchants/check-payment-status'
      fullPath: '/merchants/check-payment-status'
      preLoaderRoute: typeof MerchantsCheckPaymentStatusImport
      parentRoute: typeof rootRoute
    }
    '/merchants/order-success': {
      id: '/merchants/order-success'
      path: '/merchants/order-success'
      fullPath: '/merchants/order-success'
      preLoaderRoute: typeof MerchantsOrderSuccessImport
      parentRoute: typeof rootRoute
    }
    '/merchants/orders': {
      id: '/merchants/orders'
      path: '/merchants/orders'
      fullPath: '/merchants/orders'
      preLoaderRoute: typeof MerchantsOrdersImport
      parentRoute: typeof rootRoute
    }
    '/merchants/qr-payment': {
      id: '/merchants/qr-payment'
      path: '/merchants/qr-payment'
      fullPath: '/merchants/qr-payment'
      preLoaderRoute: typeof MerchantsQrPaymentImport
      parentRoute: typeof rootRoute
    }
    '/news-detail/$id': {
      id: '/news-detail/$id'
      path: '/news-detail/$id'
      fullPath: '/news-detail/$id'
      preLoaderRoute: typeof NewsDetailIdImport
      parentRoute: typeof rootRoute
    }
    '/promotion/$id': {
      id: '/promotion/$id'
      path: '/promotion/$id'
      fullPath: '/promotion/$id'
      preLoaderRoute: typeof PromotionIdImport
      parentRoute: typeof rootRoute
    }
    '/store-promotion/$id': {
      id: '/store-promotion/$id'
      path: '/store-promotion/$id'
      fullPath: '/store-promotion/$id'
      preLoaderRoute: typeof StorePromotionIdImport
      parentRoute: typeof rootRoute
    }
    '/merchants/': {
      id: '/merchants/'
      path: '/merchants'
      fullPath: '/merchants'
      preLoaderRoute: typeof MerchantsIndexImport
      parentRoute: typeof rootRoute
    }
    '/merchants/item-detail/$id': {
      id: '/merchants/item-detail/$id'
      path: '/merchants/item-detail/$id'
      fullPath: '/merchants/item-detail/$id'
      preLoaderRoute: typeof MerchantsItemDetailIdImport
      parentRoute: typeof rootRoute
    }
    '/merchants/order-confirmation/$id': {
      id: '/merchants/order-confirmation/$id'
      path: '/merchants/order-confirmation/$id'
      fullPath: '/merchants/order-confirmation/$id'
      preLoaderRoute: typeof MerchantsOrderConfirmationIdImport
      parentRoute: typeof rootRoute
    }
    '/merchants/update-items-detail/$idItem': {
      id: '/merchants/update-items-detail/$idItem'
      path: '/merchants/update-items-detail/$idItem'
      fullPath: '/merchants/update-items-detail/$idItem'
      preLoaderRoute: typeof MerchantsUpdateItemsDetailIdItemImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about-me': typeof AboutMeRoute
  '/account-management': typeof AccountManagementRoute
  '/auth': typeof AuthRoute
  '/contact': typeof ContactRoute
  '/login': typeof LoginRoute
  '/look-up-tickets': typeof LookUpTicketsRoute
  '/news': typeof NewsRoute
  '/payment-success': typeof PaymentSuccessRoute
  '/register': typeof RegisterRoute
  '/schedule': typeof ScheduleRoute
  '/test-ticket': typeof TestTicketRoute
  '/ticket-search': typeof TicketSearchRoute
  '/booking/checkout': typeof BookingCheckoutRoute
  '/booking/payment-info': typeof BookingPaymentInfoRoute
  '/booking/payment-verification': typeof BookingPaymentVerificationRoute
  '/booking/search': typeof BookingSearchRoute
  '/merchants/check-payment-status': typeof MerchantsCheckPaymentStatusRoute
  '/merchants/order-success': typeof MerchantsOrderSuccessRoute
  '/merchants/orders': typeof MerchantsOrdersRoute
  '/merchants/qr-payment': typeof MerchantsQrPaymentRoute
  '/news-detail/$id': typeof NewsDetailIdRoute
  '/promotion/$id': typeof PromotionIdRoute
  '/store-promotion/$id': typeof StorePromotionIdRoute
  '/merchants': typeof MerchantsIndexRoute
  '/merchants/item-detail/$id': typeof MerchantsItemDetailIdRoute
  '/merchants/order-confirmation/$id': typeof MerchantsOrderConfirmationIdRoute
  '/merchants/update-items-detail/$idItem': typeof MerchantsUpdateItemsDetailIdItemRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about-me': typeof AboutMeRoute
  '/account-management': typeof AccountManagementRoute
  '/auth': typeof AuthRoute
  '/contact': typeof ContactRoute
  '/login': typeof LoginRoute
  '/look-up-tickets': typeof LookUpTicketsRoute
  '/news': typeof NewsRoute
  '/payment-success': typeof PaymentSuccessRoute
  '/register': typeof RegisterRoute
  '/schedule': typeof ScheduleRoute
  '/test-ticket': typeof TestTicketRoute
  '/ticket-search': typeof TicketSearchRoute
  '/booking/checkout': typeof BookingCheckoutRoute
  '/booking/payment-info': typeof BookingPaymentInfoRoute
  '/booking/payment-verification': typeof BookingPaymentVerificationRoute
  '/booking/search': typeof BookingSearchRoute
  '/merchants/check-payment-status': typeof MerchantsCheckPaymentStatusRoute
  '/merchants/order-success': typeof MerchantsOrderSuccessRoute
  '/merchants/orders': typeof MerchantsOrdersRoute
  '/merchants/qr-payment': typeof MerchantsQrPaymentRoute
  '/news-detail/$id': typeof NewsDetailIdRoute
  '/promotion/$id': typeof PromotionIdRoute
  '/store-promotion/$id': typeof StorePromotionIdRoute
  '/merchants': typeof MerchantsIndexRoute
  '/merchants/item-detail/$id': typeof MerchantsItemDetailIdRoute
  '/merchants/order-confirmation/$id': typeof MerchantsOrderConfirmationIdRoute
  '/merchants/update-items-detail/$idItem': typeof MerchantsUpdateItemsDetailIdItemRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/about-me': typeof AboutMeRoute
  '/account-management': typeof AccountManagementRoute
  '/auth': typeof AuthRoute
  '/contact': typeof ContactRoute
  '/login': typeof LoginRoute
  '/look-up-tickets': typeof LookUpTicketsRoute
  '/news': typeof NewsRoute
  '/payment-success': typeof PaymentSuccessRoute
  '/register': typeof RegisterRoute
  '/schedule': typeof ScheduleRoute
  '/test-ticket': typeof TestTicketRoute
  '/ticket-search': typeof TicketSearchRoute
  '/booking/checkout': typeof BookingCheckoutRoute
  '/booking/payment-info': typeof BookingPaymentInfoRoute
  '/booking/payment-verification': typeof BookingPaymentVerificationRoute
  '/booking/search': typeof BookingSearchRoute
  '/merchants/check-payment-status': typeof MerchantsCheckPaymentStatusRoute
  '/merchants/order-success': typeof MerchantsOrderSuccessRoute
  '/merchants/orders': typeof MerchantsOrdersRoute
  '/merchants/qr-payment': typeof MerchantsQrPaymentRoute
  '/news-detail/$id': typeof NewsDetailIdRoute
  '/promotion/$id': typeof PromotionIdRoute
  '/store-promotion/$id': typeof StorePromotionIdRoute
  '/merchants/': typeof MerchantsIndexRoute
  '/merchants/item-detail/$id': typeof MerchantsItemDetailIdRoute
  '/merchants/order-confirmation/$id': typeof MerchantsOrderConfirmationIdRoute
  '/merchants/update-items-detail/$idItem': typeof MerchantsUpdateItemsDetailIdItemRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about-me'
    | '/account-management'
    | '/auth'
    | '/contact'
    | '/login'
    | '/look-up-tickets'
    | '/news'
    | '/payment-success'
    | '/register'
    | '/schedule'
    | '/test-ticket'
    | '/ticket-search'
    | '/booking/checkout'
    | '/booking/payment-info'
    | '/booking/payment-verification'
    | '/booking/search'
    | '/merchants/check-payment-status'
    | '/merchants/order-success'
    | '/merchants/orders'
    | '/merchants/qr-payment'
    | '/news-detail/$id'
    | '/promotion/$id'
    | '/store-promotion/$id'
    | '/merchants'
    | '/merchants/item-detail/$id'
    | '/merchants/order-confirmation/$id'
    | '/merchants/update-items-detail/$idItem'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about-me'
    | '/account-management'
    | '/auth'
    | '/contact'
    | '/login'
    | '/look-up-tickets'
    | '/news'
    | '/payment-success'
    | '/register'
    | '/schedule'
    | '/test-ticket'
    | '/ticket-search'
    | '/booking/checkout'
    | '/booking/payment-info'
    | '/booking/payment-verification'
    | '/booking/search'
    | '/merchants/check-payment-status'
    | '/merchants/order-success'
    | '/merchants/orders'
    | '/merchants/qr-payment'
    | '/news-detail/$id'
    | '/promotion/$id'
    | '/store-promotion/$id'
    | '/merchants'
    | '/merchants/item-detail/$id'
    | '/merchants/order-confirmation/$id'
    | '/merchants/update-items-detail/$idItem'
  id:
    | '__root__'
    | '/'
    | '/about-me'
    | '/account-management'
    | '/auth'
    | '/contact'
    | '/login'
    | '/look-up-tickets'
    | '/news'
    | '/payment-success'
    | '/register'
    | '/schedule'
    | '/test-ticket'
    | '/ticket-search'
    | '/booking/checkout'
    | '/booking/payment-info'
    | '/booking/payment-verification'
    | '/booking/search'
    | '/merchants/check-payment-status'
    | '/merchants/order-success'
    | '/merchants/orders'
    | '/merchants/qr-payment'
    | '/news-detail/$id'
    | '/promotion/$id'
    | '/store-promotion/$id'
    | '/merchants/'
    | '/merchants/item-detail/$id'
    | '/merchants/order-confirmation/$id'
    | '/merchants/update-items-detail/$idItem'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutMeRoute: typeof AboutMeRoute
  AccountManagementRoute: typeof AccountManagementRoute
  AuthRoute: typeof AuthRoute
  ContactRoute: typeof ContactRoute
  LoginRoute: typeof LoginRoute
  LookUpTicketsRoute: typeof LookUpTicketsRoute
  NewsRoute: typeof NewsRoute
  PaymentSuccessRoute: typeof PaymentSuccessRoute
  RegisterRoute: typeof RegisterRoute
  ScheduleRoute: typeof ScheduleRoute
  TestTicketRoute: typeof TestTicketRoute
  TicketSearchRoute: typeof TicketSearchRoute
  BookingCheckoutRoute: typeof BookingCheckoutRoute
  BookingPaymentInfoRoute: typeof BookingPaymentInfoRoute
  BookingPaymentVerificationRoute: typeof BookingPaymentVerificationRoute
  BookingSearchRoute: typeof BookingSearchRoute
  MerchantsCheckPaymentStatusRoute: typeof MerchantsCheckPaymentStatusRoute
  MerchantsOrderSuccessRoute: typeof MerchantsOrderSuccessRoute
  MerchantsOrdersRoute: typeof MerchantsOrdersRoute
  MerchantsQrPaymentRoute: typeof MerchantsQrPaymentRoute
  NewsDetailIdRoute: typeof NewsDetailIdRoute
  PromotionIdRoute: typeof PromotionIdRoute
  StorePromotionIdRoute: typeof StorePromotionIdRoute
  MerchantsIndexRoute: typeof MerchantsIndexRoute
  MerchantsItemDetailIdRoute: typeof MerchantsItemDetailIdRoute
  MerchantsOrderConfirmationIdRoute: typeof MerchantsOrderConfirmationIdRoute
  MerchantsUpdateItemsDetailIdItemRoute: typeof MerchantsUpdateItemsDetailIdItemRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutMeRoute: AboutMeRoute,
  AccountManagementRoute: AccountManagementRoute,
  AuthRoute: AuthRoute,
  ContactRoute: ContactRoute,
  LoginRoute: LoginRoute,
  LookUpTicketsRoute: LookUpTicketsRoute,
  NewsRoute: NewsRoute,
  PaymentSuccessRoute: PaymentSuccessRoute,
  RegisterRoute: RegisterRoute,
  ScheduleRoute: ScheduleRoute,
  TestTicketRoute: TestTicketRoute,
  TicketSearchRoute: TicketSearchRoute,
  BookingCheckoutRoute: BookingCheckoutRoute,
  BookingPaymentInfoRoute: BookingPaymentInfoRoute,
  BookingPaymentVerificationRoute: BookingPaymentVerificationRoute,
  BookingSearchRoute: BookingSearchRoute,
  MerchantsCheckPaymentStatusRoute: MerchantsCheckPaymentStatusRoute,
  MerchantsOrderSuccessRoute: MerchantsOrderSuccessRoute,
  MerchantsOrdersRoute: MerchantsOrdersRoute,
  MerchantsQrPaymentRoute: MerchantsQrPaymentRoute,
  NewsDetailIdRoute: NewsDetailIdRoute,
  PromotionIdRoute: PromotionIdRoute,
  StorePromotionIdRoute: StorePromotionIdRoute,
  MerchantsIndexRoute: MerchantsIndexRoute,
  MerchantsItemDetailIdRoute: MerchantsItemDetailIdRoute,
  MerchantsOrderConfirmationIdRoute: MerchantsOrderConfirmationIdRoute,
  MerchantsUpdateItemsDetailIdItemRoute: MerchantsUpdateItemsDetailIdItemRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/about-me",
        "/account-management",
        "/auth",
        "/contact",
        "/login",
        "/look-up-tickets",
        "/news",
        "/payment-success",
        "/register",
        "/schedule",
        "/test-ticket",
        "/ticket-search",
        "/booking/checkout",
        "/booking/payment-info",
        "/booking/payment-verification",
        "/booking/search",
        "/merchants/check-payment-status",
        "/merchants/order-success",
        "/merchants/orders",
        "/merchants/qr-payment",
        "/news-detail/$id",
        "/promotion/$id",
        "/store-promotion/$id",
        "/merchants/",
        "/merchants/item-detail/$id",
        "/merchants/order-confirmation/$id",
        "/merchants/update-items-detail/$idItem"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/about-me": {
      "filePath": "about-me.tsx"
    },
    "/account-management": {
      "filePath": "account-management.tsx"
    },
    "/auth": {
      "filePath": "auth.tsx"
    },
    "/contact": {
      "filePath": "contact.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/look-up-tickets": {
      "filePath": "look-up-tickets.tsx"
    },
    "/news": {
      "filePath": "news.tsx"
    },
    "/payment-success": {
      "filePath": "payment-success.tsx"
    },
    "/register": {
      "filePath": "register.tsx"
    },
    "/schedule": {
      "filePath": "schedule.tsx"
    },
    "/test-ticket": {
      "filePath": "test-ticket.tsx"
    },
    "/ticket-search": {
      "filePath": "ticket-search.tsx"
    },
    "/booking/checkout": {
      "filePath": "booking/checkout.tsx"
    },
    "/booking/payment-info": {
      "filePath": "booking/payment-info.tsx"
    },
    "/booking/payment-verification": {
      "filePath": "booking/payment-verification.tsx"
    },
    "/booking/search": {
      "filePath": "booking/search.tsx"
    },
    "/merchants/check-payment-status": {
      "filePath": "merchants/check-payment-status.tsx"
    },
    "/merchants/order-success": {
      "filePath": "merchants/order-success.tsx"
    },
    "/merchants/orders": {
      "filePath": "merchants/orders.tsx"
    },
    "/merchants/qr-payment": {
      "filePath": "merchants/qr-payment.tsx"
    },
    "/news-detail/$id": {
      "filePath": "news-detail.$id.tsx"
    },
    "/promotion/$id": {
      "filePath": "promotion.$id.tsx"
    },
    "/store-promotion/$id": {
      "filePath": "store-promotion.$id.tsx"
    },
    "/merchants/": {
      "filePath": "merchants/index.tsx"
    },
    "/merchants/item-detail/$id": {
      "filePath": "merchants/item-detail.$id.tsx"
    },
    "/merchants/order-confirmation/$id": {
      "filePath": "merchants/order-confirmation.$id.tsx"
    },
    "/merchants/update-items-detail/$idItem": {
      "filePath": "merchants/update-items-detail.$idItem.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
