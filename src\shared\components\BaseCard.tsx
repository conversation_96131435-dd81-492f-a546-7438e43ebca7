import React, { memo, useState, useCallback } from "react";
import { Skeleton } from "@/shared/components/skeleton";

interface BaseCardProps {
  id: string;
  image: string;
  title: string;
  timeAgo: string;
  onClick?: () => void;
  // Styling configurations
  borderColor?: "EDEDED" | "EFEFEF";
  titleSize?: "12px" | "14px";
  titleLines?: 2 | 4;
  className?: string;
}

export const BaseCard: React.FC<BaseCardProps> = memo(
  ({
    image,
    title,
    onClick,
    borderColor = "EDEDED",
    titleSize = "14px",
    titleLines = 4,
    className = "",
  }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
    }, []);

    const handleImageError = useCallback(() => {
      setImageError(true);
      setImageLoaded(true);
    }, []);

    return (
      <div
        className={`relative group cursor-pointer transition-all duration-300 ${className}`}
        onClick={onClick}
      >
        {/* Card Container - Responsive: mobile 270x210, desktop 300x280 */}
        <div className="w-full lg:max-w-[300px] mx-auto relative min-h-[210px] lg:min-h-[280px] flex flex-col">
          {/* Image - Responsive height */}
          <div className="relative">
            {!imageLoaded && (
              <div className="absolute inset-0 z-10">
                <Skeleton className="w-full h-[126px] lg:h-[168px] rounded-t-[24px]" />
              </div>
            )}
            <img
              src={imageError ? "/placeholder-image.jpg" : image}
              alt={title}
              className={`w-full h-[126px] lg:h-[168px] object-cover rounded-t-[24px] shadow-[0px_1.85px_3.15px_0px_rgba(0,0,0,0),0px_8.15px_6.52px_0px_rgba(0,0,0,0.01),0px_20px_13px_0px_rgba(0,0,0,0.01),0px_38.52px_25.48px_0px_rgba(0,0,0,0.01),0px_64.81px_46.85px_0px_rgba(0,0,0,0.02),0px_100px_80px_0px_rgba(0,0,0,0.02)] transition-opacity duration-200 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </div>

          {/* Content - Responsive height */}
          <div
            className={`bg-white border-1 border-[#${borderColor}] rounded-b-[24px] p-3 lg:p-4 flex-1 flex flex-col justify-between min-h-[84px] lg:min-h-[112px]`}
          >
            {/* Title */}
            <div className="mb-2">
              <h3
                className={`text-[#5C5C5C] font-mulish font-bold text-[${titleSize}] line-clamp-${titleLines} leading-[1.3]`}
              >
                {title}
              </h3>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

BaseCard.displayName = "BaseCard";
