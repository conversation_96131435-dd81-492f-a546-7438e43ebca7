import axiosInstance from "@/shared/api/axiosInstance";
import type { QRApiResponse } from "./qr.types";

// GET /api/v1/food-court/merchants/qr/{qrCode} - Get table and merchants info by QR code
export const getQRInfo = async (qrCode: string): Promise<QRApiResponse> => {
  const response = await axiosInstance.get<QRApiResponse>(
    `/api/v1/food-court/merchants/qr/${qrCode}`
  );
  return response.data;
};
