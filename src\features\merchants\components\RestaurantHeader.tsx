import React from "react";
import { useNavigate } from "@tanstack/react-router";
import { PhoneIcon } from "@heroicons/react/24/outline";
import { ChevronLeftIcon } from "@heroicons/react/16/solid";
import { DEFAULT_PLACEHOLDER } from "@/features/merchants/constants";
// import { getOperatingStatus } from "@/features/merchants/utils";
import type { Restaurant } from "@/features/merchants/types";

interface RestaurantHeaderProps {
  restaurant: Restaurant;
}

export const RestaurantHeader: React.FC<RestaurantHeaderProps> = React.memo(
  ({ restaurant }) => {
    const navigate = useNavigate();
    // const operatingStatus = getOperatingStatus(
    //   restaurant.operating_hours_start,
    //   restaurant.operating_hours_end
    // );

    return (
      <div className="relative">
        {/* Background Image */}
        <div className="h-[200px] bg-white">
          <img
            src={DEFAULT_PLACEHOLDER}
            alt={restaurant.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Header Navigation */}
        <div className="absolute top-4 left-0 right-0 px-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate({ to: "/merchants" })}
              className="w-10 h-10 rounded-full bg-black/50 flex items-center justify-center backdrop-blur-sm"
            >
              <ChevronLeftIcon className="w-6 h-6 text-white" />
            </button>
          </div>
        </div>

        {/* Restaurant Info Card - Overlapping the image */}
        <div className="bg-white rounded-tl-[30px] rounded-tr-[30px] px-5 pt-4 pb-2 -mt-16 relative z-10">
          {/* Restaurant Header */}
          <div className="flex items-center gap-3 mb-2">
            <div className="w-[84px] h-[84px] rounded-lg bg-white overflow-hidden shadow-sm">
              <img
                src={DEFAULT_PLACEHOLDER}
                alt={restaurant.name}
                className="w-full h-full object-cover"
              />
            </div>

            <div className="flex-1 flex-rows">
              <h1 className="text-lg font-bold text-[#181818] mb-2">
                {restaurant.name}
              </h1>

              <div className="flex items-center gap-1">
                <PhoneIcon className="w-4 h-4 text-[#7C7B7B]" />
                <span className="text-xs font-normal text-[#181818]">
                  {restaurant.contact_phone}
                </span>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="h-px bg-[#EDEDED] mt-2" />
        </div>
      </div>
    );
  }
);
