import type { Merchant } from "@/api/merchants/merchants.types";
import { PhoneIcon } from "@heroicons/react/24/outline";
import { useNavigate } from "@tanstack/react-router";
import { Route as MerchantsRoute } from "@/routes/merchants";

export default function MerchantCard({ merchant }: { merchant: Merchant }) {
  const navigate = useNavigate();
  const { qr } = MerchantsRoute.useSearch() as { qr?: string };
  return (
    <div
      onClick={() =>
        navigate({
          to: "/merchants/order-confirmation/$id",
          params: { id: merchant.id },
          search: { qr: qr ?? "" },
        })
      }
      className="w-full"
    >
      <div className="flex gap-4 mb-5">
        <div className="h-20 w-20 rounded-2xl">
          <img
            className="h-20 w-20 rounded-2xl object-contain"
            src={merchant.cover_image ?? "assets/merchants/merchant1.png"}
            alt="merchants Logo"
          />
        </div>
        <div className="text-start">
          <p className="text-base font-medium text-[#181818] mb-2">
            {merchant.name}
          </p>
          <div className="flex items-center gap-2 mb-2">
            {/* <p className="text-xs text-[#369926]">
              {merchant.is_active ? "Đang mở cửa" : "Đang đóng cửa"}
            </p> */}
            <p className="h-[2px] w-[2px] rounded-full bg-[#C1C7D0]" />
            <p className="text-xs text-[#5C5C5C]">Có mang đi</p>
          </div>
          <div className="flex gap-2 items-center">
            <PhoneIcon className="w-5 h-5 text-[#7C7B7B]" />
            <p className="text-xs text-[#172B4D]">{merchant.contact_phone}</p>
          </div>
        </div>
      </div>
      <p className="h-[1px] w-full bg-[#EDEDED]" />
    </div>
  );
}
