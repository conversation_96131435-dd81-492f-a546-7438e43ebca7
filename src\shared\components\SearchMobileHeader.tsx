import React, { useState, useMemo } from "react";
import { ArrowLeftIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useNavigate } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent } from "@/shared/components/dialog";
import { useSearchForm } from "@/contexts/SearchFormContext";
import { SearchForm } from "@/components/home/<USER>";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { formatDateRange } from "@/lib/dateUtils";
import SearchFilters from "./SearchFilters";
import type { BusFilters } from "@/shared/types/bus";

// Custom Filter Icon Component based on Figma design
const FilterIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* First slider */}
    <line
      x1="4"
      y1="6"
      x2="20"
      y2="6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="8" cy="6" r="2" fill="currentColor" />

    {/* Second slider */}
    <line
      x1="4"
      y1="12"
      x2="20"
      y2="12"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="14" cy="12" r="2" fill="currentColor" />

    {/* Third slider */}
    <line
      x1="4"
      y1="18"
      x2="20"
      y2="18"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="10" cy="18" r="2" fill="currentColor" />
  </svg>
);

// Mobile Filter Modal Component
const MobileFilterModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  filters: BusFilters;
  setFilters: (filters: BusFilters) => void;
}> = ({ isOpen, onClose, filters, setFilters }) => {
  const { t } = useTranslation();

  const closeButton = (
    <button
      onClick={onClose}
      className="w-8 h-8 flex items-center justify-center text-[#7C7B7B] hover:text-[#181818] hover:bg-[#F8F8F8] transition-all duration-200 rounded-full"
      aria-label={t("common.close")}
    >
      <XMarkIcon className="w-5 h-5" strokeWidth={2} />
    </button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full h-full max-w-none max-h-none p-0 gap-0 bg-white rounded-none [&>button]:hidden">
        <div className="flex flex-col h-full">
          {/* Filter Content */}
          <div className="flex-1 overflow-y-auto px-4 py-6 bg-gray-100">
            <div className="w-full">
              {/* Custom mobile-friendly filter layout */}
              <div className="space-y-4">
                <SearchFilters
                  activeFilters={filters}
                  onFiltersChange={setFilters}
                  closeButton={closeButton}
                />
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface SearchMobileHeaderProps {
  fromLocation?: string;
  toLocation?: string;
  departureDate?: string;
  returnDate?: string;
  numTickets?: number;
  className?: string;
  filters: BusFilters;
  setFilters: (filters: BusFilters) => void;
}

export const SearchMobileHeader: React.FC<SearchMobileHeaderProps> = ({
  fromLocation = "Bình Định",
  toLocation = "Đà Nẵng",
  departureDate = "29/05",
  returnDate = "08/06",
  numTickets = 5,
  className,
  filters,
  setFilters,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // Use the shared search form context for display only
  const { state, addSubmitSuccessCallback, removeSubmitSuccessCallback } =
    useSearchForm();

  // Set up callback to close modal on successful form submission
  React.useEffect(() => {
    const handleFormSubmitSuccess = () => {
      setIsSearchModalOpen(false);
    };

    addSubmitSuccessCallback(handleFormSubmitSuccess);

    // Cleanup callback when component unmounts
    return () => {
      removeSubmitSuccessCallback(handleFormSubmitSuccess);
    };
  }, [addSubmitSuccessCallback, removeSubmitSuccessCallback]);

  const handleBack = () => {
    navigate({ to: "/" });
  };

  const handleOpenSearchModal = () => {
    setIsSearchModalOpen(true);
  };

  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  const handleCloseFilterModal = () => {
    setIsFilterModalOpen(false);
  };

  const dateRangeDisplay = useMemo(() => {
    return formatDateRange(
      state.departureDate || departureDate,
      state.returnDate || returnDate
    );
  }, [state.departureDate, state.returnDate, departureDate, returnDate]);

  return (
    <>
      <div
        className={cn(
          "lg:hidden w-full bg-white flex items-center justify-between gap-8 px-6 py-3",
          className
        )}
      >
        {/* Back Arrow */}
        <button
          onClick={handleBack}
          className="flex-shrink-0 w-6 h-6 text-[#7C7B7B] hover:text-[#181818] transition-colors"
          aria-label={t("common.previous")}
        >
          <ArrowLeftIcon className="w-6 h-6" />
        </button>

        {/* Search Info Fields - Clickable to open filter modal */}
        <button
          onClick={handleOpenSearchModal}
          className="flex-1 bg-white rounded-[35px] border border-[#EDEDED] shadow-sm px-6 py-2.5 flex flex-col gap-1 text-left items-center"
        >
          {/* Route Information */}
          <div className="flex items-center">
            <span className="text-[#181818] font-mulish font-bold text-sm leading-[18px] truncate">
              {state.fromLocation || fromLocation} -{" "}
              {state.toLocation || toLocation}
            </span>
          </div>

          {/* Date and Tickets Information */}
          <div className="flex items-center gap-2 text-xs">
            <span className="text-[#5C5C5C] font-mulish font-normal leading-4">
              {dateRangeDisplay}
            </span>

            {/* Separator Dot */}
            <div className="w-1 h-1 bg-[#D9D9D9] rounded-full flex-shrink-0"></div>

            <span className="text-[#5C5C5C] font-mulish font-normal leading-4">
              {state.numTickets || numTickets} vé
            </span>
          </div>
        </button>

        {/* Filter Icon - Clickable to open search modal */}
        <button
          onClick={handleOpenFilterModal}
          className="flex-shrink-0 w-6 h-6 text-[#7C7B7B] hover:text-[#181818] transition-colors"
          aria-label={t("common.edit")}
        >
          <FilterIcon className="w-6 h-6" />
        </button>
      </div>

      {/* Search Modal */}
      <Dialog open={isSearchModalOpen} onOpenChange={setIsSearchModalOpen}>
        <DialogContent className="w-[calc(100vw-16px)] max-w-[1200px] max-h-[90vh] gap-0 bg-transparent border-0 shadow-none rounded-2xl [&>button]:hidden overflow-y-auto p-0">
          <div className="w-full">
            <SearchForm />
          </div>
        </DialogContent>
      </Dialog>

      {/* Filter Modal */}
      <MobileFilterModal
        isOpen={isFilterModalOpen}
        onClose={handleCloseFilterModal}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};
