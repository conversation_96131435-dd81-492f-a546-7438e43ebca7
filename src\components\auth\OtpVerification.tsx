import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/shared/components/button";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

interface OtpVerificationProps {
  onVerifySuccess?: (otp: string) => void;
  onBack?: () => void;
  onResendOtp?: () => void;
  className?: string;
  isLoading?: boolean;
  isResendLoading?: boolean;
  error?: string;
  onError?: (error: string) => void;
}

export const OtpVerification: React.FC<OtpVerificationProps> = ({
  onVerifySuccess,
  onBack,
  onResendOtp,
  className,
  isLoading = false,
  isResendLoading = false,
  error: externalError,
  onError,
}) => {
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [localError, setLocalError] = useState("");
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Use external error if provided, otherwise use local error
  const error = externalError || localError;

  useEffect(() => {
    // Focus on first input when component mounts
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleInputChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Clear errors when user starts typing
    if (error) {
      setLocalError("");
      onError?.("");
    }

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData("text").trim();

    if (/^\d{6}$/.test(pasteData)) {
      const newOtp = pasteData.split("");
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
      // Clear errors when pasting
      if (error) {
        setLocalError("");
        onError?.("");
      }
    }
  };

  const validateOtp = () => {
    const otpString = otp.join("");
    if (otpString.length !== 6) {
      const errorMsg = "Vui lòng nhập đủ 6 số OTP";
      setLocalError(errorMsg);
      onError?.(errorMsg);
      return false;
    }
    return true;
  };

  const handleVerify = () => {
    if (!validateOtp()) return;
    const otpString = otp.join("");
    onVerifySuccess?.(otpString);
  };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="space-y-12">
        {/* Header */}
        <div className="space-y-4">
          {/* Back Button */}
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="flex items-center gap-4 text-[#5C5C5C] hover:text-[#181818] transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
              <span className="text-[18px] font-normal">Quay lại Đăng ký</span>
            </button>
          </div>

          {/* Title and Description */}
          <div className="text-start space-y-4">
            <h2 className="text-[32px] font-extrabold text-[#181818] leading-tight">
              Xác thực
            </h2>
            <p className="text-[18px] text-[#5C5C5C] leading-relaxed">
              Nhập mã OTP được gửi đến số điện thoại Quý khách đã đăng ký.
            </p>

            {/* OTP Illustration */}
            <div className="flex justify-center py-4">
              <img
                src="/assets/OTP.svg"
                alt="OTP Verification"
                className="w-24 h-24"
              />
            </div>
          </div>
        </div>

        {/* OTP Input */}
        <div className="space-y-6">
          {/* OTP Fields */}
          <div className="flex justify-center gap-2">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={(el) => {
                  inputRefs.current[index] = el;
                }}
                type="text"
                maxLength={1}
                value={digit}
                onChange={(e) => handleInputChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                className={cn(
                  "w-12 h-12 text-center text-[18px] font-bold border rounded-lg",
                  "focus:outline-none focus:ring-2 focus:ring-[#FF7F37] focus:border-[#FF7F37]",
                  error
                    ? "border-red-500 bg-red-50"
                    : digit
                      ? "border-[#FF7F37] bg-[#F8F8F8]"
                      : "border-gray-300 bg-[#F8F8F8]"
                )}
              />
            ))}
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-center">
              <p className="text-red-500 text-sm font-medium">{error}</p>
            </div>
          )}

          {/* Resend OTP */}
          <div className="text-center">
            <p className="text-[14px] text-[#5C5C5C]">
              Quý khách chưa nhận được OTP?{" "}
              <button
                onClick={onResendOtp}
                disabled={isResendLoading}
                className="text-[#2D5BFF] hover:underline font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResendLoading ? "Đang gửi..." : "Gửi lại OTP"}
              </button>
            </p>
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleVerify}
            disabled={isLoading || otp.join("").length !== 6}
            className="w-full bg-[#2D5BFF] hover:bg-[#2D5BFF]/90 text-white py-6 rounded-lg font-extrabold text-[18px] tracking-wide disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Đang xác nhận..." : "Xác nhận"}
          </Button>
        </div>
      </div>
    </div>
  );
};
