import React from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { TripTypeToggle } from "./TripTypeToggle";
import { SearchFormFields } from "./SearchFormFields";
import { RecentSearches } from "./RecentSearches";
import { useSearchForm } from "../../contexts/SearchFormContext";
import { Container } from "@/shared/components";

// interface SearchFormProps {
//   focusDate?: "departure" | "return"; // Which date picker to auto-focus
// }

export const SearchForm = React.memo(() => {
  const { t } = useTranslation();
  const { handleSubmit, variant } = useSearchForm();

  // The actual form content
  const formContent = (
    <form
      onSubmit={handleSubmit}
      noValidate
      className="rounded-[20px] md:rounded-[24px] bg-white shadow-lg overflow-hidden"
    >
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center px-4 md:px-6 pt-4 md:pt-6 gap-2 md:gap-0">
        {/* Trip Type Toggle */}
        <TripTypeToggle />

        {/* Guide Link - ẩn trên mobile như trong Figma */}
        <p className="hidden md:block text-sm text-[#1E4FFF] font-medium cursor-pointer hover:underline">
          {t("home.form.guide")}
        </p>
      </div>
      {/* Divider - thay đổi margin cho mobile */}
      <hr className="my-4 mx-4 md:mx-6" />
      {/* Form Fields */}
      <SearchFormFields />
      {/* Recent Searches */}
      <RecentSearches />
    </form>
  );

  // Search page variant - just different positioning
  if (variant === "search-page") {
    return (
      <div className="w-full mb-6">
        <div className="w-full max-w-[1200px] mx-auto">{formContent}</div>
      </div>
    );
  }

  // Homepage variant - responsive positioning with consistent padding
  return (
    <div className="relative w-full md:absolute z-30 bottom-[10px] md:top-[225px] left-0 md:left-1/2 md:-translate-x-1/2 md:translate-y-1/2">
      {/* <div className="w-full max-w-[1440px] mx-auto px-6"> */}
      <Container>
        <div className="w-full mx-auto">{formContent}</div>
      </Container>
      {/* </div> */}
    </div>
  );
});
