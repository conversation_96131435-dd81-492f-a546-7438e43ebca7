import { useQuery, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import { getAllPlaces, searchPlaces } from "@/features/booking/api/places.api";
import type { Place } from "@/features/booking/api/places.types";

// Hook to get all places
export const usePlaces = () => {
  return useQuery({
    queryKey: queryKeys.places.lists(),
    queryFn: getAllPlaces,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get places on demand (only when explicitly triggered)
export const usePlacesOnDemand = () => {
  return useQuery({
    queryKey: queryKeys.places.lists(),
    queryFn: getAllPlaces,
    enabled: false, // Don't auto-fetch
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to search places
export const useSearchPlaces = (term: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.places.search(term),
    queryFn: () => searchPlaces(term),
    enabled: enabled && term.length > 0,
    staleTime: 1000 * 60 * 2, // 2 minutes for search results
  });
};

// Hook to get places by level
export const usePlacesByLevel = (level: number) => {
  return useQuery({
    queryKey: queryKeys.places.byLevel(level),
    queryFn: async () => {
      const allPlaces = await getAllPlaces();
      return allPlaces.filter((place: Place) => place.level === level);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to get places by parent ID
export const usePlacesByParentId = (parentId: number) => {
  return useQuery({
    queryKey: queryKeys.places.byParentId(parentId),
    queryFn: async () => {
      const allPlaces = await getAllPlaces();
      return allPlaces.filter((place: Place) => place.parentId === parentId);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook for prefetching places (useful for preloading data)
export const usePrefetchPlaces = () => {
  const queryClient = useQueryClient();

  return {
    prefetchAllPlaces: () => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.places.lists(),
        queryFn: getAllPlaces,
        staleTime: 1000 * 60 * 5,
      });
    },

    prefetchSearchPlaces: (term: string) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.places.search(term),
        queryFn: () => searchPlaces(term),
        staleTime: 1000 * 60 * 2,
      });
    },
  };
};

// Hook for invalidating places cache
export const useInvalidatePlaces = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAllPlaces: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.places.all });
    },

    invalidateSearchPlaces: () => {
      queryClient.invalidateQueries({
        queryKey: [...queryKeys.places.all, "search"],
      });
    },
  };
};
