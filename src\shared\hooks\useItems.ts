import { useQuery } from "@tanstack/react-query";
import { getAllItems, getItemById } from "@/api/items";
import type { ItemsResponse } from "@/api/items/items.types";

// Get order by ID query
export const useGetItemById = (itemId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ["item", itemId],
    queryFn: () => getItemById(itemId),
    enabled: enabled && !!itemId,
  });
};

// Get all orders query
export const useGetAllItems = (params?: {
  page?: number;
  limit?: number;
  isHighlighted?: boolean;
}) => {
  return useQuery<ItemsResponse>({
    queryKey: ["items", params],
    queryFn: () => getAllItems(params),
  });
};
