import type { OrderTicket } from "@/api/orders/orders.types";
import axiosInstance from "@/shared/api/axiosInstance";

interface LookupTicketsParams {
  phone_number: string;
  ticket_number: string;
}

// The actual API returns a single ticket object with nested order, busSchedule, busDropoff, etc.
export interface LookupTicketResponse extends Omit<OrderTicket, "order_id"> {
  order: {
    id: string;
    customer_name: string;
    customer_phone: string;
    customer_email: string | null;
    total_amount: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  busSchedule: OrderTicket["busSchedule"];
  busDropoff: OrderTicket["busDropoff"];
  departure_place?: any;
}

export const lookupTickets = async (
  params: LookupTicketsParams
): Promise<LookupTicketResponse> => {
  const { data } = await axiosInstance.post(
    "/api/v1/orders/tickets/lookup",
    params
  );
  return data;
};
