import React, { memo, useCallback, useMemo } from "react";
import { CardCarousel, type CardItem } from "@/shared/components/CardCarousel";
import { PromotionCard } from "./PromotionCard";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { formatTimeAgo } from "@/lib/utils";
import { useDiscounts } from "@/shared/hooks/useDiscounts";
import { useNavigate } from "@tanstack/react-router";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

export const PromotionSection: React.FC = memo(() => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data: discounts, isLoading } = useDiscounts();

  // Mock data as fallback when API fails or returns empty
  const MOCK_PROMOTIONS: CardItem[] = useMemo(
    () => [
      {
        id: "1",
        image: "/mock-images/image.png",
        title:
          "TPBank | Nghỉ lễ vi vu, TPBank giảm giá ngay 30% khi đặt Vé xe khách",
        timeAgo: formatTimeAgo(2, "hours", t),
      },
      {
        id: "2",
        image: "/mock-images/image-3.png",
        title: "Mua Vé xe rẻ - Vui vẻ vi vu cùng My Viettel",
        timeAgo: formatTimeAgo(2, "hours", t),
      },
      {
        id: "3",
        image: "/mock-images/image-4.png",
        title:
          "Combo hè di chuyển: Giảm tới 200.000 đồng vé tàu - xe trên Agribank Plus",
        timeAgo: formatTimeAgo(3, "days", t),
      },
      {
        id: "4",
        image: "/mock-images/image-5.png",
        title:
          "Nhận hoàn tiền lên đến 50% khi đặt vé xe khách trên ứng dụng Agribank E-Mobile",
        timeAgo: "21/05/2025",
      },
      {
        id: "5",
        image: "/mock-images/image.png",
        title: "Flash Sale cuối tuần - Giảm ngay 40% cho tất cả tuyến đường",
        timeAgo: formatTimeAgo(1, "weeks", t),
      },
      {
        id: "6",
        image: "/mock-images/image-3.png",
        title: "Ưu đãi đặc biệt dành cho khách hàng VIP - Miễn phí phí giao vé",
        timeAgo: formatTimeAgo(5, "days", t),
      },
    ],
    [t]
  );
  // Transform Strapi discounts to CardItem format
  const transformedPromotions: CardItem[] = useMemo(() => {
    if (!discounts || discounts.length === 0) {
      return MOCK_PROMOTIONS;
    }

    return discounts.map((discount) => ({
      id: discount.id.toString(),
      image: getStrapiImageUrl(
        discount.image_background,
        "/mock-images/image-2.png"
      ),
      title: discount.title,
      timeAgo: new Date(discount.published_at).toLocaleDateString("vi-VN"),
    }));
  }, [discounts, MOCK_PROMOTIONS]);
  const handlePromotionClick = useCallback(
    (promotionId: string) => {
      navigate({ to: "/promotion/$id", params: { id: promotionId } });
    },
    [navigate]
  );

  const renderPromotionCard = useCallback(
    (item: CardItem) => {
      return (
        <PromotionCard
          id={item.id}
          image={item.image}
          title={item.title}
          timeAgo={item.timeAgo}
          onClick={() => handlePromotionClick(item.id)}
        />
      );
    },
    [handlePromotionClick]
  );

  return (
    <CardCarousel
      className="mt-10"
      title={t("home.promotions")}
      items={transformedPromotions}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 4,
      }}
      renderCard={renderPromotionCard}
      isLoading={isLoading}
    />
  );
});

PromotionSection.displayName = "PromotionSection";
