import { Store } from "@tanstack/react-store";

export interface MerchantData {
  qrData?: any; // <PERSON><PERSON> thể thay bằng type cụ thể nếu có
  [key: string]: any;
}

export interface MerchantsStoreState {
  merchantData: MerchantData | null;
}

export const merchantsStore = new Store<MerchantsStoreState>({
  merchantData: null,
});

export const merchantsActions = {
  setMerchantData: (data: MerchantData) => {
    merchantsStore.setState((state) => ({ ...state, merchantData: data }));
  },
  clearMerchantData: () => {
    merchantsStore.setState((state) => ({ ...state, merchantData: null }));
  },
};
