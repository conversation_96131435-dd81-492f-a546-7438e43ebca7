import React, { forwardRef } from "react";
import { CheckIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

export interface CheckboxProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  label?: string | React.ReactNode;
  error?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
}

export const Checkbox = forwardRef<HTMLButtonElement, CheckboxProps>(
  (
    { checked = false, onChange, label, error, disabled, className, id },
    ref
  ) => {
    const handleClick = () => {
      if (!disabled) {
        onChange?.(!checked);
      }
    };

    return (
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <button
            ref={ref}
            id={id}
            type="button"
            disabled={disabled}
            className={cn(
              "w-5 h-5 rounded border-2 flex items-center justify-center transition-colors",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              checked
                ? "bg-blue-600 border-blue-600"
                : "bg-white border-gray-300 hover:border-gray-400",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
            onClick={handleClick}
            aria-checked={checked}
            role="checkbox"
          >
            {checked && <CheckIcon className="w-3 h-3 text-white" />}
          </button>
          {label && (
            <label
              htmlFor={id}
              className={cn(
                "text-sm text-gray-800 cursor-pointer",
                disabled && "cursor-not-allowed opacity-50"
              )}
              onClick={handleClick}
            >
              {label}
            </label>
          )}
        </div>
        {error && <p className="text-red-500 text-sm">{error}</p>}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";
