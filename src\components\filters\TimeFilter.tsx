import React from "react";
import { Checkbox } from "@/shared/components";
import type { TimeSlot } from "@/shared/types/bus";

interface TimeFilterProps {
  selectedTimes: TimeSlot[];
  onChange: (times: TimeSlot[]) => void;
}

const timeSlotOptions = [
  {
    value: "early_morning" as TimeSlot,
    label: "Sáng sớm 00:00 - 06:00",
    count: 0,
  },
  {
    value: "morning" as TimeSlot,
    label: "Buổi sáng 06:00 - 12:00",
    count: 0,
  },
  {
    value: "afternoon" as TimeSlot,
    label: "Buổi chiều 12:00 - 18:00",
    count: 0,
  },
  {
    value: "evening" as TimeSlot,
    label: "Buổi tối 18:00 - 24:00",
    count: 0,
  },
];

const TimeFilter: React.FC<TimeFilterProps> = ({ selectedTimes, onChange }) => {
  const handleTimeChange = (timeSlot: TimeSlot, checked: boolean) => {
    if (checked) {
      onChange([...selectedTimes, timeSlot]);
    } else {
      onChange(selectedTimes.filter((time) => time !== timeSlot));
    }
  };

  return (
    <div className="space-y-1">
      {timeSlotOptions.map((option) => (
        <div key={option.value} className="px-4 py-2 hover:bg-transparent">
          <Checkbox
            checked={selectedTimes.includes(option.value)}
            onChange={(checked) => handleTimeChange(option.value, checked)}
            label={`${option.label}`}
          />
        </div>
      ))}
    </div>
  );
};

export default TimeFilter;
