import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
} from "react";
import { ArrowLeftIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { Dialog, DialogContent } from "@/shared/components/dialog";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { cn } from "@/lib/utils";

interface Place {
  id: number;
  name: string;
  code: string;
}

interface LocationModalProps {
  isOpen: boolean;
  onClose: () => void;
  places: Place[];
  value?: string;
  onValueChange: (value: string) => void;
  title: string;
  placeholder?: string;
  excludedValue?: string;
  excludedLabel?: string;
}

export const LocationModal: React.FC<LocationModalProps> = ({
  isOpen,
  onClose,
  places,
  value = "",
  onValueChange,
  title,
  placeholder = "Chọn điểm đi",
  excludedValue,
  excludedLabel,
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const listRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const startX = useRef<number>(0);
  const startY = useRef<number>(0);
  const isDragging = useRef<boolean>(false);

  // Reset search when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm("");
      setIsSwipeActive(false);
      setSwipeOffset(0);
      isDragging.current = false;
    }
  }, [isOpen]);

  // Touch event handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX;
    startY.current = e.touches[0].clientY;
    isDragging.current = false;
    setIsSwipeActive(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isSwipeActive) return;

    const currentX = e.touches[0].clientX;
    const currentY = e.touches[0].clientY;
    const deltaX = currentX - startX.current;
    const deltaY = currentY - startY.current;

    // Only handle horizontal right swipes
    if (
      deltaX > 0 &&
      Math.abs(deltaX) > Math.abs(deltaY) &&
      Math.abs(deltaX) > 10
    ) {
      isDragging.current = true;
      setSwipeOffset(Math.min(deltaX, 300));
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;
    const deltaX = endX - startX.current;

    setIsSwipeActive(false);
    setSwipeOffset(0);

    // Close modal if swipe distance is significant
    if (isDragging.current && deltaX > 100) {
      onClose();
    }

    isDragging.current = false;
  };

  // Scroll to selected item when modal opens
  useEffect(() => {
    if (isOpen && value && listRef.current) {
      setTimeout(() => {
        const selectedElement = listRef.current?.querySelector(
          '[data-selected="true"]'
        );
        if (selectedElement) {
          selectedElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);
    }
  }, [isOpen, value]);

  const filteredPlaces = useMemo(() => {
    // If no search term, show all places
    if (!searchTerm.trim()) {
      return places;
    }

    // Filter places based on search term
    return places.filter(
      (place) =>
        place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (place.code &&
          place.code.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [places, searchTerm]);

  // Highlight search term in place name
  const highlightSearchTerm = useCallback(
    (text: string, searchTerm: string) => {
      if (!searchTerm.trim()) return text;

      const regex = new RegExp(`(${searchTerm})`, "gi");
      const parts = text.split(regex);

      return parts.map((part, index) =>
        regex.test(part) ? (
          <span key={index} className="bg-yellow-200 text-[#181818] font-bold">
            {part}
          </span>
        ) : (
          part
        )
      );
    },
    []
  );

  const handleLocationSelect = useCallback(
    (placeName: string) => {
      // Don't allow selecting excluded value
      if (excludedValue && placeName === excludedValue) {
        return;
      }

      onValueChange(placeName);
      onClose();
    },
    [onValueChange, onClose, excludedValue]
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    },
    []
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full h-full max-w-none max-h-none p-0 gap-0 bg-[#F8F8F8] rounded-none [&>button]:hidden"
        ref={modalRef}
        style={{
          transform: `translateX(${swipeOffset}px)`,
          opacity: isSwipeActive ? Math.max(0.5, 1 - swipeOffset / 300) : 1,
          transition: isSwipeActive
            ? "none"
            : "transform 0.2s ease-out, opacity 0.2s ease-out",
        }}
      >
        <div
          className="flex flex-col h-full"
          onTouchStart={(e) => {
            handleTouchStart(e);
          }}
          onTouchMove={(e) => {
            handleTouchMove(e);
          }}
          onTouchEnd={(e) => {
            handleTouchEnd(e);
          }}
        >
          {/* Header */}
          <div className="bg-white px-6 py-4 flex items-center gap-8">
            <button
              onClick={onClose}
              className="flex-shrink-0 w-6 h-6 text-[#7C7B7B] hover:text-[#181818] transition-colors"
              aria-label={t("common.previous")}
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </button>
            <h1 className="text-lg font-bold text-[#181818] font-mulish">
              {title}
            </h1>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto bg-[#F8F8F8] px-4 py-4">
            {/* Search Input */}
            <div className="mb-6">
              <div className="bg-white border border-[#FF7F37] rounded-lg px-4 py-4 relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder={placeholder}
                  className="w-full text-sm font-normal text-[#5C5C5C] bg-transparent outline-none font-mulish pr-8"
                  autoFocus
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm("")}
                    className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 flex items-center justify-center text-[#7C7B7B] hover:text-[#181818] transition-colors"
                    aria-label="Xóa tìm kiếm"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Provinces List */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              {/* Header - Show result count */}
              <div className="px-4 py-3 bg-white border-b border-[#F0F0F0]">
                <h3 className="text-base font-bold text-[#181818] font-mulish">
                  Tỉnh/Thành phố
                  {searchTerm && (
                    <span className="text-sm font-normal text-[#5C5C5C] ml-2">
                      ({filteredPlaces.length} kết quả)
                    </span>
                  )}
                  {!searchTerm && (
                    <span className="text-sm font-normal text-[#5C5C5C] ml-2">
                      ({places.length} địa điểm)
                    </span>
                  )}
                </h3>
              </div>

              {/* List */}
              <div className="max-h-[500px] overflow-y-auto" ref={listRef}>
                {places.length === 0 ? (
                  <div className="px-4 py-6 text-center text-[#5C5C5C] font-mulish">
                    Chưa có dữ liệu địa điểm
                  </div>
                ) : filteredPlaces.length === 0 ? (
                  <div className="px-4 py-6 text-center text-[#5C5C5C] font-mulish">
                    Không tìm thấy kết quả cho "{searchTerm}"
                    <div className="text-xs mt-2 text-gray-400">
                      Hãy thử từ khóa khác
                    </div>
                  </div>
                ) : (
                  filteredPlaces.map((place) => {
                    const isExcluded = !!(
                      excludedValue && place.name === excludedValue
                    );
                    return (
                      <button
                        key={place.id}
                        onClick={
                          isExcluded
                            ? undefined
                            : () => handleLocationSelect(place.name)
                        }
                        data-selected={value === place.name}
                        disabled={isExcluded}
                        className={cn(
                          "w-full px-4 py-3 text-left transition-colors border-b border-[#F0F0F0] last:border-b-0",
                          isExcluded
                            ? "cursor-not-allowed bg-gray-50 opacity-60"
                            : value === place.name
                              ? "bg-[#EEF4FF] hover:bg-[#EEF4FF]"
                              : "bg-white hover:bg-[#F8F8F8]"
                        )}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <span
                              className={cn(
                                "text-base font-mulish block",
                                isExcluded
                                  ? "font-normal text-gray-400"
                                  : value === place.name
                                    ? "font-bold text-[#2D5BFF]"
                                    : "font-normal text-[#181818]"
                              )}
                            >
                              {highlightSearchTerm(place.name, searchTerm)}
                            </span>
                            {isExcluded && excludedLabel && (
                              <span className="text-xs text-gray-400 mt-1 block">
                                {excludedLabel}
                              </span>
                            )}
                          </div>
                          {value === place.name && !isExcluded && (
                            <div className="w-4 h-4 rounded-full bg-[#2D5BFF] flex items-center justify-center">
                              <div className="w-2 h-2 rounded-full bg-white"></div>
                            </div>
                          )}
                        </div>
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
