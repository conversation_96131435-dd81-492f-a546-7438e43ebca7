import axiosInstance from "@/shared/api/axiosInstance";
import {
  GET_ALL_BUS_SCHEDULES_API_ENDPOINT,
  SEARCH_BUS_SCHEDULES_API_ENDPOINT,
  GET_BUS_SCHEDULE_BY_ID_API_ENDPOINT,
  CREATE_BUS_SCHEDULE_API_ENDPOINT,
  UPDATE_BUS_SCHEDULE_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";
import type {
  BusSchedulesResponse,
  BusScheduleResponse,
  CreateBusScheduleRequest,
  UpdateBusScheduleRequest,
  SearchBusSchedulesParams,
} from "./bus-schedules.types";

// GET /api/v1/bus-schedules - Get all bus schedules, optionally filtered by busId
const getAllBusSchedules = async (
  busId?: string
): Promise<BusSchedulesResponse> => {
  const response = await axiosInstance.get(GET_ALL_BUS_SCHEDULES_API_ENDPOINT, {
    params: busId ? { busId } : {},
  });
  return response.data;
};

// GET /api/v1/bus-schedules/search - Search bus schedules by departure and arrival places
const searchBusSchedules = async (
  params: SearchBusSchedulesParams
): Promise<BusSchedulesResponse> => {
  const response = await axiosInstance.get(SEARCH_BUS_SCHEDULES_API_ENDPOINT, {
    params,
  });
  return response.data;
};

// GET /api/v1/bus-schedules/{id} - Get a specific bus schedule
const getBusScheduleById = async (id: string): Promise<BusScheduleResponse> => {
  const response = await axiosInstance.get(
    `${GET_BUS_SCHEDULE_BY_ID_API_ENDPOINT}/${id}`
  );
  return response.data;
};

// POST /api/v1/bus-schedules - Create a new bus schedule
const createBusSchedule = async (
  data: CreateBusScheduleRequest
): Promise<BusScheduleResponse> => {
  const response = await axiosInstance.post(
    CREATE_BUS_SCHEDULE_API_ENDPOINT,
    data
  );
  return response.data;
};

// PATCH /api/v1/bus-schedules/{id} - Update a bus schedule
const updateBusSchedule = async (
  id: string,
  data: UpdateBusScheduleRequest
): Promise<BusScheduleResponse> => {
  const response = await axiosInstance.patch(
    `${UPDATE_BUS_SCHEDULE_API_ENDPOINT}/${id}`,
    data
  );
  return response.data;
};

export {
  getAllBusSchedules,
  searchBusSchedules,
  getBusScheduleById,
  createBusSchedule,
  updateBusSchedule,
};
