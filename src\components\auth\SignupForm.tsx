import React, { useState } from "react";
import { Button } from "@/shared/components/button";
import { Input } from "@/shared/components";
import { cn } from "@/lib/utils";
import { useSignup } from "@/shared/hooks/useAuth";
import { getTranslatedErrorMessage } from "@/shared/utils/errorMessageTranslator";
import { validateVietnamesePhoneNumber } from "@/shared/utils/phoneNumberUtils";

interface SignupFormProps {
  onOtpSent?: (phoneNumber: string, formData: FormData) => void;
  className?: string;
}

interface FormData {
  phoneNumber: string;
}

export const SignupForm: React.FC<SignupFormProps> = ({
  onOtpSent,
  className,
}) => {
  const [formData, setFormData] = useState<FormData>({
    phoneNumber: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { mutateAsync: signup, isPending: isLoading } = useSignup();

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    const phoneValidation = validateVietnamesePhoneNumber(formData.phoneNumber);
    if (!phoneValidation.isValid) {
      newErrors.phoneNumber = phoneValidation.errorMessage!;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async () => {
    if (!validateForm()) return;

    try {
      // Normalize phone number before sending to API
      const phoneValidation = validateVietnamesePhoneNumber(
        formData.phoneNumber
      );
      const normalizedPhone =
        phoneValidation.normalizedNumber || formData.phoneNumber;

      const response = await signup({ phone_number: normalizedPhone });

      // Check for errors in response data even if HTTP status is success
      const responseData = response as any;
      if (
        responseData?.error_type === "RATE_LIMITED" ||
        responseData?.success === false
      ) {
        const errorMessage = getTranslatedErrorMessage({
          response: { data: responseData },
        });
        setErrors({ general: errorMessage });
        return;
      }

      onOtpSent?.(normalizedPhone, { phoneNumber: normalizedPhone });
    } catch (error: any) {
      console.error("Signup error:", error);
      setErrors({
        general: getTranslatedErrorMessage(error),
      });
    }
  };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="space-y-4">
        {/* Phone Number */}
        <Input
          type="tel"
          name="phoneNumber"
          placeholder="Số điện thoại "
          value={formData.phoneNumber}
          onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
          error={errors.phoneNumber}
        />

        {/* Error Message */}
        {errors.general && (
          <p className="text-red-500 text-sm text-center">{errors.general}</p>
        )}

        {/* Submit Button */}
        <Button
          onClick={handleSignup}
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-bold"
        >
          {isLoading ? "Đang xử lý..." : "Tiếp tục"}
        </Button>
      </div>
    </div>
  );
};
