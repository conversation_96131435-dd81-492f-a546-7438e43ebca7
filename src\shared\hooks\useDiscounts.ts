import { useQuery } from "@tanstack/react-query";
import { getDiscounts, getDiscountById } from "@/api/strapiApi";
import type {
  StrapiDiscountsResponse,
  StrapiApiError,
  StrapiDiscount,
} from "@/shared/types/strapi";

// Query keys for discounts
export const discountsKeys = {
  all: ["discounts"] as const,
  list: () => [...discountsKeys.all, "list"] as const,
  detail: (id: string) => [...discountsKeys.all, "detail", id] as const,
};

/**
 * Hook to fetch discounts from Strapi
 */
export const useDiscounts = () => {
  return useQuery<StrapiDiscountsResponse, StrapiApiError>({
    queryKey: discountsKeys.list(),
    queryFn: () => getDiscounts(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook to fetch a single discount by ID from Strapi
 * @param id - The discount ID to fetch
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useDiscountById = (id: string, enabled: boolean = true) => {
  return useQuery<StrapiDiscount, StrapiApiError>({
    queryKey: discountsKeys.detail(id),
    queryFn: () => getDiscountById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    enabled: enabled && !!id,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
