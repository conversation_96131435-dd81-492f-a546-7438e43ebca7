import React from "react";
import { cn } from "@/lib/utils";
import { Container } from "./Container";

interface FullWidthContainerProps {
  children: React.ReactNode;
  backgroundClassName?: string;
  containerClassName?: string;
  containerSize?: "sm" | "md" | "lg" | "full";
  noPadding?: boolean;
}

export const FullWidthContainer: React.FC<FullWidthContainerProps> = ({
  children,
  backgroundClassName = "bg-gray-100",
  containerClassName,
  containerSize = "lg",
  noPadding = false,
}) => {
  return (
    <div className={cn("w-full", backgroundClassName)}>
      <Container
        className={containerClassName}
        size={containerSize}
        noPadding={noPadding}
      >
        {children}
      </Container>
    </div>
  );
};
