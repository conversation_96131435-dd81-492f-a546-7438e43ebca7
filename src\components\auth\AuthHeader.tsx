import React from "react";

interface AuthHeaderProps {
  activeTab: "signin" | "signup";
}

export const AuthHeader: React.FC<AuthHeaderProps> = ({ activeTab }) => {
  return (
    <div className="text-center mb-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        {activeTab === "signin" ? "Đăng nhập " : "Đăng ký "}
      </h2>
      <p className="text-gray-600 text-base leading-relaxed">
        Đ<PERSON> nhận ưu đãi độc quyền và quản lý vé dễ dàng!
      </p>
    </div>
  );
};
