import { useGetAllOrders } from "@/shared/hooks/useOrders";
import { TicketsTable } from "../components/TicketsTable";
import React from "react";
import { useAuthState } from "@/shared/hooks/useAuth";

const MyTicketsPanel: React.FC = () => {
  const { profile } = useAuthState();

  const { data: orders = [], isLoading } = useGetAllOrders({
    customer_phone: profile?.phone_number,
  });
  return (
    <div className="bg-white rounded-xl p-2 sm:p-4 md:p-6 w-full max-w-full sm:max-w-2xl md:max-w-5xl mx-auto">
      <h1 className="text-[16px] text-center sm:text-start font-bold text-gray-900 mb-4 md:mb-8 border-b border-gray-200 pb-2">
        Theo dõi và quản lý quá trình lịch sử mua vé của bạn
      </h1>
      <div className="overflow-x-auto">
        <TicketsTable orders={orders} isLoading={isLoading} />
      </div>
    </div>
  );
};
export { MyTicketsPanel };
