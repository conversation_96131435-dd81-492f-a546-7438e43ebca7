import React from "react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { useLanguage, useTranslation } from "@/shared/hooks/useTranslation";

interface LanguageSwitcherProps {
  className?: string;
  variant?: "full" | "compact"; // New prop to control appearance
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className = "",
  variant = "full",
}) => {
  const { t } = useTranslation();
  const { currentLanguage, availableLanguages, changeLanguage } = useLanguage();
  const [isOpen, setIsOpen] = React.useState(false);

  // Compact variant for header - just shows flag and text without dropdown
  if (variant === "compact") {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        {/* Vietnam Flag - based on Figma design */}
        <div className="w-8 h-6 bg-red-600 relative rounded-sm overflow-hidden">
          <div className="absolute inset-0 bg-red-600"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              className="text-yellow-400"
            >
              <polygon
                points="7,2 8.5,5.5 12,5.5 9.25,7.5 10.75,11 7,9 3.25,11 4.75,7.5 2,5.5 5.5,5.5"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
        <span className="text-sm font-normal text-[#181818]">VN</span>
      </div>
    );
  }

  // Full variant with dropdown
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        aria-label={t("language.switch")}
      >
        {/* Dynamic Flag based on current language */}
        {currentLanguage === "vi" && (
          <div className="w-5 h-4 bg-red-600 relative rounded-sm overflow-hidden">
            <div className="absolute inset-0 bg-red-600"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <svg
                width="12"
                height="8"
                viewBox="0 0 12 8"
                className="text-yellow-400"
              >
                <polygon
                  points="6,1 7.5,4.5 11,4.5 8.25,6.5 9.75,10 6,8 2.25,10 3.75,6.5 1,4.5 4.5,4.5"
                  fill="currentColor"
                />
              </svg>
            </div>
          </div>
        )}
        {currentLanguage === "en" && (
          <div className="w-5 h-4 bg-blue-600 relative rounded-sm overflow-hidden">
            <div className="absolute inset-0 bg-blue-600"></div>
            <div className="absolute top-0 left-0 w-full h-1/3 bg-red-600"></div>
            <div className="absolute bottom-0 left-0 w-full h-1/3 bg-red-600"></div>
            <div className="absolute top-1/3 left-0 w-full h-1/3 bg-white"></div>
          </div>
        )}
        <span className="text-sm font-medium">
          {currentLanguage === "vi" ? "VN" : "EN"}
        </span>
        <ChevronDownIcon
          className={`w-4 h-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute right-0 z-20 mt-1 bg-white border border-gray-300 rounded-md shadow-lg min-w-[140px]">
            {availableLanguages.map((language) => (
              <button
                key={language.code}
                onClick={async () => {
                  await changeLanguage(language.code);
                  setIsOpen(false);
                }}
                className={`w-full px-4 py-2 text-sm text-left hover:bg-gray-50 first:rounded-t-md last:rounded-b-md transition-colors flex items-center gap-2 ${
                  currentLanguage === language.code
                    ? "bg-blue-50 text-blue-700 font-medium"
                    : "text-gray-700"
                }`}
              >
                {language.code === "vi" && (
                  <div className="w-4 h-3 bg-red-600 relative rounded-sm overflow-hidden">
                    <div className="absolute inset-0 bg-red-600"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <svg
                        width="8"
                        height="6"
                        viewBox="0 0 8 6"
                        className="text-yellow-400"
                      >
                        <polygon
                          points="4,0.5 5,3 6.5,3 5.5,4 6,5.5 4,4.5 2,5.5 2.5,4 1.5,3 3,3"
                          fill="currentColor"
                        />
                      </svg>
                    </div>
                  </div>
                )}
                {language.code === "en" && (
                  <div className="w-4 h-3 bg-blue-600 relative rounded-sm overflow-hidden">
                    <div className="absolute inset-0 bg-blue-600"></div>
                    <div className="absolute top-0 left-0 w-full h-1/3 bg-red-600"></div>
                    <div className="absolute bottom-0 left-0 w-full h-1/3 bg-red-600"></div>
                    <div className="absolute top-1/3 left-0 w-full h-1/3 bg-white"></div>
                  </div>
                )}
                {language.name}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};
