# Auth Components và Pages

Các component và page auth đã được tạo theo Figma design và tích hợp với API auth hiện có.

## Components

### 1. AuthLayout

- Layout wrapper cho tất cả auth pages
- <PERSON><PERSON>ồ<PERSON> header, footer và background theo Figma design
- Responsive design cho desktop và mobile

### 2. SignupForm

- Form đăng ký với validation
- Tab switching giữa đăng ký và đăng nhập
- Tích hợp với `useSignup` hook
- Fields: <PERSON><PERSON>ê<PERSON>, <PERSON>ail, SĐT, <PERSON><PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON> nhận mật khẩu
- Checkbox đồng ý điều khoản

### 3. OtpVerification

- Component xác thực OTP 6 chữ số
- Auto-focus và paste support
- Tích hợp với `useVerifyOtp` hook
- Auto-verify khi nhập đủ 6 số

### 4. SignupSuccess

- M<PERSON><PERSON> hình thông bá<PERSON> đăng ký thành công
- Success icon và message
- But<PERSON> tiếp tục

### 5. SigninForm

- Form đăng nhập với validation
- Tab switching với signup
- Tích hợp với `useSignin` hook
- Fields: SĐT, Mật khẩu
- Remember me checkbox
- Forgot password link

## Pages/Routes

### /register

- **Thay thế route cũ** `/register` bằng UI mới
- Multi-step signup flow:
  1. **Form** - Nhập thông tin đăng ký
  2. **OTP** - Xác thực OTP
  3. **Success** - Hoàn thành đăng ký

### /login

- **Thay thế route cũ** `/login` bằng UI mới
- Single page signin form
- Tab để chuyển sang register

## API Integration

Tất cả component đều tích hợp với auth API hooks:

- `useSignup` - Gửi OTP
- `useVerifyOtp` - Xác thực OTP
- `useSetPassword` - Thiết lập mật khẩu và profile
- `useSignin` - Đăng nhập

## Flow hoàn chỉnh

1. **Đăng ký** (`/register`):
   - Nhập thông tin → Gửi OTP → Xác thực OTP → Hoàn thành
2. **Đăng nhập** (`/login`):
   - Nhập SĐT + mật khẩu → Đăng nhập thành công
   - Tab để chuyển sang `/register`

## Testing

Để test các màn hình:

1. Chạy app và truy cập `/register` hoặc `/login`
2. Hoặc từ AuthExample component, click vào links "Màn hình Auth mới"

## ⚠️ Thay đổi Routes

- **Đã xóa**: `/auth/signup` và `/auth/signin` (trùng lặp)
- **Cập nhật**: Sử dụng `/register` và `/login` hiện có
- **Lý do**: Tránh trùng lặp chức năng, sử dụng lại routes đã có

## Figma Design Reference

- Desktop Signup: node-id=815-11989
- Mobile Signup: node-id=818-21771
- Desktop OTP: node-id=818-15417
- Mobile OTP: node-id=818-22169
- Desktop Success: node-id=818-15898
- Mobile Success: node-id=818-22456
- Desktop Signin: node-id=818-16096
- Mobile Signin: node-id=818-22565
