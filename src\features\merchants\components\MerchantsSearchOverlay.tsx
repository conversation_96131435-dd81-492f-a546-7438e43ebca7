import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import ItemSearch from "@/features/merchants/components/ItemSearch";
import type { FoodItem } from "@/features/merchants/api/food-court.types";
import React, { useCallback } from "react";

interface SearchItem extends FoodItem {
  merchantId: string;
  merchantName: string;
  merchantLogo: string;
  categoryId: string;
  categoryName: string;
}

interface MerchantsSearchOverlayProps {
  searchValue: string;
  setSearchValue: (v: string) => void;
  filteredItems: SearchItem[];
  onSelectItem: (item: SearchItem) => void;
  onClose: () => void;
}

export const MerchantsSearchOverlay: React.FC<MerchantsSearchOverlayProps> =
  React.memo(
    ({ searchValue, setSearchValue, filteredItems, onSelectItem, onClose }) => {
      // Memo hóa callback để tránh render lại không cần thiết
      const handleSelectItem = useCallback(
        (item: SearchItem) => {
          onSelectItem(item);
        },
        [onSelectItem]
      );

      return (
        <div className="w-full z-50 h-16 bg-transparent px-4 pt-4 pb-2">
          <div className="relative">
            <input
              className="w-full py-2 pl-10 pr-4 bg-white rounded-lg border border-[#FF7F37] placeholder-gray-400 text-base focus:outline-none focus:ring-[#FF7F37] focus:border-[#FF7F37]"
              placeholder="Tìm theo tên món"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              autoFocus
              onBlur={onClose}
            />
            <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          </div>
          {filteredItems.length > 0 && (
            <div
              className="fixed z-50 max-h-[calc(100vh-30rem)] overflow-y-auto top-16 left-4 right-4 bg-white rounded-lg shadow-lg p-2"
              onMouseDown={(e) => e.preventDefault()}
            >
              {filteredItems.map((item, idx) => (
                <div key={item.id || idx}>
                  <ItemSearch
                    item={item}
                    onSelectItem={() => handleSelectItem(item)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
  );

MerchantsSearchOverlay.displayName = "MerchantsSearchOverlay";
