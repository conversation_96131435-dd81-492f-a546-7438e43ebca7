import type { BusSchedule } from "@/features/booking/api/bus-schedules";
import type { Bus } from "@/shared/types/bus";
import { formatDuration, getOptimalImageUrl, formatTime } from "@/lib/utils";

// Helper function to process bus schedule data
export const processBusSchedules = ({
  schedules,
  date,
  fromPlaceName,
  toPlaceName,
  isReturnTrip = false,
}: {
  schedules: BusSchedule[];
  date: string;
  fromPlaceName: string;
  toPlaceName: string;
  isReturnTrip?: boolean;
}): Bus[] => {
  return schedules.map((schedule: BusSchedule) => {
    const dropoff = schedule.dropoffs && schedule.dropoffs[0];

    let departureDateTime: Date | string = schedule.departure_time;
    let arrivalDateTime: Date | string = dropoff ? dropoff.arrival_time : "N/A";

    if (date) {
      const datePart = date;
      if (schedule.departure_time) {
        departureDateTime = new Date(`${datePart}T${schedule.departure_time}`);
      }
      if (dropoff && dropoff.arrival_time) {
        arrivalDateTime = new Date(`${datePart}T${dropoff.arrival_time}`);
      }
    }

    // Determine bus type based on description or model
    let busType: "standard" | "limousine" | "sleeper" = "standard";
    const description = schedule.bus.description?.toLowerCase() || "";
    const model = schedule.bus.model?.toLowerCase() || "";

    if (description.includes("limousine") || model.includes("limousine")) {
      busType = "limousine";
    } else if (description.includes("giường") || model.includes("sleeper")) {
      busType = "sleeper";
    }

    return {
      id: isReturnTrip ? `return-${schedule.id}` : schedule.id,
      originalScheduleId: schedule.id,
      name: schedule.bus.fleet.name,
      image: getOptimalImageUrl(schedule.bus.images, "medium"),
      busNumber: schedule.bus.license_plate || "N/A",
      busDescription: `${schedule.bus.brand} ${schedule.bus.model} (${schedule.bus.seats} seats)`,
      departureDateTime,
      arrivalDateTime,
      price: dropoff ? parseFloat(dropoff.ticket_price) : 0,
      rawDepartureTime: schedule.departure_time,
      rawArrivalTime: dropoff ? dropoff.arrival_time : "N/A",
      dropoffId: dropoff ? dropoff.id : undefined,
      departureTime: formatTime(schedule.departure_time),
      arrivalTime: dropoff ? formatTime(dropoff.arrival_time) : "N/A",
      duration:
        dropoff && dropoff.duration_minutes
          ? formatDuration(dropoff.duration_minutes)
          : "Không xác định",
      departureLocation: isReturnTrip ? toPlaceName : fromPlaceName,
      arrivalLocation: isReturnTrip ? fromPlaceName : toPlaceName,
      availableSeats: schedule.bus.seats || 20,
      remainingTickets: dropoff?.remainingTickets || schedule.bus.seats || 20,
      amenities: ["Lịch trình", "Chính sách"],
      type: busType,
      dropoffs: schedule.dropoffs,
      fleetPolicy: schedule.bus.fleet.policy,
      bus: {
        ...schedule.bus,
        bus_type: busType,
      },
    };
  });
};
