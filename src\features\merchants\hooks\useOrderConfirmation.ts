import { useState, useCallback, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useCreateOrder } from "@/features/merchants/hooks/useCreateOrder";
import {
  DeliveryTypeEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";
import type {
  CustomerInfo,
  DeliveryOption,
} from "@/shared/types/order-confirmation.types";
import type { CreateOrderRequest } from "@/api/merchants/orders.types";
import { Route as OrderConfirmationRoute } from "@/routes/merchants/order-confirmation.$id";
import {
  simpleCartActions,
  simpleCartSelectors,
} from "@/stores/simpleCartStore";
// import { prepareOrderData, validateCustomerInfo } from "@/shared/utils/order.utils";

interface UseOrderConfirmationProps {
  restaurantId: string;
}

export const useOrderConfirmation = ({
  restaurantId,
}: UseOrderConfirmationProps) => {
  const navigate = useNavigate();
  const createOrderMutation = useCreateOrder();
  const { qr } = OrderConfirmationRoute.useSearch
    ? OrderConfirmationRoute.useSearch()
    : { qr: undefined };

  const [isLoading, setIsLoading] = useState(true);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: "",
    phone: "",
  });
  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption>(
    DeliveryTypeEnum.SELF_PICKUP
  );
  const [note, setNote] = useState("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  // Check cart data on component mount
  useEffect(() => {
    // Don't redirect if we're placing an order
    if (createOrderMutation.isPending) return;

    // Skip validation for multiple restaurant flow (empty restaurantId)
    if (restaurantId === "") {
      setIsLoading(false);
      return;
    }

    const restaurantCart = simpleCartSelectors.getAllItems();
    if (!restaurantCart) {
      // No cart data for this restaurant, redirect back
      navigate({ to: `/merchants`, search: { qr: qr ?? "" } });
      return;
    }

    setIsLoading(false);
  }, [restaurantId, navigate, createOrderMutation.isPending, qr]);

  const handleBackToMenu = useCallback(() => {
    navigate({ to: `/merchants`, search: { qr: qr ?? "" } });
  }, [navigate, qr]);

  const handleAddMoreItems = useCallback(() => {
    navigate({ to: `/merchants-detail/${restaurantId}` });
  }, [restaurantId, navigate]);

  // Handle quantity changes using store
  const handleQuantityChange = useCallback(
    (itemId: string, newQuantity: number) => {
      simpleCartActions.updateItemQuantity(itemId, newQuantity);
      // If no items left after update, redirect back to menu
      const restaurantCart = simpleCartSelectors.getAllItems();
      if (newQuantity === 0 && restaurantCart.length === 1) {
        navigate({ to: `/merchants-detail/${restaurantId}` });
      }
    },
    [navigate, restaurantId]
  );

  // Prepare order data for API
  const prepareOrderData = useCallback((): CreateOrderRequest => {
    const restaurantCart = simpleCartSelectors.getAllItems();
    if (!restaurantCart) {
      throw new Error("No cart data available");
    }

    return {
      food_court_table_id: "99bb7773-8b1e-4840-9740-e75577093a5e", // TODO: Get actual table ID
      delivery_type: deliveryOption,
      special_instructions: note || undefined,
      payment_method: PaymentTypeEnum.CARD,
      items: restaurantCart.map((item) => ({
        food_item_id: item.id,
        quantity: item.quantity,
        special_instructions: undefined, // TODO: Add per-item instructions if needed
      })),
    };
  }, [restaurantId, deliveryOption, note]);

  const handlePlaceOrder = useCallback(async () => {
    const restaurantCart = simpleCartSelectors.getAllItems();
    if (!restaurantCart || createOrderMutation.isPending) return;

    try {
      const orderData = prepareOrderData();

      await createOrderMutation.mutateAsync(orderData);

      // Show success popup
      setShowSuccessPopup(true);

      // Clear cart after successful order
      simpleCartActions.clearCart();

      // Wait 3 seconds then redirect
      setTimeout(() => {
        setShowSuccessPopup(false);
        navigate({ to: "/merchants" });
      }, 3000);
    } catch (error) {
      console.error("Failed to place order:", error);
      // Error handling is done by the mutation's onError callback
      // The UI will show the error through createOrderMutation.isError
    }
  }, [restaurantId, createOrderMutation, prepareOrderData, navigate]);

  const handleCustomerInfoChange = useCallback((info: CustomerInfo) => {
    setCustomerInfo(info);
  }, []);

  const handleDeliveryOptionChange = useCallback((option: DeliveryOption) => {
    setDeliveryOption(option);
  }, []);

  const handleNoteChange = useCallback((newNote: string) => {
    setNote(newNote);
  }, []);

  return {
    // State
    isLoading,
    isPlacingOrder: createOrderMutation.isPending,
    customerInfo,
    deliveryOption,
    note,
    createOrderMutation,
    showSuccessPopup,

    // Handlers
    handleBackToMenu,
    handleAddMoreItems,
    handleQuantityChange,
    handlePlaceOrder,
    handleCustomerInfoChange,
    handleDeliveryOptionChange,
    handleNoteChange,
  };
};
