import React from "react";
import { cn } from "@/lib/utils";

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg" | "full";
  noPadding?: boolean;
  as?: React.ElementType;
}

export const Container: React.FC<ContainerProps> = ({
  children,
  className,
  size = "lg",
  noPadding = false,
  as: Component = "div",
}) => {
  const sizeClasses = {
    sm: "max-w-2xl",
    md: "max-w-4xl",
    lg: "max-w-7xl",
    full: "max-w-full",
  };

  const paddingClasses = noPadding ? "" : "px-4 sm:px-20 lg:px-25";

  return (
    <Component
      className={cn(
        "mx-auto w-full",
        sizeClasses[size],
        paddingClasses,
        className
      )}
    >
      {children}
    </Component>
  );
};
