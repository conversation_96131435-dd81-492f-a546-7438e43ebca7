import { useState, useRef, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { CalendarIcon } from "@heroicons/react/24/outline";
import { Calendar } from "../calendar";
import { cn } from "@/lib/utils";
// import { Calendar } from "@/shared/components/calendar";
// import { Button } from "@/shared/components/button";
// import { cn } from "@/shared/utils/cn";

interface DatePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  containerClassName?: string;
  inputClassName?: string;
  disabled?: boolean;
  classNameIcon?: string;
}

export const DatePicker = ({
  value,
  onChange,
  placeholder = "Chọn ngày",
  className,
  containerClassName,
  inputClassName,
  classNameIcon,
  disabled = false,
}: DatePickerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const hiddenInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setIsFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateSelect = (date: Date | undefined) => {
    onChange?.(date);
    setIsOpen(false);
    setIsFocused(false);
  };

  const handleContainerClick = () => {
    if (!disabled) {
      const newOpenState = !isOpen;
      setIsOpen(newOpenState);
      setIsFocused(newOpenState);
      if (newOpenState) {
        hiddenInputRef.current?.focus();
      } else {
        hiddenInputRef.current?.blur();
      }
    }
  };

  const handleInputFocus = () => {
    if (!isOpen) {
      setIsFocused(true);
    }
  };

  const handleInputBlur = () => {
    if (!isOpen) {
      setIsFocused(false);
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative bg-[#F8F8F8] rounded-lg pr-4 flex items-center cursor-pointer transition-all",
        (isFocused || isOpen) &&
          "bg-[#f0f0f0] ring-2 ring-blue-500 ring-opacity-50",
        className,
        containerClassName
      )}
      onClick={handleContainerClick}
      style={{
        outline: "none !important",
        border: "none !important",
        boxShadow:
          isFocused || isOpen ? "0 0 0 2px rgba(59, 130, 246, 0.5)" : "none",
      }}
    >
      {/* Hidden input for accessibility */}
      <input
        ref={hiddenInputRef}
        type="text"
        readOnly
        value={value ? format(value, "dd/MM/yyyy", { locale: vi }) : ""}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            handleContainerClick();
          }
        }}
        style={{
          position: "absolute",
          opacity: 0,
          pointerEvents: "none",
          width: "1px",
          height: "1px",
          overflow: "hidden",
          outline: "none !important",
          border: "none !important",
          boxShadow: "none !important",
        }}
        disabled={disabled}
      />

      <div
        className={cn(
          "bg-[#EDEDED] flex items-center justify-center rounded-tl-lg rounded-bl-lg mr-3",
          classNameIcon
        )}
      >
        <CalendarIcon className="w-4 h-4" style={{ color: "#7C7B7B" }} />
      </div>
      <div className={cn("flex-1 flex items-center", inputClassName)}>
        <span className="text-[#5C5C5C] text-[14px] font-mulish font-normal leading-[18px]">
          {value ? format(value, "dd/MM/yyyy", { locale: vi }) : placeholder}
        </span>
      </div>
      {/* Calendar popup */}
      {isOpen && (
        <div className="absolute top-[calc(100%+4px)] left-0 z-50">
          <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
            <Calendar
              mode="single"
              selected={value}
              onSelect={handleDateSelect}
              initialFocus
              locale={vi}
            />
          </div>
        </div>
      )}
    </div>
  );
};
