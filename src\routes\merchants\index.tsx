import { useState, useMemo } from "react";
import { createFileRoute } from "@tanstack/react-router";
import { useMerchantsMenu } from "@/features/merchants/hooks/useMerchantsMenu";
import { LoadingSpinner } from "@/shared/components/LoadingSpinner";
import { ErrorMessage } from "@/shared/components/ErrorMessage";
import { MerchantsHeader } from "@/features/merchants/components/MerchantsHeader";
import { RestaurantInfo } from "@/features/merchants/components/RestaurantInfo";
import { MenuList } from "@/features/merchants/components/MenuList";
import { BottomCart } from "@/features/merchants/components/BottomCart";
import { MerchantsSearchOverlay } from "@/features/merchants/components/MerchantsSearchOverlay";
import { MenuListSkeleton } from "@/shared/components/skeleton";
import { PopularMerchants } from "@/features/merchants";
import { RestaurantInfoSkeleton } from "@/shared/components/skeleton";

// Search params schema
interface MerchantsSearch {
  qr?: string;
}

export const Route = createFileRoute("/merchants/")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>): MerchantsSearch => {
    return {
      qr: typeof search.qr === "string" ? search.qr : "QR-T001-XYZ222123",
    };
  },
});

function RouteComponent() {
  const { qr } = Route.useSearch();
  // Only run if qr exists
  const merchantsMenu = qr ? useMerchantsMenu({ qr }) : null;

  // Destructure safely
  const {
    merchants = [],
    isLoading = false,
    error = null,
    selectedMerchantId = null,
    setSelectedMerchantId = () => {},
    restaurant = null,
    activeCategory = "",
    setActiveCategory = () => {},
  } = merchantsMenu || {};

  // Search state
  const [showSearch, setShowSearch] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [scrollToItemId, setScrollToItemId] = useState<string | null>(null);

  // All menu items for search
  const allMenuItems = useMemo(() => {
    return merchants.flatMap((merchant: any) =>
      (merchant.food_items || []).map((item: any) => ({
        ...item,
        merchantId: merchant.id,
        merchantName: merchant.name,
        merchantLogo: merchant.logo_image,
        // categoryId: category.id,
        categoryName: "Đồ ăn",
      }))
    );
  }, [merchants]);
  const filteredItems = useMemo(() => {
    if (!searchValue.trim()) return [];
    return allMenuItems.filter((item) =>
      item.name.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [searchValue, allMenuItems]);

  if (!qr) {
    return (
      <ErrorMessage
        message={"Không tìm thấy thông tin QR code."}
        onRetry={() => window.location.reload()}
      />
    );
  }
  if (isLoading) return <LoadingSpinner />;
  if (error)
    return (
      <ErrorMessage
        message={"Không thể tải thông tin bàn từ QR code."}
        onRetry={() => window.location.reload()}
      />
    );

  return (
    <div className="min-h-screen bg-[url(/assets/merchants/home_merchant.jpg)] relative">
      {showSearch ? (
        <MerchantsSearchOverlay
          searchValue={searchValue}
          setSearchValue={setSearchValue}
          filteredItems={filteredItems}
          onSelectItem={(item) => {
            setSelectedMerchantId(item.merchantId);
            setShowSearch(false);
            setSearchValue("");
            setScrollToItemId(item.id);
          }}
          onClose={() => {
            setShowSearch(false);
            setSearchValue("");
          }}
        />
      ) : (
        <MerchantsHeader onSearch={() => setShowSearch(true)} />
      )}

      <div className="relative z-10">
        <div className="px-4 pb-5 bg-transparent">
          <PopularMerchants
            merchants={merchants as any}
            selectedMerchantId={selectedMerchantId}
            onSelectMerchant={(merchant) => setSelectedMerchantId(merchant.id)}
          />
        </div>
        <div className="bg-white min-h-screen rounded-t-[20px]">
          {isLoading ? (
            <div className="space-y-4 p-4">
              <RestaurantInfoSkeleton />
              <MenuListSkeleton />
            </div>
          ) : error ? (
            <div className="p-4">
              <ErrorMessage
                message="Không thể tải thông tin nhà hàng."
                onRetry={() => window.location.reload()}
              />
            </div>
          ) : restaurant ? (
            <>
              <RestaurantInfo restaurant={restaurant} />
              <MenuList
                restaurant={restaurant}
                activeCategory={activeCategory}
                onCategorySelect={setActiveCategory}
                scrollToItemId={scrollToItemId}
                onScrolledToItem={() => setScrollToItemId(null)}
              />
              <BottomCart qr={qr ?? ""} />
            </>
          ) : null}
        </div>
      </div>
    </div>
  );
}

export default RouteComponent;
