import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  use<PERSON><PERSON><PERSON>,
  useRouter,
} from "@tanstack/react-router";
import { TicketQRBox } from "@/components/ticket/TicketQRBox";
import { PriceSummary } from "@/features/booking/components";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import { VietQRPopup } from "@/components/merchants-order-confirmation/VietQRPopup";
import { PaymentVerificationStatus } from "@/components/payment/PaymentVerificationStatus";
import { useEffect } from "react";
import z from "zod";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

// Header + breadcrumb
function PaymentHeader() {
  const router = useRouter();

  const handleBackToSearch = () => {
    for (let i = 0; i < 3; i++) {
      router.history.back();
    }
  };

  return (
    <div className="w-full px-4 pt-8 pb-6 mx-auto max-w-7xl">
      <nav className="text-sm text-[#8A8A8A] mb-2 gap-2 items-center hidden lg:flex">
        <Link to="/">Trang chủ</Link>
        <ChevronRightIcon className="text-gray-400 size-4" />
        <button
          className="cursor-pointer"
          type="button"
          onClick={handleBackToSearch}
        >
          Tìm chuyến
        </button>
        <ChevronRightIcon className="text-gray-400 size-4" />
        <span>Chi tiết vé</span>
        <ChevronRightIcon className="text-gray-400 size-4" />
        <span className="text-[#181818] font-semibold">Thanh toán</span>
      </nav>
      <h1 className="text-3xl font-bold text-[#181818]">Thanh toán</h1>
    </div>
  );
}

// Block xác minh trạng thái thanh toán (card xanh)
function VerificationStatusCard() {
  return (
    <div className="h-full">
      <PaymentVerificationStatus variant="primary" className="shadow-md" />
    </div>
  );
}

// Block thông tin thanh toán + cảnh báo (cột giữa)
function PaymentInfoColumn({ totalPrice }: { totalPrice: number }) {
  return (
    <div className="flex flex-col h-full gap-6">
      {/* Card thông tin thanh toán */}
      <div className="flex-1 p-6 bg-white shadow-md rounded-2xl">
        <div className="font-bold text-[#181818] mb-4">
          Thông tin thanh toán
        </div>
        <div className="flex flex-col gap-3">
          <div className="flex items-center justify-between">
            <span className="text-[#5C5C5C]">Trạng thái</span>
            <span className="text-[#2D5BFF] font-semibold">Đang xác minh</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-[#5C5C5C]">Phương thức thanh toán</span>
            <span className="text-[#181818] font-semibold">
              Chuyển khoản Online
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-[#5C5C5C]">Tổng tiền</span>
            <span className="text-[#181818] font-bold">
              {totalPrice.toLocaleString()}đ
            </span>
          </div>
        </div>
      </div>

      {/* Cảnh báo hotline */}
      <div className="bg-[#FFF0D6] border border-[#FF7F37] rounded-xl p-4 flex items-start gap-3">
        {/* Icon cảnh báo */}
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM9 15V13H11V15H9ZM9 5V11H11V5H9Z"
            fill="#FF7F37"
          />
        </svg>

        {/* Text cảnh báo */}
        <p className="text-sm text-[#181818] leading-5">
          Nếu giao dịch chưa được xác minh trong vòng{" "}
          <span className="font-bold">5 phút</span> sau khi chuyển khoản, vui
          lòng liên hệ{" "}
          <span className="font-bold text-[#2D5BFF]">190088888</span> để được xử
          lý kịp thời.
        </p>
      </div>
    </div>
  );
}

export const Route = createFileRoute("/booking/payment-verification")({
  validateSearch: z.object({
    orderId: z.string(),
  }),
  component: PaymentVerificationPage,
});

function PaymentVerificationPage() {
  const navigate = useNavigate();
  const { getTotalPrice } = useSelectedTripsStore();
  const { orderId } = Route.useSearch();

  const numTickets = 2;
  const totalPrice = getTotalPrice() * numTickets;
  const bankInfo = {
    bank: "VIETINBANK",
    accountNumber: "**********",
    accountName: "CHUTAIKHOAN",
    qrImage: "/assets/qr-fake.png",
    content: "VE547488",
  };

  // Add auto-redirect effect
  useEffect(() => {
    const timer = setTimeout(() => {
      navigate({
        to: "/payment-success",
        search: { orderId },
      });
    }, 5000); // 5 seconds

    return () => clearTimeout(timer);
  }, [navigate, orderId]);

  const handleDownloadQR = () => {
    const link = document.createElement("a");
    link.href = bankInfo.qrImage;
    link.download = "vietqr.png";
    link.click();
  };

  return (
    <div className="min-h-screen bg-[#F4F6FB] flex flex-col">
      <PaymentHeader />
      <div className="flex flex-col w-full gap-8 px-4 py-6 mx-auto max-w-7xl lg:flex-row">
        {/* Cột trái: Stack dọc 3 block */}
        <div className="flex flex-col flex-1 gap-8">
          {/* Hàng 1: 2 block ngang */}
          <div className="flex flex-col gap-8 lg:flex-row">
            {/* Block xác minh thanh toán */}
            <div className="flex-1">
              <VerificationStatusCard />
            </div>
            {/* Block thông tin thanh toán + cảnh báo */}
            <div className="flex-1">
              <PaymentInfoColumn totalPrice={totalPrice} />
            </div>
          </div>

          {/* Block TicketQRBox */}
          <div className="hidden w-full lg:flex">
            <TicketQRBox
              qrImage={bankInfo.qrImage}
              bank={bankInfo.bank}
              accountNumber={bankInfo.accountNumber}
              accountName={bankInfo.accountName}
              amount={totalPrice}
              content={bankInfo.content}
              expiryText={"Mã sẽ hết hạn vào lúc 12:11 Thứ 3, 10/6"}
              onDownloadQR={handleDownloadQR}
            />
          </div>

          <div className="flex min-h-screen lg:hidden">
            <VietQRPopup
              show={true}
              onClose={() => {}}
              qrData={bankInfo}
              onDownloadQR={handleDownloadQR}
              totalPrice={totalPrice}
              bgColor="bg-[#FF7F37]"
            />
          </div>
        </div>

        {/* Cột phải: PriceSummary */}
        <div className="w-full lg:w-[360px] lg:sticky lg:top-8">
          <PriceSummary
            isSubmitting={false}
            numTickets={numTickets}
            disableMobileUI={true}
          />
        </div>
      </div>
    </div>
  );
}
