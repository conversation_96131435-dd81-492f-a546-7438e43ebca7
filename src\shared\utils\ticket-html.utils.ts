import QRCode from "qrcode";
import { saveAs } from "file-saver";
import html2canvas from "html2canvas";
import type { CreateOrderResponse, OrderTicket } from "@/api/orders";

interface TicketHTMLData {
  ticket: OrderTicket;
  orderData: CreateOrderResponse;
  isReturnTicket: boolean;
}

// Create a trip data representation from API ticket
export const createTripDataFromTicket = (ticket: OrderTicket) => {
  // Calculate duration in readable format
  const calculateDuration = (durationMinutes: number): string => {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    if (hours > 0 && minutes > 0) {
      return `${hours}h${minutes.toString().padStart(2, "0")}p`;
    } else if (hours > 0) {
      return `${hours}h00p`;
    } else {
      return `${minutes}p`;
    }
  };

  return {
    companyName: ticket.busSchedule.bus.fleet.name,
    route: `${ticket.busSchedule.departure_place.name} - ${ticket.busDropoff.arrival_place.name}`,
    departureLocation: ticket.busSchedule.departure_place.name,
    arrivalLocation: ticket.busDropoff.arrival_place.name,
    departureTime: ticket.departure_time.slice(0, 5), // Remove seconds
    arrivalTime: ticket.arrival_time.slice(0, 5), // Remove seconds
    duration: calculateDuration(ticket.busDropoff.duration_minutes),
    departureDate: ticket.departure_date,
    busNumber: ticket.busSchedule.bus.license_plate,
    busDescription: ticket.busSchedule.bus.description || "",
    price: parseFloat(ticket.price),
  };
};

// Function to create ticket HTML
const createTicketHTML = ({
  ticket,
  orderData,
  isReturnTicket,
}: TicketHTMLData): string => {
  const tripData = createTripDataFromTicket(ticket);
  const primaryColor = isReturnTicket ? "#FF7F37" : "#2D5BFF";
  const direction = isReturnTicket ? "Chiều về" : "Chiều đi";

  // Format departure date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const dayNames = [
        "Chủ Nhật",
        "Thứ 2",
        "Thứ 3",
        "Thứ 4",
        "Thứ 5",
        "Thứ 6",
        "Thứ 7",
      ];
      const dayName = dayNames[date.getDay()];
      const day = date.getDate().toString().padStart(2, "0");
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      return `${dayName}, ${day}/${month}/2025`;
    } catch {
      return dateString;
    }
  };

  // Format current time for footer
  const getCurrentTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, "0");
    const minutes = now.getMinutes().toString().padStart(2, "0");
    const seconds = now.getSeconds().toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const year = now.getFullYear();
    return `${hours}:${minutes}:${seconds} ${day}/${month}/${year}`;
  };

  return `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vé xe buýt - ${ticket.ticket_number}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8f8f8;
            color: #181818;
            width: 800px;
            margin: 0;
            padding: 0;
        }
        
        .ticket-container {
            width: 800px;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            margin: 0;
        }
        
        /* Header Section */
        .header-section {
            background: ${primaryColor};
            color: white;
            padding: 24px 32px;
            position: relative;
        }
        
        .direction-label {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .route-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
        }
        
        .date-label {
            font-size: 16px;
            margin-bottom: 8px;
            opacity: 0.9;
        }
        
        .company-label {
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Main Content Section */
        .content-section {
            background: white;
            padding: 32px;
            display: flex;
            gap: 32px;
        }
        
        /* QR Code Section */
        .qr-section {
            flex-shrink: 0;
        }
        
        .qr-container {
            width: 180px;
            height: 180px;
            border: 2px dashed #E5E7EB;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        
        .qr-label {
            text-align: center;
            margin-top: 12px;
            font-size: 14px;
            color: #6B7280;
        }
        
        .ticket-number {
            text-align: center;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 700;
            color: #181818;
        }
        
        .ticket-number-label {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 4px;
        }
        
        /* Trip Info Section */
        .trip-section {
            flex: 1;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .section-icon {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #181818;
            line-height: 28px;
        }
        
        .time-section {
            display: flex;
            align-items: center;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .time-info {
            text-align: center;
        }
        
        .time-value {
            font-size: 36px;
            font-weight: 800;
            color: #181818;
            line-height: 1;
        }
        
        .location-name {
            font-size: 16px;
            font-weight: 600;
            color: #181818;
            margin: 4px 0;
        }
        
        .time-label {
            font-size: 14px;
            color: #6B7280;
            margin-top: 4px;
            text-transform: uppercase;
        }
        
        .journey-line {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            height: 40px;
        }
        
        .journey-line-bg {
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #2D5BFF, #FF7F37);
            border-radius: 2px;
            position: relative;
        }
        
        .duration-badge {
            position: absolute;
            top: 0px;
            left: 50%;
            transform: translate(-50%, -100%);
            background: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .end-icon {
            width: 16px;
            height: 16px;
            color: #FF7F37;
        }
        
        .passenger-section {
            border-top: 1px solid #E5E7EB;
            padding-top: 24px;
        }
        
        .passenger-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .info-label {
            font-size: 14px;
            color: #6B7280;
            text-transform: uppercase;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #181818;
        }
        
        /* Footer Section */
        .footer-section {
            background: #374151;
            color: white;
            padding: 16px 32px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer-text {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="direction-label">${direction}</div>
            <div class="route-title">${tripData.route}</div>
            <div class="date-label">${formatDate(ticket.departure_date)}</div>
            <div class="company-label">${tripData.companyName}</div>
        </div>
        
        <!-- Main Content Section -->
        <div class="content-section">
            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="qr-container">
                    <img src="data:image/png;base64,{{QR_CODE_BASE64}}" width="160" height="160" alt="QR Code" />
                </div>
                <div class="qr-label">Quét mã để kiểm tra vé</div>
                <div class="ticket-number">
                    <div class="ticket-number-label">MÃ VÉ</div>
                    <div>${ticket.ticket_number}</div>
                </div>
            </div>
            
            <!-- Trip Information -->
            <div class="trip-section">
                <!-- Trip Info Header -->
                <div class="section-header">
                    <div class="section-icon">🚌</div>
                    <div class="section-title">Thông tin chuyến đi</div>
                </div>
                
                <!-- Time Section -->
                <div class="time-section">
                    <div class="time-info">
                        <div class="time-value">${tripData.departureTime}</div>
                        <div class="location-name">${tripData.departureLocation}</div>
                        <div class="time-label">KHỞI HÀNH</div>
                    </div>
                    
                    <div class="journey-line">
                        <div class="journey-line-bg">
                            <div class="duration-badge">${tripData.duration}</div>
                        </div>
                    </div>
                    
                    <div class="time-info">
                        <div class="time-value">${tripData.arrivalTime}</div>
                        <div class="location-name">${tripData.arrivalLocation}</div>
                        <div class="time-label">ĐẾN NƠI</div>
                    </div>
                </div>
                
                <!-- Passenger Info Section -->
                <div class="passenger-section">
                    <div class="section-header">
                        <div class="section-icon">👤</div>
                        <div class="section-title">Thông tin hành khách</div>
                    </div>
                    
                    <div class="passenger-info">
                        <div class="info-item">
                            <div class="info-label">HỌ VÀ TÊN</div>
                            <div class="info-value">${orderData.customer_name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">SỐ ĐIỆN THOẠI</div>
                            <div class="info-value">${orderData.customer_phone}</div>
                        </div>
                        ${
                          orderData.customer_email
                            ? `
                        <div class="info-item">
                            <div class="info-label">EMAIL</div>
                            <div class="info-value">${orderData.customer_email}</div>
                        </div>
                        `
                            : ""
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer Section -->
        <div class="footer-section">
            <div class="footer-text">
                Vui lòng giữ vé cho đến khi kết thúc chuyến đi • Được tạo: ${getCurrentTime()}
            </div>
        </div>
    </div>
</body>
</html>
`;
};

// Function to convert HTML to image and download
const convertHTMLToImageAndDownload = async (
  htmlContent: string,
  fileName: string
) => {
  try {
    // Create an invisible iframe for complete isolation
    const iframe = document.createElement("iframe");
    iframe.style.position = "fixed";
    iframe.style.left = "-9999px";
    iframe.style.top = "-9999px";
    iframe.style.width = "800px";
    iframe.style.height = "600px";
    iframe.style.border = "none";
    iframe.style.visibility = "hidden";
    iframe.style.opacity = "0";
    iframe.style.pointerEvents = "none";

    // Append iframe to body
    document.body.appendChild(iframe);

    // Wait for iframe to be ready
    await new Promise((resolve) => {
      iframe.onload = resolve;
      iframe.src = "about:blank";
    });

    // Get iframe document and write HTML
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      throw new Error("Cannot access iframe document");
    }

    iframeDoc.open();
    iframeDoc.write(htmlContent);
    iframeDoc.close();

    // Wait for content to load
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Get the ticket container from iframe
    const ticketContainer = iframeDoc.querySelector(".ticket-container");
    if (!ticketContainer) {
      throw new Error("Ticket container not found");
    }

    // Convert to canvas
    const canvas = await html2canvas(ticketContainer as HTMLElement, {
      width: 800,
      height: 600,
      backgroundColor: "#f8f8f8",
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
      scrollX: 0,
      scrollY: 0,
    });

    // Remove iframe immediately
    document.body.removeChild(iframe);

    // Convert canvas to blob and download
    return new Promise<void>((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            saveAs(blob, fileName);
            resolve();
          } else {
            reject(new Error("Failed to create image blob"));
          }
        },
        "image/png",
        0.95
      );
    });
  } catch (error) {
    console.error("Error converting HTML to image:", error);
    // Cleanup any leftover iframes
    const iframes = document.querySelectorAll('iframe[style*="-9999px"]');
    iframes.forEach((iframe) => {
      try {
        document.body.removeChild(iframe);
      } catch (e) {
        // Ignore cleanup errors
      }
    });
    throw error;
  }
};

// Function to download single ticket as image
export const downloadSingleTicketImage = async (
  ticket: OrderTicket,
  orderData: CreateOrderResponse,
  isReturnTicket: boolean
) => {
  try {
    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(ticket.ticket_number, {
      width: 160,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    // Extract base64 data from data URL
    const base64Data = qrCodeDataURL.split(",")[1];

    // Create HTML content
    let htmlContent = createTicketHTML({
      ticket,
      orderData,
      isReturnTicket,
    });

    // Replace QR code placeholder
    htmlContent = htmlContent.replace("{{QR_CODE_BASE64}}", base64Data);

    // Generate filename
    const tripData = createTripDataFromTicket(ticket);
    const direction = isReturnTicket ? "chieu-ve" : "chieu-di";
    const fileName = `ve-${direction}-${tripData.route.replace(/\s+/g, "-").toLowerCase()}-${ticket.ticket_number}.png`;

    // Convert HTML to image and download
    await convertHTMLToImageAndDownload(htmlContent, fileName);
  } catch (error) {
    console.error("Error generating ticket image:", error);
    throw error;
  }
};

// Function to download all tickets as image files
export const downloadAllTicketImages = async (
  orderData: CreateOrderResponse
) => {
  try {
    // Group tickets by direction if needed
    const promises = orderData.tickets.map((ticket) => {
      // Determine if this is a return ticket based on ticket_type or logic
      const isReturnTicket = ticket.ticket_type === "Return";

      return downloadSingleTicketImage(ticket, orderData, isReturnTicket);
    });

    await Promise.all(promises);
  } catch (error) {
    console.error("Error downloading all tickets:", error);
    throw error;
  }
};

// Legacy function names for backward compatibility (deprecated)
export const downloadSingleTicketHTML = downloadSingleTicketImage;
export const downloadAllTicketHTMLs = downloadAllTicketImages;
