import React, { useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Button } from "@/shared/components/button";
import { cn } from "@/lib/utils";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

interface ForgotPasswordSuccessProps {
  className?: string;
}

export const ForgotPasswordSuccess: React.FC<ForgotPasswordSuccessProps> = ({
  className,
}) => {
  const navigate = useNavigate();

  useEffect(() => {
    const timeout = setTimeout(() => {
      navigate({ to: "/" });
    }, 2500);

    return () => clearTimeout(timeout);
  }, [navigate]);

  const handleContinue = () => {
    navigate({ to: "/" });
  };

  return (
    <div
      className={cn(
        "w-full max-w-md mx-auto h-screen flex flex-col",
        className
      )}
    >
      <div className="flex-1 overflow-y-auto px-4 py-6">
        <div className="min-h-full flex flex-col justify-center">
          <div className="space-y-8 md:space-y-12">
            {/* Success Icon and Message */}
            <div className="text-center space-y-4 md:space-y-6">
              <div className="flex justify-center">
                <CheckCircleIcon className="w-16 h-16 md:w-24 md:h-24 text-green-500" />
              </div>

              <div className="space-y-3 md:space-y-4">
                <h2 className="text-2xl md:text-[32px] font-extrabold text-[#181818] leading-tight px-4">
                  Đặt lại mật khẩu thành công!
                </h2>
                <p className="text-base md:text-[18px] text-[#5C5C5C] leading-relaxed px-4">
                  Mật khẩu của bạn đã được cập nhật thành công. Bạn có thể đăng
                  nhập với mật khẩu mới.
                </p>
              </div>
            </div>

            {/* Continue Button */}
            <div className="px-4">
              <Button
                onClick={handleContinue}
                className="w-full bg-[#2D5BFF] hover:bg-[#2D5BFF]/90 text-white py-6 rounded-lg font-extrabold text-[18px] tracking-wide"
              >
                Quay về trang chủ
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
