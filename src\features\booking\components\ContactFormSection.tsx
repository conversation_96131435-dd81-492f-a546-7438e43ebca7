import { memo } from "react";
import { Label } from "@/shared/components/label";
import { CheckIcon } from "@heroicons/react/24/outline";

interface ContactFormSectionProps {
  customerName: string;
  setCustomerName: (value: string) => void;
  customerPhone: string;
  setCustomerPhone: (value: string) => void;
  customerEmail: string;
  setCustomerEmail: (value: string) => void;
  acceptTerms: boolean;
  setAcceptTerms: (value: boolean) => void;
  isSubmitting: boolean;
  formErrors: { name?: string; phone?: string; email?: string; terms?: string };
}

const RequiredLabel: React.FC<{ text: string; isRequired?: boolean }> = ({
  text,
  isRequired = true,
}) => (
  <div>
    <label className="block font-bold text-[#5C5C5C] text-[12px] leading-4 mb-1 font-mulish">
      {text} {isRequired && <span className="text-red-500">*</span>}
    </label>
  </div>
);

const ContactFormSection = memo(
  ({
    customerName,
    setCustomerName,
    customerPhone,
    setCustomerPhone,
    customerEmail,
    setCustomerEmail,
    acceptTerms,
    setAcceptTerms,
    isSubmitting,
    formErrors,
  }: ContactFormSectionProps) => {
    return (
      <div className="bg-white border border-[#EDEDED] rounded-xl p-3 lg:p-6">
        <h3 className="text-lg font-bold text-[#181818] mb-4">
          Thông tin liên hệ
        </h3>
        <hr className="border-[#EDEDED] mb-4 lg:mb-6" />

        {/* Mobile Layout - Single Column */}
        <div className="lg:hidden space-y-4">
          <div className="relative mb-6">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${formErrors.name ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Tên người đi" />
              <div className="flex items-center relative">
                <input
                  type="text"
                  placeholder="Nhập tên người đi"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {formErrors.name && (
              <div className="absolute top-full mt-1 left-0 right-0 z-10">
                <span className="text-red-500 text-xs bg-white px-2 line-clamp-2">
                  {formErrors.name}
                </span>
              </div>
            )}
          </div>
          <div className="relative mb-6">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${formErrors.phone ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Số điện thoại" />
              <input
                type="tel"
                placeholder="Nhập số điện thoại"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                required
                disabled={isSubmitting}
              />
            </div>

            {formErrors.phone && (
              <div className="absolute top-full my-1 left-0 right-0 z-10">
                <span className="text-red-500 text-xs bg-white px-2 line-clamp-2">
                  {formErrors.phone}
                </span>
              </div>
            )}
          </div>
          <div className="relative mb-6">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50${formErrors.email ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Email nhận thông tin" isRequired={false} />
              <input
                type="email"
                placeholder="Nhập email"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                disabled={isSubmitting}
              />
            </div>

            {formErrors.email && (
              <div className="absolute top-full mt-1 left-0 right-0 z-10">
                <span className="text-red-500 text-xs bg-white px-2 line-clamp-2">
                  {formErrors.email}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Desktop Layout - Three Column Grid */}
        <div className="hidden lg:grid lg:grid-cols-3 lg:gap-4 lg:mb-8">
          <div className="relative">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${formErrors.name ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Tên người đi" />
              <div className="flex items-center relative">
                <input
                  type="text"
                  placeholder="Nhập tên người đi"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {formErrors.name && (
              <div className="absolute top-full left-0 right-0 z-10">
                <span className="text-red-500 text-xs bg-white px-2 line-clamp-2">
                  {formErrors.name}
                </span>
              </div>
            )}
          </div>
          <div className="relative">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${formErrors.phone ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Số điện thoại" />
              <div className="flex items-center relative">
                <input
                  type="tel"
                  placeholder="Nhập số điện thoại"
                  value={customerPhone}
                  onChange={(e) => setCustomerPhone(e.target.value)}
                  className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>
            <div className="relative h-1.5">
              {formErrors.phone && (
                <div className="absolute top-full left-0 right-0 z-10">
                  <span className="text-red-500 text-xs bg-white px-2 line-clamp-2">
                    {formErrors.phone}
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="relative">
            <div
              className={`bg-[#F8F8F8] rounded-lg px-4 py-1.5 w-full transition-all hover:bg-[#f0f0f0] focus-within:bg-[#f0f0f0] focus-within:ring-2 focus-within:ring-[#2d5bff] focus-within:ring-opacity-50 ${formErrors.email ? "ring-2 ring-red-500 ring-opacity-50" : ""}`}
            >
              <RequiredLabel text="Email nhận thông tin" isRequired={false} />
              <div className="flex items-center relative">
                <input
                  type="email"
                  placeholder="Nhập email"
                  value={customerEmail}
                  onChange={(e) => setCustomerEmail(e.target.value)}
                  className="w-full bg-transparent outline-none text-[#181818] text-[14px] leading-[18px] font-mulish placeholder:text-[#BDBDBD] pr-8"
                  disabled={isSubmitting}
                />
              </div>
            </div>
            {formErrors.email && (
              <div className="absolute top-full left-0 right-0 z-10">
                <span className="text-red-500 text-xs bg-white px-2">
                  {formErrors.email}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Terms and Conditions Checkbox */}
        <div className="space-y-2 relative">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <button
                type="button"
                onClick={() => setAcceptTerms(!acceptTerms)}
                disabled={isSubmitting}
                className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                  acceptTerms
                    ? "bg-[#2D5BFF] border-[#2D5BFF]"
                    : "bg-white border-[#D9D9D9] hover:border-[#2D5BFF]"
                } ${formErrors.terms ? "border-red-500" : ""}`}
              >
                {acceptTerms && <CheckIcon className="w-3 h-3 text-white" />}
              </button>
            </div>
            <Label
              className={`text-sm cursor-pointer line-clamp-2 gap-1 ${formErrors.terms ? "text-red-500" : "text-[#181818]"}`}
              onClick={() => setAcceptTerms(!acceptTerms)}
            >
              <span
                className={`underline ${formErrors.terms ? "text-red-500" : "text-blue-600"}`}
              >
                Chấp nhận điều khoản
              </span>{" "}
              đặt vé & chính sách bảo mật thông tin.
            </Label>
          </div>
        </div>
      </div>
    );
  }
);

ContactFormSection.displayName = "ContactFormSection";

export default ContactFormSection;
