import {
  DeliveryTypeEnum,
  OrderStatusEnum,
  PaymentStatusEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";

export interface CreateOrderRequest {
  food_court_table_id: string;
  delivery_type: DeliveryTypeEnum;
  special_instructions?: string;
  payment_method: PaymentTypeEnum;
  items: OrderItemRequest[];
}

export interface OrderItemRequest {
  food_item_id: string;
  quantity: number;
  special_instructions?: string;
}

export interface CreateOrderResponse {
  id: string;
  user_id: number;
  food_court_table_id: string;
  order_number: string;
  delivery_type: string;
  special_instructions: string;
  total_amount: number;
  status: OrderStatusEnum;
  payment_status: PaymentStatusEnum;
  payment_method: string;
  ordered_at: string;
  estimated_ready_at: string;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}
