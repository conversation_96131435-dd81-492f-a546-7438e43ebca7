import logo from "@/logo.png";
import {
  Link,
  useLocation,
  useNavigate,
  useSearch,
} from "@tanstack/react-router";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { Bars3Icon, UserIcon } from "@heroicons/react/24/outline";
import { useState, useCallback, useMemo, useEffect } from "react";
import { useAuthState, useLogout } from "@/shared/hooks/useAuth";
import { Container } from "./Container";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/components/dropdown-menu";
import { Dialog, DialogContent } from "@/shared/components/dialog";
import { cn } from "@/lib/utils";

export function Header() {
  const { t } = useTranslation();
  const location = useLocation();
  const search = useSearch({ strict: false });
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const { isAuthenticated, isLoading, profile } = useAuthState();
  const logoutMutation = useLogout();
  const navigate = useNavigate();
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMobileMenu = useCallback(() => {
    if (isMobileMenuOpen) {
      setIsDrawerVisible(false);
      setTimeout(() => setIsMobileMenuOpen(false), 300);
    } else {
      setIsMobileMenuOpen(true);
      setTimeout(() => setIsDrawerVisible(true), 10);
    }
  }, [isMobileMenuOpen]);

  const closeMobileMenu = useCallback(() => {
    setIsDrawerVisible(false);
    setTimeout(() => setIsMobileMenuOpen(false), 300);
  }, []);

  // Close drawer when clicking outside or on backdrop
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isMobileMenuOpen) {
        closeMobileMenu();
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isMobileMenuOpen, closeMobileMenu]);

  const handleLogout = useCallback(() => {
    setLogoutDialogOpen(true);
  }, []);

  const confirmLogout = () => {
    setLogoutDialogOpen(false);
    logoutMutation.mutate();
  };

  // Navigation items with dynamic active state
  const navigationItems = useMemo(
    () => [
      {
        key: "home",
        label: "Trang chủ",
        to: "/",
        active: location.pathname === "/",
      },
      {
        key: "look-up-tickets",
        label: "Tra cứu vé",
        to: "/look-up-tickets",
        active: location.pathname.startsWith("/look-up-tickets"),
      },
      {
        key: "merchants",
        label: "Gọi món",
        to: "/merchants",
        active: location.pathname.startsWith("/merchants"),
      },
      // { key: "order", label: "Gọi món", to: "/order", active: false },
      // { key: "news", label: "Tin tức", to: "/news", active: false },
      // { key: "contact", label: "Liên hệ", to: "/contact", active: false },
    ],
    [location.pathname]
  );

  const accountManagementItems = useMemo(
    () => [
      {
        key: "account",
        label: "Thông tin tài khoản",
        to: "/account-management",
        active:
          location.pathname.startsWith("/account-management") &&
          (!search.tab || search.tab === "account"),
        search: { tab: "account" },
      },

      {
        key: "tickets",
        label: "Quản lý vé",
        to: "/account-management",
        active:
          location.pathname.startsWith("/account-management") &&
          search.tab === "tickets",
        search: { tab: "tickets" },
      },

      // {
      //   key: "password",
      //   label: "Đổi mật khẩu",
      //   to: "/account-management",
      //   active:
      //     location.pathname.startsWith("/account-management") &&
      //     search.tab === "password",
      //   search: { tab: "password" },
      // },

      // { key: "order", label: "Gọi món", to: "/order", active: false },
      // { key: "news", label: "Tin tức", to: "/news", active: false },
      // { key: "contact", label: "Liên hệ", to: "/contact", active: false },
    ],
    [location.pathname, search.tab]
  );

  // User profile display component
  const UserProfile = useMemo(() => {
    if (isLoading) {
      return (
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
          <div className="w-24 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      );
    }

    if (isAuthenticated && profile) {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger className="flex items-center gap-4 hover:opacity-80 transition-opacity">
            {/* Avatar */}
            <div className="w-8 h-8 bg-[#EFEFEF] rounded-full flex items-center justify-center">
              {profile?.avatar?.sizes ? (
                <img
                  src={
                    profile?.avatar?.sizes?.medium ||
                    profile?.avatar?.sizes?.original
                  }
                  alt={profile?.avatar?.originalName || "avatar"}
                  className="w-full h-full object-cover"
                />
              ) : (
                <UserIcon className="w-4 h-4 text-[#8A8A8A]" />
              )}
            </div>

            {/* User name */}
            <div className="flex flex-col">
              <span className="text-base font-bold text-[#5C5C5C] leading-5">
                {profile.full_name || "Người dùng"}
              </span>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            <DropdownMenuItem
              onClick={() =>
                navigate({
                  to: "/account-management",
                  search: { tab: "account" },
                })
              }
            >
              Thông tin tài khoản
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                navigate({
                  to: "/account-management",
                  search: { tab: "tickets" },
                })
              }
            >
              Quản lý vé của tôi
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              Đăng xuất
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return null;
  }, [isLoading, isAuthenticated, profile]);

  // const avatarUrl =
  //   profile?.avatar?.sizes?.medium ||
  //   profile?.avatar?.sizes?.original ||
  //   "/default-avatar.png";

  return (
    <div
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        isScrolled ? "bg-white shadow-md" : "bg-transparent"
      )}
    >
      <Container>
        <header className="bg-transparent">
          <div className="">
            {/* Mobile Header Layout */}
            <div className="flex justify-between items-center h-20 md:hidden">
              {/* Logo - Mobile */}
              <Link to="/" className="flex items-center">
                <img
                  className="h-[29px] w-[126px]"
                  src={logo}
                  alt="GTech Ecommerce"
                />
              </Link>

              {/* Right side - Current tab (if on account management) + VN Flag and Hamburger */}
              <div className="flex items-center gap-4">
                {/* Language Switcher with VN flag - Mobile */}
                <LanguageSwitcher />

                {/* Hamburger Menu Button */}
                <button
                  onClick={toggleMobileMenu}
                  className="p-2 border border-[#D6DDEB] rounded-full bg-white hover:bg-gray-50 transition-colors"
                >
                  <Bars3Icon className="w-5 h-5 text-[#25324B]" />
                </button>
              </div>
            </div>

            {/* Desktop Header Layout */}
            <div className="hidden md:flex justify-between items-center h-16">
              {/* Logo - Desktop */}
              <div className="flex items-center">
                <Link to="/" className="flex items-center">
                  <img
                    className="h-[29px] w-[126px]"
                    src={logo}
                    alt="GTech Ecommerce"
                  />
                </Link>
              </div>

              {/* Navigation Menu - Desktop */}
              <nav className="flex items-center gap-8">
                {navigationItems.map((item) => (
                  <Link
                    key={item.key}
                    to={item.to}
                    className={`text-base font-bold transition-colors ${
                      item.active
                        ? "text-[#2D5BFF]"
                        : "text-[#5C5C5C] hover:text-[#2D5BFF]"
                    }`}
                  >
                    {item.label}
                  </Link>
                ))}
              </nav>

              {/* Right side - Language Switcher and User Profile */}
              <div className="flex items-center gap-2">
                {/* Language Switcher with VN flag */}
                <div className="flex items-center justify-center bg-white border border-gray-200 rounded-lg px-4 py-3 h-11">
                  <LanguageSwitcher variant="compact" />
                </div>

                {/* Divider */}
                <div className="w-px h-6 bg-[#D7D7D7]" />

                {/* User Profile or Auth buttons */}
                {isAuthenticated ? (
                  <div className="flex items-center gap-4">{UserProfile}</div>
                ) : (
                  <div className="flex items-center gap-4">
                    <Link
                      to="/auth"
                      search={{ tab: "signin" }}
                      className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
                    >
                      {t("header.login")}
                    </Link>
                    <Link
                      to="/auth"
                      search={{ tab: "signup" }}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                    >
                      {t("header.register")}
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Menu Drawer */}
            {isMobileMenuOpen && (
              <>
                {/* Backdrop */}
                <div
                  className={`fixed inset-0 bg-[rgba(0,0,0,0.2)] z-40 md:hidden transition-opacity duration-300 ${
                    isDrawerVisible ? "bg-opacity-10" : "bg-opacity-0"
                  }`}
                  onClick={closeMobileMenu}
                />

                {/* Drawer */}
                <div
                  className={`fixed top-0 right-0 h-full w-80 bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out md:hidden ${
                    isDrawerVisible ? "translate-x-0" : "translate-x-full"
                  }`}
                >
                  {/* Drawer Content */}
                  <div className="flex flex-col h-full">
                    {/* Navigation items */}
                    <nav className="flex-1 px-4 py-6">
                      <div className="space-y-2">
                        {navigationItems.map((item) => (
                          <Link
                            key={item.key}
                            to={item.to}
                            className={`block text-base font-medium px-4 py-3 rounded-lg transition-colors ${
                              item.active
                                ? "text-[#2D5BFF]"
                                : "text-[#5C5C5C] hover:text-[#2D5BFF] hover:bg-gray-50"
                            }`}
                            onClick={closeMobileMenu}
                          >
                            {item.label}
                          </Link>
                        ))}

                        {isAuthenticated && <hr />}

                        {isAuthenticated &&
                          accountManagementItems.map((item) => (
                            <Link
                              key={item.key}
                              to={item.to}
                              search={item.search}
                              className={`block text-base font-medium px-4 py-3 rounded-lg transition-colors ${
                                item.active
                                  ? "text-[#2D5BFF]"
                                  : "text-[#5C5C5C] hover:text-[#2D5BFF] hover:bg-gray-50"
                              }`}
                              onClick={closeMobileMenu}
                            >
                              {item.label}
                            </Link>
                          ))}

                        {/* User Section */}
                        <div className="border-t border-gray-200 py-4 pb-6">
                          {isAuthenticated ? (
                            <div className="space-y-4">
                              <button
                                onClick={() => {
                                  handleLogout();
                                  closeMobileMenu();
                                }}
                                className="block text-base font-medium px-4 py-3 rounded-lg transition-colors text-[#5C5C5C] hover:text-[#2D5BFF] hover:bg-gray-50"
                              >
                                Đăng xuất
                              </button>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              <Link
                                to="/auth"
                                search={{ tab: "signin" }}
                                className="block text-base font-medium px-4 py-3 rounded-lg transition-colors text-[#5C5C5C] hover:text-[#2D5BFF] hover:bg-gray-50"
                                onClick={closeMobileMenu}
                              >
                                {t("header.login")}
                              </Link>
                              <Link
                                to="/auth"
                                search={{ tab: "signup" }}
                                className="block text-base font-medium px-4 py-3 rounded-lg transition-colors text-[#5C5C5C] hover:text-[#2D5BFF] hover:bg-gray-50"
                                onClick={closeMobileMenu}
                              >
                                {t("header.register")}
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>
                    </nav>
                  </div>
                </div>
              </>
            )}
          </div>
        </header>
      </Container>

      {/* Logout Confirm Dialog */}
      <Dialog open={logoutDialogOpen} onOpenChange={setLogoutDialogOpen}>
        <DialogContent className="bg-white rounded-2xl shadow-[0px_0px_20px_0px_rgba(132,132,132,0.2)] p-10 max-w-[400px] w-full">
          <div className="text-[#181818] text-[18px] font-bold font-mulish mb-8 text-center leading-6">
            Bạn có chắc chắn muốn đăng xuất?
          </div>
          <div className="flex flex-col gap-4">
            <button
              className="bg-[#ff3030] text-white text-[16px] font-extrabold font-mulish rounded-lg py-3 tracking-wide hover:bg-[#e02b2b] transition"
              onClick={confirmLogout}
            >
              Xác nhận
            </button>
            <button
              className="bg-[#ecf0ff] text-[#2d5bff] text-[16px] font-extrabold font-mulish rounded-lg py-3 tracking-wide hover:bg-[#d6e0ff] transition"
              onClick={() => setLogoutDialogOpen(false)}
            >
              Huỷ
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
