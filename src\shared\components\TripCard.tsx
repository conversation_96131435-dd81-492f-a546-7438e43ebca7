import { memo } from "react";
import { TripTimeDisplay } from "./TripTimeDisplay";
import { BusImage } from "./BusImage";
import { MapPinIcon } from "@heroicons/react/24/solid";
import type { SelectedTrip } from "@/stores/selectedTripsStore";
import { ChangeTripIcon } from "@/shared/components/icons";

interface TripCardProps {
  trip: SelectedTrip;
  tripType?: "outbound" | "return";
  onChangeTrip?: () => void;
  showChangeButton?: boolean;
}

const TripCard = memo(
  ({
    trip,
    // tripType,
    onChangeTrip,
    showChangeButton = false,
  }: TripCardProps) => {
    // const isOutbound = tripType === "outbound";
    // const directionTag = isOutbound ? "Chiều đi" : "Chiều về";
    // const tagBgColor = isOutbound ? "bg-[#2D5BFF]" : "bg-[#FF7F37]";

    // Format departure date for display
    const formatDate = (dateString: string) => {
      try {
        const date = new Date(dateString);
        const dayNames = [
          "Chủ Nhật",
          "Thứ 2",
          "Thứ 3",
          "Thứ 4",
          "Thứ 5",
          "Thứ 6",
          "Thứ 7",
        ];
        const dayName = dayNames[date.getDay()];
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        return `${dayName}, ${day}/${month}`;
      } catch {
        return dateString;
      }
    };

    const handleChangeTrip = () => {
      if (onChangeTrip) {
        onChangeTrip();
      }
    };

    return (
      <div className="bg-white border border-[#EDEDED] rounded-xl p-3 lg:p-4">
        {/* Mobile Layout */}
        <div className="lg:hidden">
          {/* Header Row */}
          <div className="flex justify-between items-start gap-4 mb-3">
            {/* Left: Tags and Route Info - Vertical layout */}
            <div className="flex flex-col gap-1">
              {/* <div
              className={`${tagBgColor} text-white text-xs font-bold px-3 py-1.5 rounded h-5 flex items-center justify-center w-fit`}
            >
              {directionTag}
            </div> */}
              <span className="text-base font-bold text-[#2D5BFF]">
                {trip.route}
              </span>
              <span className="text-sm text-[#5C5C5C]">
                {trip.departureDate ? formatDate(trip.departureDate) : ""}
              </span>
            </div>
            {showChangeButton && (
              <button
                type="button"
                onClick={handleChangeTrip}
                className="cursor-pointer bg-[#ECF0FF] rounded-md py-1.5 px-2 flex gap-2 items-center"
              >
                <ChangeTripIcon />{" "}
                <p className="text-sm font-extrabold text-[#2D5BFF]">
                  Đổi chuyến
                </p>
              </button>
            )}
          </div>

          {/* Trip Information */}
          <div className="flex flex-col gap-3">
            {/* Company Name */}
            <h4 className="text-xl font-extrabold text-[#181818]">
              {trip.companyName}
            </h4>

            {/* Time and Route Section */}
            <div className="flex flex-col gap-2">
              {/* Time Row */}
              <div className="flex items-center gap-4">
                <span className="text-2xl font-extrabold text-[#181818] w-[60px]">
                  {trip.departureTime}
                </span>

                {/* Timeline */}
                <div className="flex items-center gap-1 flex-1">
                  {/* Start Circle */}
                  <div className="w-4 h-4 bg-[#2D5BFF] rounded-full flex-shrink-0"></div>

                  {/* Dashed Line */}
                  <div className="border-t border-[#D7D7D7] border-dashed flex-1 min-w-[20px]"></div>

                  {/* Duration */}
                  <span className="text-base text-[#5C5C5C] whitespace-nowrap">
                    {trip.duration}
                  </span>

                  {/* Dashed Line */}
                  <div className="border-t border-[#D7D7D7] border-dashed flex-1 min-w-[20px]"></div>

                  {/* End Pin */}
                  <MapPinIcon className="w-4 h-4 text-[#FF7F37] flex-shrink-0" />
                </div>

                <span className="text-2xl font-extrabold text-[#181818] w-[60px] text-right">
                  {trip.arrivalTime}
                </span>
              </div>

              {/* Location Row */}
              <div className="flex justify-between items-start gap-4">
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-[#181818] text-left truncate">
                    {trip.departureLocation}
                  </p>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-[#181818] text-right truncate">
                    {trip.arrivalLocation}
                  </p>
                </div>
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-[#EDEDED] w-full"></div>

            {/* Note */}
            <p className="text-xs leading-[16px]">
              Quý khách vui lòng có mặt tại Bến xe/Văn Phòng{" "}
              <span className="font-bold">QUẢNG PHÚ (Cư Mgar)</span>{" "}
              <span className="text-[#FF7F37] font-bold">
                Trước 16:45 28/05/2025
              </span>{" "}
              để kiểm tra thông tin trước khi lên xe.
            </p>
          </div>
        </div>

        {/* Desktop Layout - Keep original */}
        <div className="hidden lg:block">
          {/* Header with tag and change button */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              {/* <div
              className={`${tagBgColor} text-white text-xs font-bold px-3 py-1.5 rounded-sm`}
            >
              {directionTag}
            </div> */}
              <span className="text-base font-bold text-[#2D5BFF]">
                {trip.route}
              </span>
              <span className="text-sm text-[#5C5C5C]">
                {trip.departureDate}
              </span>
            </div>
            {showChangeButton && (
              <button
                type="button"
                onClick={handleChangeTrip}
                className="cursor-pointer bg-[#ECF0FF] rounded-md py-1.5 px-2 flex gap-2 items-center"
              >
                <ChangeTripIcon />{" "}
                <p className="text-sm font-extrabold text-[#2D5BFF]">
                  Đổi chuyến
                </p>
              </button>
            )}
          </div>

          <div className="border-t border-[#EDEDED] border-dashed mb-4"></div>

          {/* Trip details - Desktop: Horizontal */}
          <div className="flex gap-4 flex-1">
            {/* Bus image - Desktop: Fixed width */}
            <div className="w-32 h-[158px] bg-white rounded-lg overflow-hidden flex-shrink-0">
              <BusImage src={trip.image} alt={`${trip.companyName} Bus`} />
            </div>

            {/* Trip info */}
            <div className="flex-1 flex flex-col justify-between">
              {/* Company name and time section */}
              <div className="flex flex-col gap-3">
                <h4 className="text-xl font-extrabold text-[#181818]">
                  {trip.companyName}
                </h4>

                {/* Time and location layout */}
                <TripTimeDisplay
                  departureTime={trip.departureTime}
                  arrivalTime={trip.arrivalTime}
                  departureLocation={trip.departureLocation}
                  arrivalLocation={trip.arrivalLocation}
                  duration={trip.duration}
                  variant="default"
                  showLocations={true}
                />
              </div>

              {/* Note */}
              <p className="text-xs text-[#5C5C5C] leading-4 mt-3">
                Quý khách vui lòng có mặt tại Bến xe/Văn Phòng{" "}
                <span className="font-bold">QUẢNG PHÚ (Cư Mgar)</span>{" "}
                <span className="text-[#FF7F37] font-bold">
                  Trước 16:45 28/05/2025
                </span>{" "}
                để kiểm tra thông tin trước khi lên xe.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

TripCard.displayName = "TripCard";

export { TripCard };
