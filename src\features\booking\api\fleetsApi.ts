import axiosInstance from "@/shared/api/axiosInstance";
import { CREATE_FLEET_API_ENDPOINT } from "@/shared/api/apiEndpoint";
import type { CreateFleetRequest, Fleet } from "@/shared/types/auth";

// Create a new fleet
export const createFleet = async (data: CreateFleetRequest): Promise<Fleet> => {
  const response = await axiosInstance.post(CREATE_FLEET_API_ENDPOINT, data);
  return response.data;
};

// Get all fleets (you may want to add this endpoint)
export const getFleets = async (): Promise<Fleet[]> => {
  const response = await axiosInstance.get(CREATE_FLEET_API_ENDPOINT);
  return response.data;
};

// Get fleet by ID (you may want to add this endpoint)
export const getFleetById = async (id: number): Promise<Fleet> => {
  const response = await axiosInstance.get(
    `${CREATE_FLEET_API_ENDPOINT}/${id}`
  );
  return response.data;
};

// Update fleet (you may want to add this endpoint)
export const updateFleet = async (
  id: number,
  data: Partial<CreateFleetRequest>
): Promise<Fleet> => {
  const response = await axiosInstance.put(
    `${CREATE_FLEET_API_ENDPOINT}/${id}`,
    data
  );
  return response.data;
};

// Delete fleet (you may want to add this endpoint)
export const deleteFleet = async (id: number): Promise<void> => {
  await axiosInstance.delete(`${CREATE_FLEET_API_ENDPOINT}/${id}`);
};
