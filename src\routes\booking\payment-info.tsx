import {
  createFileRout<PERSON>,
  <PERSON>,
  useNavigate,
  useRouter,
} from "@tanstack/react-router";
import { z } from "zod";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import { useContactFormStore } from "@/stores/contactFormStore";
import { PriceSummary } from "@/features/booking/components";
import { TicketQRBox } from "@/components/ticket/TicketQRBox";
import { VietQRPopup } from "@/components/merchants-order-confirmation/VietQRPopup";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

const paymentInfoSearchSchema = z.object({
  customerName: z.string(),
  customerPhone: z.string(),
  customerEmail: z.string().optional(),
  numTickets: z.number().or(z.string()),
  orderId: z.string(),
});

export const Route = createFileRoute("/booking/payment-info")({
  validateSearch: paymentInfoSearchSchema,
  component: PaymentInfoPage,
});

function PaymentInfoPage() {
  const navigate = useNavigate();
  const search = Route.useSearch();
  const router = useRouter();

  const { getTotalPrice } = useSelectedTripsStore();
  const { clearFormData } = useContactFormStore();

  // Parse search params
  const parsedSearch = paymentInfoSearchSchema.safeParse(search);
  const safeSearch = parsedSearch.success
    ? parsedSearch.data
    : {
        customerName: "",
        customerPhone: "",
        numTickets: 1,
        customerEmail: "",
        orderId: "",
      };

  const numTickets = Number(safeSearch.numTickets) || 1;
  const totalPrice = getTotalPrice() * numTickets;

  // Hardcoded bank info & QR
  const bankInfo = {
    bank: "VIETINBANK",
    accountNumber: "**********",
    accountName: "CHUTAIKHOAN",
    qrImage: "/assets/qr-fake.png", // TODO: Replace with real QR
    content: `VE${safeSearch.customerPhone}`,
  };

  // Download QR handler
  const handleDownloadQR = () => {
    const link = document.createElement("a");
    link.href = bankInfo.qrImage;
    link.download = "vietqr.png";
    link.click();
  };

  // Confirm transfer handler
  const handleConfirmTransfer = () => {
    // Clear form data khi hoàn thành thanh toán
    clearFormData();

    navigate({
      to: "/booking/payment-verification",
      search: { orderId: safeSearch.orderId },
    });
  };

  const handleBackDetailTicket = () => {
    router.history.back();
  };

  const handleBackToSearch = () => {
    router.history.back();
    router.history.back();
  };

  return (
    <div className="min-h-screen bg-[#F4F6FB]">
      <div className="w-full px-4 py-6 space-y-3 mx-auto max-w-[1200px]">
        <nav className="text-sm text-[#8A8A8A] hidden lg:flex gap-2 items-center">
          <Link to="/">Trang chủ</Link>
          <ChevronRightIcon className="text-gray-400 size-4" />
          <button
            className="cursor-pointer"
            type="button"
            onClick={handleBackToSearch}
          >
            Tìm chuyến
          </button>
          <ChevronRightIcon className="text-gray-400 size-4" />
          <button
            type="button"
            onClick={handleBackDetailTicket}
            className="cursor-pointer"
          >
            Chi tiết vé
          </button>
          <ChevronRightIcon className="text-gray-400 size-4" />
          <span className="text-[#181818] font-semibold">Thanh toán</span>
        </nav>
        <h1 className="text-3xl font-extrabold text-[#181818]">Thanh toán</h1>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex w-full max-w-[1200px] mx-auto flex-col lg:flex-row gap-8 px-4 py-8">
        {/* Left: QR Section */}
        <div className="flex-1 w-full lg:w-auto">
          <TicketQRBox
            qrImage={bankInfo.qrImage}
            bank={bankInfo.bank}
            accountNumber={bankInfo.accountNumber}
            accountName={bankInfo.accountName}
            amount={totalPrice}
            content={bankInfo.content}
            expiryText={"Mã sẽ hết hạn vào lúc 12:11 Thứ 3, 10/6"}
            onDownloadQR={handleDownloadQR}
          />
        </div>

        {/* Right: PriceSummary */}
        <div className="w-full lg:w-[360px] lg:sticky lg:top-8">
          <PriceSummary
            onPayment={handleConfirmTransfer}
            isSubmitting={false}
            numTickets={numTickets}
            buttonTitle="Tôi đã chuyển khoản"
          />
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="flex min-h-screen lg:hidden">
        <VietQRPopup
          show={true}
          onClose={() => {}}
          qrData={bankInfo}
          onDownloadQR={handleDownloadQR}
          onConfirmTransfer={handleConfirmTransfer}
          totalPrice={totalPrice}
          bgColor="bg-[#FF7F37]"
          buttonTitle="Tôi đã chuyển khoản"
        />
      </div>
    </div>
  );
}
