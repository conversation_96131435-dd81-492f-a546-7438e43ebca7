import { memo } from "react";
import { useLocation } from "@tanstack/react-router";
import logo from "../../logo.png";
import { FooterDesktop } from "./FooterDesktop";

// Social Icons as SVG Components - Updated with SVG files
export const YoutubeIcon = () => (
  <div className="w-6 h-6 flex items-center justify-center">
    <img src="/assets/youtube.svg" alt="YouTube" className="w-6 h-6" />
  </div>
);

export const FacebookIcon = () => (
  <div className="w-6 h-6 flex items-center justify-center">
    <img src="/assets/facebook.svg" alt="Facebook" className="w-6 h-6" />
  </div>
);

export const TikTokIcon = () => (
  <div className="w-6 h-6 flex items-center justify-center">
    <img src="/assets/tiktok.svg" alt="TikTok" className="w-6 h-6" />
  </div>
);

const Footer = memo(() => {
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  return (
    <div className={`bg-[#F8F8F8] ${isHomePage && "mt-10"}`}>
      {/* Mobile Footer */}
      <div className="lg:hidden">
        <footer className="px-6 py-8">
          {/* Main Content Container */}
          <div className="flex flex-col gap-8">
            {/* Company Info Section */}
            <div className="flex flex-col items-center gap-4">
              {/* Logo */}
              <div className="w-[196px] h-[45px]">
                <img
                  className="w-full h-full object-contain"
                  src={logo}
                  alt="GTech"
                />
              </div>

              {/* Company Name */}
              <p className="text-sm font-bold text-[#5C5C5C] text-center leading-[18px] max-w-[291px]">
                CÔNG TY CỔ PHẦN GIẢI PHÁP CÔNG NGHỆ PHẦN MỀM GTECH
              </p>

              {/* Certification Logo */}
              <div className="w-[120px] h-[45px]">
                <img
                  className="w-full h-full object-contain"
                  src="assets/certificate.png"
                  alt="Certification Logo"
                />
              </div>
            </div>

            {/* Company Details */}
            <div className="text-center">
              <p className="text-sm text-[#747474] leading-[18px] whitespace-pre-line">
                Địa chỉ đăng ký kinh doanh: Tầng 4, Số 4 Ngõ 91 Phố Trần Duy
                Hưng, Phường Trung Hoà, Quận Cầu Giấy, Thành phố Hà Nội, Việt
                Nam
                {"\n\n"}Giấy chứng nhận ĐKKD số 0110218665
              </p>
            </div>

            {/* Social Icons */}
            <div className="flex justify-center items-center gap-5">
              <YoutubeIcon />
              <FacebookIcon />
              <TikTokIcon />
            </div>
          </div>

          {/* Separator Line */}
          <hr className="border-[#EDEDED] my-6" />

          {/* Copyright Section */}
          <div className="text-center">
            <p className="text-sm text-[#5C5C5C] font-normal">
              Bản quyền © 2025 thuộc về Gtech
            </p>
          </div>
        </footer>
      </div>

      {/* Desktop Footer - Keep original layout */}
      <FooterDesktop />
    </div>
  );
});

Footer.displayName = "Footer";

export default Footer;
