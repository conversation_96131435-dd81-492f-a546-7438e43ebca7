// Image sizes interface
export interface ImageSizes {
  large: string;
  small: string;
  medium: string;
  original: string;
  thumbnail: string;
}

// Bus image interface
export interface BusImage {
  id: string;
  sizes: ImageSizes;
  uploadedAt: string;
  originalName: string;
}

// Bus interface
export interface Bus {
  id: string;
  license_plate: string;
  brand: string;
  model: string;
  seats: number;
  ticket_price: string;
  description: string;
  images: BusImage[];
  is_active: boolean;
  fleet_id: string;
  created_at: string;
  updated_at: string;
  fleet: Fleet;
}

// Fleet interface
export interface Fleet {
  id: string;
  name: string;
  description: string;
  policy: string;
  created_at: string;
  updated_at: string;
}

// Departure Place interface
export interface DeparturePlace {
  id: number;
  parentId: number;
  type: string;
  name: string;
  tags: string;
  level: number;
  code: string;
}

// Arrival Place interface
export interface ArrivalPlace {
  id: number;
  parentId: number;
  type: string;
  name: string;
  tags: string;
  level: number;
  code: string;
}

// Dropoff interface
export interface Dropoff {
  id: string;
  bus_schedule_id: string;
  arrival_place_id: number;
  location_name: string;
  duration_minutes: number;
  ticket_price: string;
  dropoff_order: number;
  created_at: string;
  updated_at: string;
  arrival_place: ArrivalPlace;
  arrival_time: string;
  arrival_date: string;
  remainingTickets: number;
}

// Bus Schedule interface
export interface BusSchedule {
  id: string;
  bus_id: string;
  departure_place_id: number;
  departure_time: string;
  day_of_week: number;
  created_at: string;
  updated_at: string;
  dropoffs: Dropoff[];
  bus: Bus;
  departure_place: DeparturePlace;
}

// Request interfaces
export interface CreateBusScheduleRequest {
  bus_id: string;
  departure_place_id: number;
  departure_time: string;
  day_of_week: number;
}

export interface UpdateBusScheduleRequest {
  bus_id?: string;
  departure_place_id?: number;
  departure_time?: string;
  day_of_week?: number;
}

// Search parameters - Updated to match new API specification
export interface SearchBusSchedulesParams {
  // Required parameters
  fromPlaceId: number;
  toPlaceId: number;
  numTickets: number;
  departureDate: string; // YYYY-MM-DD format

  // Optional filter parameters
  busTypes?: string[]; // bed, chair, limousine
  sortByDepartureTime?: "asc" | "desc";
  sortByPrice?: "asc" | "desc";
  departureTimeFrom?: string; // HH:MM format
  departureTimeTo?: string; // HH:MM format
}

// Response types
export type BusSchedulesResponse = BusSchedule[];
export type BusScheduleResponse = BusSchedule;
