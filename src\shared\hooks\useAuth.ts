import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useUserProfileStore } from "@/shared/store/userProfileStore";
import { useEffect } from "react";
import { authStore } from "@/stores/authStore";

import type {
  SignupRequest,
  VerifyOtpRequest,
  SetPasswordRequest,
  SigninRequest,
  ApiError,
  ForgotPasswordRequest,
  ForgotPasswordVerifyOtpRequest,
  ResetPasswordRequest,
} from "@/shared/types/auth";
import {
  storeResetToken,
  getResetToken,
  clearResetToken,
} from "@/shared/utils/tokenUtils";
import {
  signup,
  verifyOtp,
  setPassword,
  signin,
  getProfile,
  adminTest,
  setAuthToken,
  clearAuthToken,
  forgotPassword,
  forgotPasswordVerifyOtp,
  resetPassword,
} from "@/api/auth/authApi";

// Query keys
export const authKeys = {
  all: ["auth"] as const,
  profile: () => [...authKeys.all, "profile"] as const,
  adminTest: () => [...authKeys.all, "admin-test"] as const,
};

// Signup mutation
export const useSignup = () => {
  return useMutation({
    mutationFn: (data: SignupRequest) => signup(data),
    onError: (error: ApiError) => {
      console.error("Signup failed:", error);
    },
  });
};

// Verify OTP mutation
export const useVerifyOtp = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: VerifyOtpRequest) => verifyOtp(data),
    onSuccess: (data) => {
      // Set auth token if provided
      if (data.access_token) {
        setAuthToken(data.access_token);
        // Store token in localStorage
        localStorage.setItem("access_token", data.access_token);
      }
      // Invalidate auth queries
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
    onError: (error: ApiError) => {
      console.error("OTP verification failed:", error);
    },
  });
};

// Set password mutation
export const useSetPassword = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SetPasswordRequest) => setPassword(data),
    onSuccess: () => {
      // No token handling here since token is set by verify-otp
      // Invalidate auth queries
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
    onError: (error: ApiError) => {
      console.error("Set password failed:", error);
    },
  });
};

// Signin mutation
export const useSignin = () => {
  const queryClient = useQueryClient();
  // const setProfile = useUserProfileStore((state) => state.setProfile);
  const setIsAuthenticated = authStore((state) => state.setIsAuthenticated);

  return useMutation({
    mutationFn: (data: SigninRequest) => signin(data),
    onSuccess: (data) => {
      // Set auth token
      if (data.access_token) {
        setAuthToken(data.access_token);
        // Store token in localStorage
        localStorage.setItem("access_token", data.access_token);
        // Update auth store
        setIsAuthenticated(true);
      }
      // Invalidate auth queries
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
    onError: (error: ApiError) => {
      console.error("Signin failed:", error);
    },
  });
};

// Get profile query
export const useProfile = (enabled: boolean = true) => {
  const setProfile = useUserProfileStore((state) => state.setProfile);
  const setIsAuthenticated = authStore((state) => state.setIsAuthenticated);
  const setIsLoading = authStore((state) => state.setIsLoading);

  const query = useQuery({
    queryKey: authKeys.profile(),
    queryFn: () => getProfile(),
    enabled,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  useEffect(() => {
    if (query.data) {
      setProfile(query.data);
      setIsAuthenticated(true);
    }
    setIsLoading(query.isLoading);
  }, [
    query.data,
    query.isLoading,
    setProfile,
    setIsAuthenticated,
    setIsLoading,
  ]);

  return query;
};

// Admin test query
export const useAdminTest = (enabled: boolean = false) => {
  return useQuery({
    queryKey: authKeys.adminTest(),
    queryFn: () => adminTest(),
    enabled,
    retry: (failureCount, error: any) => {
      // Don't retry on 401/403 errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Logout function
export const useLogout = () => {
  const queryClient = useQueryClient();
  const clearProfile = useUserProfileStore((state) => state.clearProfile);
  const setIsAuthenticated = authStore((state) => state.setIsAuthenticated);

  return useMutation({
    mutationFn: async () => {
      // Clear auth token
      clearAuthToken();
      // Remove token from localStorage
      localStorage.removeItem("access_token");
      // Clear profile from store
      clearProfile();
      // Update auth store
      setIsAuthenticated(false);
      // Clear all queries
      queryClient.clear();
    },
    onSuccess: () => {
      queryClient.clear();
    },
  });
};

// Forgot password mutations
export const useForgotPassword = () => {
  return useMutation({
    mutationFn: (data: ForgotPasswordRequest) => forgotPassword(data),
    onError: (error: ApiError) => {
      console.error("Forgot password failed:", error);
    },
  });
};

export const useForgotPasswordVerifyOtp = () => {
  return useMutation({
    mutationFn: (data: ForgotPasswordVerifyOtpRequest) =>
      forgotPasswordVerifyOtp(data),
    onSuccess: (data) => {
      // Store the reset token temporarily if provided
      if (data.access_token) {
        storeResetToken(data.access_token);
      }
    },
    onError: (error: ApiError) => {
      console.error("Forgot password OTP verification failed:", error);
    },
  });
};

export const useResetPassword = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ResetPasswordRequest) => {
      // Get the reset token from storage
      const resetToken = getResetToken();

      if (!resetToken) {
        throw new Error(
          "Reset token not found. Please restart the password reset process."
        );
      }

      // Call API with token in header
      return resetPassword(data, resetToken);
    },
    onSuccess: (response) => {
      // Clean up the reset token after successful password reset
      clearResetToken();

      // Optional: Set the new access token if provided in response
      if (response.access_token) {
        setAuthToken(response.access_token);
        localStorage.setItem("access_token", response.access_token);
      }

      // Invalidate auth queries
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
    onError: (error: any) => {
      console.error("Reset password failed:", error);

      // Clean up reset token on certain errors to force restart of flow
      if (error?.response?.status === 401) {
        clearResetToken();
      }
    },
  });
};

// Initialize auth on app start and check authentication status
export const useInitializeAuth = () => {
  // Check if token exists in localStorage
  const token = localStorage.getItem("access_token");

  if (token) {
    setAuthToken(token);
  }

  return token;
};

// Custom hook to manage auth state
export const useAuthState = () => {
  const token = useInitializeAuth();
  const storedProfile = useUserProfileStore((state) => state.profile);
  const isAuthenticated = authStore((state) => state.isAuthenticated);
  const isLoading = authStore((state) => state.isLoading);

  // Automatically fetch profile if token exists
  const { isLoading: isProfileLoading, error: profileError } =
    useProfile(!!token);

  return {
    isAuthenticated,
    isLoading: isLoading || isProfileLoading,
    profile: storedProfile,
    token,
    profileError,
  };
};
