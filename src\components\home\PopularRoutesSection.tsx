import React, { useState, useCallback, useEffect } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { Container } from "@/shared/components/Container";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/shared/components/carousel";
import { usePopularRoutes } from "@/shared/hooks/usePopularRoutes";
import {
  transformStrapiPopularRoutes,
  type PopularRoute,
} from "@/shared/utils/strapiDataTransform";
import { Skeleton } from "@/shared/components/skeleton";
import { useSearchForm } from "@/contexts/SearchFormContext";
import { useNavigate } from "@tanstack/react-router";
import { format, addDays } from "date-fns";

// Fallback mock data in case API fails
const fallbackRoutes: PopularRoute[] = [
  {
    id: "1",
    title: "<PERSON><PERSON><PERSON>",
    price: "Từ 200.000đ",
    image:
      "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
  },
  {
    id: "2",
    title: "<PERSON><PERSON> Nội - H<PERSON>i <PERSON>òng",
    price: "Từ 100.000đ",
    image:
      "https://images.unsplash.com/photo-1564507592333-c60657eea523?w=400&h=300&fit=crop",
  },
  {
    id: "3",
    title: "Sài Gòn - Đà Lạt",
    price: "Từ 180.000đ",
    image:
      "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
  },
];

interface RouteCardProps {
  route: PopularRoute;
  isLoading?: boolean;
  onClick?: () => void;
}

const RouteCard: React.FC<RouteCardProps> = ({
  route,
  isLoading = false,
  onClick,
}) => {
  if (isLoading) {
    return (
      <div className="w-full max-w-[230px] lg:max-w-[250px] bg-white rounded-lg shadow-sm overflow-hidden min-h-[170px] lg:min-h-[175px] flex flex-col">
        {/* Image Skeleton - Responsive height */}
        <Skeleton className="w-full h-[90px] lg:h-[100px]" />
        {/* Content Skeleton - Responsive height */}
        <div className="flex flex-col gap-2 p-2 h-[90px] lg:h-[90px] bg-[#2D5BFF] flex-1">
          <Skeleton className="h-4 bg-white/20 rounded" />
          <Skeleton className="h-3 bg-white/20 rounded w-2/3" />
        </div>
      </div>
    );
  }

  return (
    <div
      className="w-full max-w-[230px] lg:max-w-[250px] bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow min-h-[170px] lg:min-h-[175px] flex flex-col cursor-pointer group"
      onClick={onClick}
    >
      {/* Image - Responsive height */}
      <div className="relative w-full h-[90px] lg:h-[100px] bg-gray-200 group-hover:scale-105 transition-transform duration-200">
        <img
          src={route.image}
          alt={route.title}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src =
              "https://via.placeholder.com/250x100/E5E7EB/9CA3AF?text=No+Image";
          }}
        />
      </div>

      {/* Content - Responsive height */}
      <div className="flex flex-col gap-1 p-2 h-[90px] lg:h-[90px] bg-[#2D5BFF] flex-1 group-hover:bg-[#1e40af] transition-colors duration-200">
        <h3 className="text-sm font-bold text-white leading-tight font-mulish line-clamp-2 sm:line-clamp-2">
          {route.title}
        </h3>
        <p className="text-sm text-white font-normal font-mulish">
          {route.price}
        </p>
      </div>
    </div>
  );
};

export const PopularRoutesSection: React.FC = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  // Get search form context and navigation
  const {
    setFromLocation,
    setToLocation,
    setDepartureDate,
    // setIsRoundTrip,
    state,
    places,
  } = useSearchForm();
  const navigate = useNavigate();

  // Fetch popular routes from Strapi
  const { data: strapiRoutes, isLoading, error } = usePopularRoutes();
  // Transform and prepare routes data
  const routes = React.useMemo(() => {
    if (strapiRoutes && strapiRoutes.length > 0) {
      return transformStrapiPopularRoutes(strapiRoutes);
    }
    // Fallback to mock data if API fails or returns empty
    return fallbackRoutes;
  }, [strapiRoutes]);

  // Handle route click - auto fill search form and navigate
  const handleRouteClick = useCallback(
    (route: PopularRoute) => {
      // Extract fromCode and toCode from strapiRoutes data
      const strapiRoute = strapiRoutes?.find(
        (r) => r.id.toString() === route.id
      );

      let fromLocation = "";
      let toLocation = "";
      let fromPlace = null;
      let toPlace = null;

      if (strapiRoute) {
        // First try to find places by code
        fromPlace = places?.find(
          (place) => place.code === strapiRoute.fromCode
        );
        toPlace = places?.find((place) => place.code === strapiRoute.toCode);

        // If found by code, use the place names
        if (fromPlace && toPlace) {
          fromLocation = fromPlace.name;
          toLocation = toPlace.name;
        } else {
          // Fallback: use fromName and toName from strapiRoute
          fromLocation = strapiRoute.fromName;
          toLocation = strapiRoute.toName;

          // Try to find places by name as fallback
          fromPlace = places?.find(
            (place) => place.name === strapiRoute.fromName
          );
          toPlace = places?.find((place) => place.name === strapiRoute.toName);
        }
      } else {
        // Fallback: parse from route title for mock data
        const [fromLoc, toLoc] = route.title.split(" - ");
        fromLocation = fromLoc?.trim() || "";
        toLocation = toLoc?.trim() || "";

        // Try to find places by name
        fromPlace = places?.find((place) => place.name === fromLocation);
        toPlace = places?.find((place) => place.name === toLocation);
      }

      // Check if we have valid places with IDs
      if (!fromPlace || !toPlace) {
        // Last attempt: try fuzzy matching by partial name
        if (!fromPlace && fromLocation) {
          fromPlace = places?.find(
            (place) =>
              place.name.toLowerCase().includes(fromLocation.toLowerCase()) ||
              fromLocation.toLowerCase().includes(place.name.toLowerCase())
          );
        }
        if (!toPlace && toLocation) {
          toPlace = places?.find(
            (place) =>
              place.name.toLowerCase().includes(toLocation.toLowerCase()) ||
              toLocation.toLowerCase().includes(place.name.toLowerCase())
          );
        }
      }

      // Fill form data
      setFromLocation(fromLocation);
      setToLocation(toLocation);

      // Set departure date to tomorrow
      const tomorrow = addDays(new Date(), 1);
      const tomorrowStr = format(tomorrow, "yyyy-MM-dd");
      setDepartureDate(tomorrowStr);

      // Prepare search params for navigation
      const searchParams: any = {
        fromPlaceName: fromLocation,
        toPlaceName: toLocation,
        departureDate: tomorrowStr,
        numTickets: state.numTickets || 1,
        isRoundTrip: state.isRoundTrip,
        // Add focus parameter to indicate which date picker should be focused
        focusDate: state.isRoundTrip ? "return" : "departure",
      };

      // Add place IDs if we found them
      if (fromPlace) {
        searchParams.fromPlaceId = Number(fromPlace.id);
      }
      // Handle special case for Hà Nội - always use ID 24
      if (fromLocation === "Hà Nội") {
        searchParams.fromPlaceId = 24;
      }
      if (toPlace) {
        searchParams.toPlaceId = Number(toPlace.id);
      }

      // Include return date if round trip
      if (state.isRoundTrip && state.returnDate) {
        searchParams.returnDate = state.returnDate;
      }

      // Navigate immediately to search page
      navigate({ to: "/booking/search", search: searchParams });
    },
    [
      strapiRoutes,
      places,
      setFromLocation,
      setToLocation,
      setDepartureDate,
      state,
      navigate,
    ]
  );

  // Setup carousel API
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    onSelect();
    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api.off("select", onSelect);
      api.off("reInit", onSelect);
    };
  }, [api]);

  const scrollToPrevious = useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollToNext = useCallback(() => {
    api?.scrollNext();
  }, [api]);

  // Generate skeleton items for loading state
  const skeletonItems = Array.from({ length: 6 }, (_, index) => ({
    id: `skeleton-${index}`,
    title: "",
    price: "",
    image: "",
  }));

  const displayRoutes = isLoading ? skeletonItems : routes;

  return (
    <section className="w-full bg-white mt-10 sm:mt-20 md:mt-96 lg:mt-64">
      {/* Header */}
      <Container>
        <div className="flex items-center justify-between mb-6">
          <div className="flex flex-col gap-3">
            <h2 className="text-[#181818] font-mulish font-extrabold text-[20px] sm:text-[20px] md:text-[24px] lg:text-[32px] leading-[1.2]">
              Tuyến đường phổ biến
            </h2>
            {error && (
              <p className="text-sm text-red-600">
                Đang sử dụng dữ liệu mẫu do không thể tải dữ liệu từ server
              </p>
            )}
          </div>

          {/* Custom Navigation - Hidden on mobile */}
          <div className="hidden md:flex gap-2">
            <button
              onClick={scrollToPrevious}
              disabled={!canScrollPrev}
              className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 ${
                !canScrollPrev
                  ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                  : "bg-white text-[#181818] hover:bg-[#2D5BFF] hover:text-white border-[#EFEFEF] hover:border-[#2D5BFF]"
              }`}
              aria-label="Previous routes"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            <button
              onClick={scrollToNext}
              disabled={!canScrollNext}
              className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 shadow-[0px_0px_30px_0px_rgba(132,132,132,0.25)] ${
                !canScrollNext
                  ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                  : "bg-[#2D5BFF] text-white hover:bg-[#1e40af] border-[#2D5BFF]"
              }`}
              aria-label="Next routes"
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </Container>

      {/* Carousel */}
      <Container className="py-2 px-0 sm:px-20 lg:px-25 max-w-full lg:max-w-7xl">
        <Carousel
          setApi={setApi}
          className="w-full"
          opts={{
            align: "start",
            slidesToScroll: 1,
            dragFree: true,
          }}
        >
          <CarouselContent className="pl-4 sm:pl-0">
            {displayRoutes.map((route) => (
              <CarouselItem
                key={route.id}
                className="basis-[35%] sm:basis-1/2 md:basis-1/4 lg:basis-1/5 flex justify-center"
              >
                <div className="w-full flex justify-center">
                  <RouteCard
                    route={route}
                    isLoading={isLoading}
                    onClick={() => handleRouteClick(route)}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </Container>
    </section>
  );
};
