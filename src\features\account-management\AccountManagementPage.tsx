import React, { useEffect } from "react";
import { Sidebar } from "@/shared/components/Sidebar";
import type { SidebarItem } from "@/shared/components/Sidebar";
import { AccountInfoPanel } from "@/features/account/pages/AccountInfoPanel";
import { MyTicketsPanel } from "@/features/tickets/pages/MyTicketsPanel";
import { useSearch } from "@tanstack/react-router";
import { PasswordChangePanel } from "@/features/account/pages/PasswordChangePanel";
import { Route } from "@/routes/account-management";
import { useLogout, useAuthState } from "@/shared/hooks/useAuth";
import { Dialog, DialogContent } from "@/shared/components/dialog";

const SIDEBAR_ITEMS: SidebarItem[] = [
  { label: "Thông tin tài khoản", icon: "user", path: "account" },
  { label: "Quản lý vé", icon: "ticket", path: "tickets", badgeDot: true },
  { label: "<PERSON><PERSON><PERSON> mật khẩ<PERSON>", icon: "key", path: "password" },
];

const AccountManagementPage: React.FC = () => {
  const navigate = Route.useNavigate();
  const logoutMutation = useLogout();
  const [logoutDialogOpen, setLogoutDialogOpen] = React.useState(false);
  const search = useSearch({ strict: false });
  const { isAuthenticated, token, isLoading } = useAuthState();
  const tab =
    search.tab === "tickets"
      ? "tickets"
      : search.tab === "password"
        ? "password"
        : "account";
  const [active, setActive] = React.useState(tab);
  const [drawerOpen, setDrawerOpen] = React.useState(false);

  // Redirect to home if not authenticated or no token
  React.useEffect(() => {
    if (!isLoading && (!isAuthenticated || !token)) {
      navigate({ to: "/" });
    }
  }, [isAuthenticated, token, isLoading, navigate]);

  // Sync state <-> query string
  useEffect(() => {
    if (active !== tab) {
      navigate({
        search: { tab: active },
        replace: true,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [active]);

  useEffect(() => {
    if (tab !== active) setActive(tab);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tab]);

  const handleLogout = () => setLogoutDialogOpen(true);
  const confirmLogout = () => {
    setLogoutDialogOpen(false);
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        navigate({ to: "/" });
      },
    });
  };

  return (
    <div className="min-h-screen bg-[#F8F8F8] flex justify-center">
      <div className="flex w-full max-w-[1440px] gap-8 px-2 sm:px-4 md:px-6">
        {/* Sidebar for desktop */}
        <div className="hidden md:block flex-shrink-0 pt-8">
          <Sidebar
            items={SIDEBAR_ITEMS}
            activePath={active}
            onNavigate={setActive}
            onLogout={handleLogout}
          />
        </div>

        {/* Main content */}
        <div className="flex flex-1 justify-center w-full">
          <div
            className={`w-full bg-white rounded-2xl shadow p-2 sm:p-4 md:p-8 my-4 md:my-8 ${
              active === "password" || active === "account"
                ? "min-h-screen sm:max-h-[calc(100vh-300px)] sm:overflow-y-auto"
                : ""
            }`}
          >
            {active === "account" && <AccountInfoPanel />}
            {active === "tickets" && <MyTicketsPanel />}
            {active === "password" && <PasswordChangePanel />}
          </div>
        </div>
        {/* Sidebar Drawer for mobile */}
        <Dialog open={drawerOpen} onOpenChange={setDrawerOpen}>
          <DialogContent className="p-0 max-w-xs w-[90vw] h-full top-0 left-0 fixed rounded-none shadow-2xl md:hidden flex flex-col">
            <Sidebar
              items={SIDEBAR_ITEMS}
              activePath={active}
              onNavigate={(path) => {
                setActive(path);
                setDrawerOpen(false);
              }}
              onLogout={() => {
                setDrawerOpen(false);
                handleLogout();
              }}
              className="h-full rounded-none shadow-none border-0"
            />
          </DialogContent>
        </Dialog>
        {/* </Container> */}
      </div>
      {/* Logout Confirm Dialog */}
      <Dialog open={logoutDialogOpen} onOpenChange={setLogoutDialogOpen}>
        <DialogContent className="bg-white rounded-2xl shadow-[0px_0px_20px_0px_rgba(132,132,132,0.2)] p-10 max-w-[400px] w-full">
          <div className="text-[#181818] text-[18px] font-bold font-mulish mb-8 text-center leading-6">
            Bạn có chắc chắn muốn đăng xuất?
          </div>
          <div className="flex flex-col gap-4">
            <button
              className="bg-[#ff3030] text-white text-[16px] font-extrabold font-mulish rounded-lg py-3 tracking-wide hover:bg-[#e02b2b] transition"
              onClick={confirmLogout}
            >
              Xác nhận
            </button>
            <button
              className="bg-[#ecf0ff] text-[#2d5bff] text-[16px] font-extrabold font-mulish rounded-lg py-3 tracking-wide hover:bg-[#d6e0ff] transition"
              onClick={() => setLogoutDialogOpen(false)}
            >
              Huỷ
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export { AccountManagementPage };
