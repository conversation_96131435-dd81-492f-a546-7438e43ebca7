# Optimization Guide

This document outlines the optimization work performed on the SearchMobileHeader and SearchFormContext components, including the extraction of utility functions for better code reusability and maintainability.

## 🎯 Overview

The optimization focused on:

1. **Extracting reusable utility functions** into separate modules
2. **Improving code consistency** across components
3. **Auto-closing modal functionality** after successful form submission
4. **Better data synchronization** between SearchMobileHeader and SearchForm
5. **Enhanced performance** through memoization and proper dependency management

## 📁 New Utility Files Created

### `src/lib/dateUtils.ts`

A comprehensive collection of date utility functions with **zero external dependencies**:

#### **Core Functions:**

- `convertToISODate(dateStr: string): string` - Converts various date formats to ISO (YYYY-MM-DD)
- `formatDateForDisplay(dateStr: string): string` - Formats dates for display as dd/MM
- `formatDateDisplay(dateString: string): string` - Full date formatting with locale support
- `formatDateRange(departureDate: string, returnDate?: string): string` - Formats date ranges

#### **Validation Functions:**

- `isValidISODate(dateStr: string): boolean` - Validates ISO date format
- `isValidRoundTrip(departureDate: string, returnDate: string): boolean` - Validates round trip scenarios
- `isDateInPast(dateStr: string): boolean` - Checks if date is in the past

#### **Helper Functions:**

- `getTodayISO(): string` - Gets today's date in ISO format

#### **Supported Date Formats:**

- ✅ `dd/MM` (current year assumed)
- ✅ `dd/MM/yyyy`
- ✅ `yyyy/MM/dd`
- ✅ `YYYY-MM-DD` (ISO format)

### `src/lib/utils.ts` (Enhanced)

Added number and formatting utilities:

#### **New Functions:**

- `formatPrice(price: number): string` - Formats price with Vietnamese locale
- `formatNumber(num: number): string` - Formats numbers with Vietnamese locale
- `isPositiveNumber(num: number | string): boolean` - Validates positive numbers
- `safeNumber(value: string | number | undefined, fallback: number): number` - Safe number conversion with fallback

## 🔧 Component Optimizations

### SearchMobileHeader.tsx

#### **Before:**

- Inline date formatting functions
- Manual round trip validation logic
- No auto-close functionality
- Multiple useCallback hooks for simple operations

#### **After:**

- ✅ **Extracted utility functions** to `dateUtils.ts`
- ✅ **Auto-close modal** after successful form submission
- ✅ **Memoized date processing** for better performance
- ✅ **Consistent data formatting** using shared utilities
- ✅ **Improved accessibility** with proper aria-labels
- ✅ **Robust round trip detection** using utility functions

#### **Key Improvements:**

```typescript
// Before: Inline function
const convertToISODate = useCallback((dateStr: string): string => {
  // 40+ lines of date conversion logic
}, []);

// After: Imported utility
import {
  convertToISODate,
  formatDateRange,
  isValidRoundTrip,
} from "@/lib/dateUtils";

// Before: Manual date range formatting
const formatDateRange = useCallback(() => {
  // Complex logic for date range display
}, [processedDates]);

// After: Simple utility usage
const dateRangeDisplay = useMemo(() => {
  return formatDateRange(departureDate || "", returnDate);
}, [departureDate, returnDate]);
```

### SearchFormContext.tsx

#### **Before:**

- Manual number validation
- Inline date conversion logic
- No safety checks for number inputs

#### **After:**

- ✅ **Safe number conversion** using `safeNumber()` utility
- ✅ **Consistent date handling** using `dateUtils` functions
- ✅ **Enhanced validation** with utility functions
- ✅ **Better error handling** for edge cases

#### **Key Improvements:**

```typescript
// Before: Manual number handling
const setNumTickets = useCallback((value: number) => {
  setState((prev) => ({ ...prev, numTickets: value }));
}, []);

// After: Safe number conversion
const setNumTickets = useCallback((value: number) => {
  const safeValue = safeNumber(value, 1);
  setState((prev) => ({ ...prev, numTickets: safeValue }));
}, []);

// Before: Manual date conversion
const departureDate = new Date(value);

// After: Using utility function
const departureDate = new Date(convertToISODate(value));
```

## 🚀 Performance Improvements

### Memoization Optimizations

- **SearchMobileHeader**: Memoized date processing and form values
- **SearchFormContext**: Optimized place filtering with useMemo
- **Reduced re-renders** through proper dependency arrays

### Callback Optimizations

- **Extracted utility functions** eliminate the need for useCallback in many cases
- **Simplified dependency arrays** reduce memory usage
- **Better change detection** through memoization

## ✨ New Features

### Auto-Close Modal

```typescript
// SearchFormProvider now accepts onSubmitSuccess callback
<SearchFormProvider
  variant="search-page"
  initialValues={initialFormValues}
  onSubmitSuccess={handleSubmitSuccess} // ← New callback
>
```

### Enhanced Date Support

```typescript
// Now supports multiple date formats seamlessly
convertToISODate("29/05"); // → "2024-05-29"
convertToISODate("29/05/2024"); // → "2024-05-29"
convertToISODate("2024/05/29"); // → "2024-05-29"
convertToISODate("2024-05-29"); // → "2024-05-29"
```

### Robust Round Trip Detection

```typescript
// Intelligent round trip validation
isValidRoundTrip("29/05", "08/06"); // → true
isValidRoundTrip("29/05", "29/05"); // → false
isValidRoundTrip("", "08/06"); // → false
```

## 📋 Usage Guide

### Using Date Utils

```typescript
import {
  convertToISODate,
  formatDateForDisplay,
  formatDateRange,
  isValidRoundTrip,
} from "@/lib/dateUtils";

// Convert any date format to ISO
const isoDate = convertToISODate("29/05/2024");

// Format for display
const displayDate = formatDateForDisplay("2024-05-29"); // "29/5"

// Format date range
const range = formatDateRange("29/05", "08/06"); // "29/5 - 8/6"

// Validate round trip
const isRoundTrip = isValidRoundTrip(departureDate, returnDate);
```

### Using Number Utils

```typescript
import { formatPrice, safeNumber, isPositiveNumber } from "@/lib/utils";

// Format price
const formattedPrice = formatPrice(150000); // "150.000"

// Safe number conversion
const tickets = safeNumber(userInput, 1); // Fallback to 1 if invalid

// Validate positive number
const isValid = isPositiveNumber(tickets);
```

## 🧪 Testing

All utility functions are **pure functions** with no external dependencies, making them:

- ✅ **Easy to unit test**
- ✅ **Predictable behavior**
- ✅ **No side effects**
- ✅ **Mockable if needed**

Example test structure:

```typescript
describe("dateUtils", () => {
  describe("convertToISODate", () => {
    it("should convert dd/MM format to ISO", () => {
      expect(convertToISODate("29/05")).toBe("2024-05-29");
    });

    it("should handle dd/MM/yyyy format", () => {
      expect(convertToISODate("29/05/2024")).toBe("2024-05-29");
    });

    it("should return empty string for invalid input", () => {
      expect(convertToISODate("")).toBe("");
      expect(convertToISODate("invalid")).toBe("invalid");
    });
  });
});
```

## 🔄 Migration Benefits

### Code Reusability

- **Single source of truth** for date and number formatting
- **Consistent behavior** across all components
- **Easy to maintain** and update formatting logic

### Performance

- **Reduced bundle size** through shared utilities
- **Better tree shaking** with pure functions
- **Optimized re-renders** through memoization

### Developer Experience

- **IntelliSense support** for all utility functions
- **Type safety** with full TypeScript support
- **Clear documentation** and examples
- **Predictable behavior** across the application

## 📈 Impact Summary

### Before Optimization:

- ❌ Duplicate date formatting logic in multiple files
- ❌ Inconsistent date handling across components
- ❌ Manual modal management
- ❌ No input validation for edge cases
- ❌ Performance issues with unnecessary re-renders

### After Optimization:

- ✅ **Single source of truth** for date utilities
- ✅ **Consistent formatting** across the entire app
- ✅ **Auto-closing modal** for better UX
- ✅ **Robust input validation** with fallbacks
- ✅ **Optimized performance** through memoization
- ✅ **Better maintainability** and code organization
- ✅ **Enhanced type safety** and error handling

## 🎯 Next Steps

1. **Extend utility functions** as needed for other components
2. **Add comprehensive unit tests** for all utilities
3. **Create additional shared utilities** for common operations
4. **Document usage patterns** for other developers
5. **Monitor performance** improvements in production

---

This optimization establishes a **solid foundation** for scalable, maintainable code that follows **DRY principles** and **best practices** for React applications.
