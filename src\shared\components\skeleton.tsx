import React from "react";
import { cn } from "@/lib/utils";

interface SkeletonProps {
  className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({ className }) => {
  return (
    <div className={cn("animate-pulse rounded-md bg-gray-200", className)} />
  );
};

export const RestaurantInfoSkeleton: React.FC = () => {
  return (
    <div className="px-4 pt-5">
      {/* Restaurant Name */}
      <div className="mb-1">
        <Skeleton className="h-6 w-48 mb-2" />

        {/* Contact Info and Status */}
        <div className="flex items-center gap-2 mb-2">
          {/* Phone Icon */}
          <Skeleton className="w-4 h-4 rounded-full" />
          {/* Phone Number */}
          <Skeleton className="h-4 w-24" />

          {/* Dot separator */}
          <Skeleton className="w-[5px] h-[5px] rounded-full" />

          {/* Operating Status */}
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
    </div>
  );
};

export const MenuListSkeleton: React.FC = () => {
  return (
    <div className="bg-white">
      {/* Sticky Category Tabs Skeleton */}
      <div className="sticky top-0 z-30 bg-white py-2">
        <div className="px-4">
          <div className="flex gap-2">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-8 w-20 rounded-lg" />
            ))}
          </div>
        </div>
      </div>

      {/* Menu Items Skeleton */}
      <div className="px-4">
        {[1, 2].map((categoryIndex) => (
          <div key={categoryIndex} className="mb-6 pt-4">
            {/* Category Name */}
            <Skeleton className="h-5 w-32 mb-4" />

            {/* Menu Items */}
            <div className="space-y-5">
              {[1, 2, 3].map((itemIndex) => (
                <div key={itemIndex}>
                  <div className="flex items-center gap-4 py-0">
                    {/* Food Image */}
                    <Skeleton className="w-[88px] h-[88px] rounded-xl" />

                    <div className="flex-1 flex flex-col justify-between gap-2">
                      <div className="flex-1">
                        {/* Food Name */}
                        <Skeleton className="h-4 w-32 mb-1" />
                        {/* Food Description */}
                        <Skeleton className="h-3 w-48 mb-1" />
                        <Skeleton className="h-3 w-36" />
                      </div>

                      <div className="flex items-center justify-between">
                        {/* Price */}
                        <Skeleton className="h-4 w-16" />
                        {/* Add Button */}
                        <Skeleton className="w-8 h-8 rounded-lg" />
                      </div>
                    </div>
                  </div>

                  {/* Divider */}
                  {itemIndex < 2 && <div className="h-px bg-[#EDEDED] mt-5" />}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
