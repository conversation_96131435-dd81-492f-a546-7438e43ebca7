import { create } from "zustand";
import { persist } from "zustand/middleware";

interface ContactFormData {
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  acceptTerms: boolean;
  lastUpdated?: number; // Timestamp để kiểm tra form data có cũ không
}

interface ContactFormStore {
  formData: ContactFormData;
  setFormData: (data: Partial<ContactFormData>) => void;
  clearFormData: () => void;
  hasFormData: () => boolean;
  isFormDataStale: () => boolean; // Kiểm tra form data có cũ không (quá 1 giờ)
}

const initialFormData: ContactFormData = {
  customerName: "",
  customerPhone: "",
  customerEmail: "",
  acceptTerms: false,
  lastUpdated: undefined,
};

export const useContactFormStore = create<ContactFormStore>()(
  persist(
    (set, get) => ({
      formData: initialFormData,
      setFormData: (data) =>
        set((state) => ({
          formData: {
            ...state.formData,
            ...data,
            lastUpdated: Date.now(),
          },
        })),
      clearFormData: () => set({ formData: initialFormData }),
      hasFormData: () => {
        const { formData } = get();
        return !!(
          formData.customerName ||
          formData.customerPhone ||
          formData.customerEmail
        );
      },
      isFormDataStale: () => {
        const { formData } = get();
        if (!formData.lastUpdated) return true;

        // Form data được coi là cũ nếu quá 1 giờ
        const oneHour = 60 * 60 * 1000; // 1 giờ tính bằng milliseconds
        return Date.now() - formData.lastUpdated > oneHour;
      },
    }),
    {
      name: "contact-form-storage",
    }
  )
);
