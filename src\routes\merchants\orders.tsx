import React from "react";
import { OrdersList } from "@/features/merchants/components";
import { createFileRoute } from "@tanstack/react-router";
import { useNavigate } from "@tanstack/react-router";
import { ChevronLeftIcon } from "@heroicons/react/24/outline";

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { qr } = Route.useSearch();

  return (
    <div className="max-w-xl mx-auto pt-4 pb-20 min-h-screen bg-gray-50">
      <div className="flex items-center mb-4 px-4">
        <button
          className="p-1 mr-2 text-blue-600 hover:text-blue-800"
          onClick={() =>
            navigate({ to: "/merchants", search: { qr: qr ?? "" } })
          }
          aria-label="Quay lại"
        >
          <ChevronLeftIcon className="h-6 w-6" />
        </button>
        <h1 className="text-xl font-bold">Đ<PERSON><PERSON> hàng của bạn</h1>
      </div>
      <OrdersList />
    </div>
  );
};

export default OrdersPage;

export const Route = createFileRoute("/merchants/orders")({
  component: OrdersPage,
  validateSearch: (search: Record<string, unknown>) => ({
    qr: typeof search.qr === "string" ? search.qr : undefined,
  }),
});
