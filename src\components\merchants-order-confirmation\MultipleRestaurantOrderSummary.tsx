import React from "react";
import { OrderItemCard } from "./OrderItemCard";
import { formatPrice } from "@/features/merchants/utils";
import type { CartItem } from "@/features/merchants/types";

interface MultipleRestaurantOrderSummaryProps {
  items: CartItem[];
  disabled?: boolean;
  merchantName: string;
}

export const MultipleRestaurantOrderSummary: React.FC<
  MultipleRestaurantOrderSummaryProps
> = ({ items, disabled = false, merchantName }) => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  return (
    <div className="space-y-2.5">
      {/* Danh sách món */}
      <div className="bg-white rounded-xl border border-[#EDEDED] p-3">
        <h2 className="text-base font-bold text-[#181818] mb-4">
          {merchantName}
        </h2>
        <div className="space-y-2">
          {items.map((item, index) => (
            <div key={item.id}>
              <OrderItemCard item={item} disabled={disabled} />
              {index < items.length - 1 && (
                <div className="h-px bg-[#EDEDED] border-dashed my-2" />
              )}
            </div>
          ))}
        </div>
      </div>
      {/* Tổng kết */}
      <div className="bg-white rounded-xl border border-[#EDEDED] p-3">
        <h2 className="text-base font-bold text-[#181818] mb-4">
          Tổng đơn hàng
        </h2>
        <div className="bg-[#F8F8F8] rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-[#747474]">
              Tổng số món
            </span>
            <span className="text-sm font-normal text-[#181818]">
              {totalItems}
            </span>
          </div>
          <div className="h-px bg-[#EDEDED] my-2" />
          <div className="flex items-center justify-between">
            <span className="text-base font-bold text-[#181818]">
              Tổng cộng
            </span>
            <span className="text-base font-bold text-[#2D5BFF]">
              {formatPrice(totalPrice)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
