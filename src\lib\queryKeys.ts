// Query keys for TanStack Query
// Following the recommended pattern: [feature, ...params]

export const queryKeys = {
  // Places queries
  places: {
    all: ["places"] as const,
    lists: () => [...queryKeys.places.all, "list"] as const,
    list: (filters?: Record<string, any>) =>
      [...queryKeys.places.lists(), filters] as const,
    details: () => [...queryKeys.places.all, "detail"] as const,
    detail: (id: number) => [...queryKeys.places.details(), id] as const,
    search: (term: string) =>
      [...queryKeys.places.all, "search", term] as const,
    byLevel: (level: number) =>
      [...queryKeys.places.all, "level", level] as const,
    byParentId: (parentId: number) =>
      [...queryKeys.places.all, "parent", parentId] as const,
  },

  // Bus Schedules queries
  busSchedules: {
    all: ["busSchedules"] as const,
    lists: (busId?: string) =>
      [...queryKeys.busSchedules.all, "list", busId] as const,
    details: () => [...queryKeys.busSchedules.all, "detail"] as const,
    detail: (id: string) => [...queryKeys.busSchedules.details(), id] as const,
    search: (params: Record<string, any>) =>
      [...queryKeys.busSchedules.all, "search", params] as const,
  },

  // Add more feature query keys here as needed
  // bookings: {
  //   all: ['bookings'] as const,
  //   lists: () => [...queryKeys.bookings.all, 'list'] as const,
  //   // ... etc
  // },
} as const;
