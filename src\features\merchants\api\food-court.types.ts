// Food Court Menu Types
export interface ImageSizes {
  large: string;
  medium: string;
  original: string;
  small: string;
  thumbnail: string;
}

export interface FoodItemImage {
  id: string;
  originalName: string;
  sizes: ImageSizes;
  uploadedAt: string;
}

export interface FoodCourtImage {
  [key: string]: any; // Based on the empty object {} in the response
}

export interface FoodCourtMerchant {
  id: string;
  name: string;
  description: string;
  cover_image: FoodCourtImage;
  logo_image: FoodCourtImage;
  contact_phone: string;
  operating_hours_start: string;
  operating_hours_end: string;
}

export interface FoodItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  images?: FoodItemImage[];
  preparation_time_minutes: number;
}

export interface MenuCategory {
  id: string;
  name: string;
  description: string;
  icon_url: string;
  color_code: string;
  food_items: FoodItem[];
}

export interface FoodCourtMenuResponse {
  merchant: FoodCourtMerchant;
  categories: MenuCategory[];
}

// API Request Parameters
export interface GetMerchantMenuParams {
  merchantId: string;
}

// Error Response
export interface FoodCourtApiError {
  message: string;
  code?: string;
  details?: any;
}
