import { useState, useCallback } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useCreateOrder } from "@/features/merchants/hooks/useCreateOrder";
import {
  DeliveryTypeEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";
import type {
  CustomerInfo,
  DeliveryOption,
} from "@/shared/types/order-confirmation.types";
import type { CreateOrderRequest } from "@/api/merchants/orders.types";
import { simpleCartSelectors } from "@/stores/simpleCartStore";

export const useMultipleRestaurantOrderConfirmation = () => {
  const navigate = useNavigate();

  // Create order mutation
  const createOrderMutation = useCreateOrder();

  // Local state
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: "",
    phone: "",
  });
  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption>(
    DeliveryTypeEnum.TABLE_DELIVERY
  );
  const [note, setNote] = useState("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  // Navigation handler
  const handleBackToMenu = useCallback(() => {
    navigate({ to: "/merchants" });
  }, [navigate]);

  // Form handlers
  const handleCustomerInfoChange = useCallback((info: CustomerInfo) => {
    setCustomerInfo(info);
  }, []);

  const handleDeliveryOptionChange = useCallback((option: DeliveryOption) => {
    setDeliveryOption(option);
  }, []);

  const handleNoteChange = useCallback((value: string) => {
    setNote(value);
  }, []);

  // Prepare order data
  const prepareOrderData = useCallback((): CreateOrderRequest => {
    return {
      food_court_table_id: "QR-T001-XYZ222123", // TODO: Get actual table ID
      delivery_type: deliveryOption,
      special_instructions: note || undefined,
      payment_method: PaymentTypeEnum.CARD,
      items: [], // TODO: Implement items logic
    };
  }, [deliveryOption, note]);

  // Place order handler
  const handlePlaceOrder = useCallback(async () => {
    if (!createOrderMutation.isPending) return;

    try {
      const orderData = prepareOrderData();

      // Tạo 1 order duy nhất cho tất cả items từ nhiều nhà hàng
      await createOrderMutation.mutateAsync(orderData);

      // Show success popup
      setShowSuccessPopup(true);

      // Wait 3 seconds then redirect
      setTimeout(() => {
        setShowSuccessPopup(false);
        navigate({ to: "/merchants" });
      }, 3000);
    } catch (error) {
      console.error("Failed to place multiple orders:", error);
      // Error handling is done by the mutation's onError callback
    }
  }, [createOrderMutation, prepareOrderData, navigate]);

  // Computed values
  const isProcessing = createOrderMutation.isPending;

  return {
    // Data
    customerInfo,
    deliveryOption,
    note,
    showSuccessPopup,
    createOrderMutation,
    isProcessing,

    // Handlers
    handleBackToMenu,
    handleCustomerInfoChange,
    handleDeliveryOptionChange,
    handleNoteChange,
    handlePlaceOrder,

    // Computed values
    totalPrice: simpleCartSelectors
      .getAllItems()
      .reduce((sum, item) => sum + item.price * item.quantity, 0),
  };
};
