import React, { useState, useCallback, useEffect } from "react";
import { clsx } from "clsx";
// import { Link } from "@tanstack/react-router";
import type { Bus, RouteInfo, SearchParams } from "@/shared/types/bus";
import BusCard from "@/features/booking/components/BusCard";
import { useSearchForm } from "../../contexts/SearchFormContext";
import { useSelectedTripsStore } from "../../stores/selectedTripsStore";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { useTranslation } from "@/shared/hooks/useTranslation";

// Car Icon Component using the provided SVG
const CarIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.99278 3.47945C4.89458 3.47945 4.8048 3.53222 4.76089 3.61576L3.19057 6.60274H13.8094L12.2391 3.61576C12.1952 3.53222 12.1054 3.47945 12.0072 3.47945H4.99278ZM15.5 7.34247V6.91777C15.5 6.64981 15.4344 6.38553 15.3084 6.14586L13.6304 2.95412C13.323 2.36937 12.6946 2 12.0072 2H4.99278C4.30537 2 3.67697 2.36937 3.36956 2.95412L1.6916 6.14586C1.5656 6.38553 1.5 6.64981 1.5 6.91777V7.34247V11.9452V12.9315C1.5 13.5216 2.00299 14 2.62346 14C3.24393 14 3.74691 13.5216 3.74691 12.9315V12.6849H12.5642C12.609 13.4184 13.2485 14 14.0309 14C14.8422 14 15.5 13.3744 15.5 12.6027V11.9452V7.34247ZM13.9444 8.08219H3.05556V11.2055H13.3395H13.9444V8.08219ZM5.73457 9.64384C5.73457 10.1886 5.27027 10.6301 4.69753 10.6301C4.12479 10.6301 3.66049 10.1886 3.66049 9.64384C3.66049 9.09912 4.12479 8.65753 4.69753 8.65753C5.27027 8.65753 5.73457 9.09912 5.73457 9.64384ZM12.3025 10.6301C12.8752 10.6301 13.3395 10.1886 13.3395 9.64384C13.3395 9.09912 12.8752 8.65753 12.3025 8.65753C11.7297 8.65753 11.2654 9.09912 11.2654 9.64384C11.2654 10.1886 11.7297 10.6301 12.3025 10.6301Z"
      fill="currentColor"
    />
  </svg>
);

interface SearchResultsProps {
  departureBuses: Bus[];
  returnBuses: Bus[];
  isDepartureLoading: boolean;
  isReturnLoading: boolean;
  route: RouteInfo;
  searchParams?: SearchParams & {
    focusDate?: "departure" | "return";
  };
}

const SearchResults: React.FC<SearchResultsProps> = ({
  departureBuses,
  returnBuses,
  isDepartureLoading,
  isReturnLoading,
  route,
  searchParams,
}) => {
  const { t } = useTranslation();
  const { state } = useSearchForm();
  const [activeTab, setActiveTab] = useState<"departure" | "return">(
    searchParams?.focusDate || "departure"
  );
  const [selectedBusId, setSelectedBusId] = useState<string | null>(null);

  // Auto-focus tab based on search params
  useEffect(() => {
    if (searchParams?.focusDate) {
      setActiveTab(searchParams.focusDate);
    }
  }, [searchParams?.focusDate]);

  const handleTabChange = useCallback((tab: "departure" | "return") => {
    setActiveTab(tab);
  }, []);

  const handleBusSelect = useCallback((busId: string) => {
    setSelectedBusId(busId);
  }, []);

  // Only show return tab if there's a return date
  const showReturnTab = state.isRoundTrip && state.returnDate;

  const handleOutboundSelected = useCallback(() => {
    // Tự động chuyển sang tab return khi đã chọn outbound trong round trip
    if (showReturnTab) {
      setActiveTab("return");
    }
  }, [showReturnTab]);

  // Format dates for display in tabs
  const formatDateForTab = (dateString: string) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      // Format as "Thứ 5, 05/06"
      const dateOnly = format(date, "EEEE, dd/MM", { locale: vi });

      // Capitalize first letter and format
      return dateOnly;
    } catch {
      return dateString;
    }
  };

  // Choose the appropriate buses and loading state based on active tab
  const currentBuses = activeTab === "departure" ? departureBuses : returnBuses;
  const isCurrentlyLoading =
    activeTab === "departure" ? isDepartureLoading : isReturnLoading;

  if (isCurrentlyLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-xl text-blue-600">{t("common.searchingBuses")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 lg:space-y-6">
      {/* Route Header - Mobile optimized */}
      <div className="mb-4 lg:mb-6 px-4 lg:px-0">
        <h1 className="text-lg lg:text-[20px] font-mulish font-extrabold leading-[1.4] text-[#FF7F37]">
          {route.from} - {route.to} ({route.totalBuses} {t("search.busesCount")}
          )
        </h1>
      </div>

      {/* Trip Tabs - Mobile optimized */}
      <div className="bg-white rounded-lg p-1 flex gap-2 mx-4 lg:mx-0">
        <button
          onClick={() => handleTabChange("departure")}
          className={clsx(
            "flex items-center justify-center gap-2 lg:gap-3 px-3 lg:px-5 py-2 rounded-lg font-bold text-sm lg:text-base transition-all",
            showReturnTab ? "flex-1" : "w-full",
            activeTab === "departure"
              ? "bg-[#2D5BFF] text-white"
              : "text-[#5C5C5C] hover:bg-gray-50"
          )}
        >
          <CarIcon
            className={clsx(
              "w-4 h-4 hidden lg:flex",
              activeTab === "departure" ? "text-white" : "text-[#7C7B7B]"
            )}
          />
          <span className="truncate">
            {/* Mobile: Show with date only when active */}
            <span className="lg:hidden">
              {activeTab === "departure" && state.departureDate
                ? `Chuyến đi - ${formatDateForTab(state.departureDate)}`
                : "Chuyến đi"}
            </span>
            {/* Desktop: Show with date only when active */}
            <span className="hidden lg:inline">
              {activeTab === "departure" && state.departureDate
                ? `${t("search.departureTrip")} - ${formatDateForTab(state.departureDate)}`
                : t("search.departureTrip")}
            </span>
          </span>
        </button>

        {showReturnTab && (
          <button
            onClick={() => handleTabChange("return")}
            className={clsx(
              "flex-1 flex items-center justify-center gap-2 lg:gap-3 px-3 lg:px-5 py-2 rounded-lg font-bold text-sm lg:text-base transition-all",
              activeTab === "return"
                ? "bg-[#FF7F37] text-white"
                : "text-[#5C5C5C] hover:bg-gray-50"
            )}
          >
            <CarIcon
              className={clsx(
                "w-4 h-4 scale-x-[-1] hidden lg:flex",
                activeTab === "return" ? "text-white" : "text-[#7C7B7B]"
              )}
            />
            <span className="truncate">
              {/* Mobile: Show with date only when active */}
              <span className="lg:hidden">
                {activeTab === "return" && state.returnDate
                  ? `Chuyến về - ${formatDateForTab(state.returnDate)}`
                  : "Chuyến về"}
              </span>
              {/* Desktop: Show with date only when active */}
              <span className="hidden lg:inline">
                {activeTab === "return" && state.returnDate
                  ? `${t("search.returnTrip")} - ${formatDateForTab(state.returnDate)}`
                  : t("search.returnTrip")}
              </span>
            </span>
          </button>
        )}
      </div>

      {/* Bus List - Mobile optimized spacing */}
      {currentBuses.length > 0 ? (
        <div className="space-y-3 lg:space-y-4 px-4 lg:px-0">
          {currentBuses.map((bus) => (
            <BusCard
              key={bus.id}
              bus={bus}
              dropoffs={bus.dropoffs}
              isHighlighted={selectedBusId === bus.id}
              onSelect={handleBusSelect}
              searchParams={searchParams}
              tripType={activeTab === "departure" ? "outbound" : "return"}
              onOutboundSelected={handleOutboundSelected}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-10 bg-yellow-50 rounded-lg mx-4 lg:mx-0">
          <p className="text-lg text-yellow-700">{t("bus.noTripsFound")}</p>
        </div>
      )}

      {/* Selected Trips Bar */}
      <SelectedTripsBar />
    </div>
  );
};

// Selected Trips Bar Component - Mobile optimized
const SelectedTripsBar = () => {
  const {
    outboundTrip,
    returnTrip,
    // getTotalPrice,
    // getSelectedTripsCount,
    isReadyForCheckout,
    setReturnTrip,
  } = useSelectedTripsStore();
  const { state } = useSearchForm(); // Get current search form state

  // Clear return trip if user switched from round trip to one-way
  React.useEffect(() => {
    if (!state.isRoundTrip && returnTrip) {
      setReturnTrip(null);
    }
  }, [state.isRoundTrip, returnTrip, setReturnTrip]);

  if (!outboundTrip && !returnTrip) {
    return null;
  }

  // Only show return trip if current search form state is round trip
  const shouldShowReturnTrip = state.isRoundTrip && returnTrip;

  // Calculate price based on current search form state
  // const basePrice = state.isRoundTrip
  //   ? getTotalPrice()
  //   : outboundTrip?.price || 0;

  // Multiply by number of tickets to get actual total price
  // const displayPrice = basePrice * state.numTickets;

  // Calculate count based on current search form state
  // const displayCount = state.isRoundTrip
  //   ? getSelectedTripsCount()
  //   : outboundTrip
  //     ? 1
  //     : 0;

  // Prepare checkout search params based on current state
  // const checkoutSearchParams = {
  //   scheduleId: outboundTrip?.id || "",
  //   price: displayPrice,
  //   numTickets: state.numTickets,
  //   fromPlaceId: searchParams?.fromPlaceId,
  //   toPlaceId: searchParams?.toPlaceId,
  //   fromPlaceName: outboundTrip?.departureLocation || "",
  //   toPlaceName: outboundTrip?.arrivalLocation || "",
  //   departureDate: outboundTrip?.departureDate || "",
  //   returnDate: state.returnDate,
  //   isRoundTrip: state.isRoundTrip,
  //   busNumber: outboundTrip?.busNumber || "",
  //   departureTime: outboundTrip?.departureTime || "",
  //   arrivalTime: outboundTrip?.arrivalTime || "",
  //   dropoffId: outboundTrip?.dropoffId || "",
  // };

  // Check if ready for checkout based on current search state
  const readyForCheckout = state.isRoundTrip
    ? isReadyForCheckout()
    : !!outboundTrip;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="max-w-7xl mx-auto px-4 lg:px-10 py-3 lg:py-4">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-3 lg:gap-6 flex-1 min-w-0">
            {/* Outbound Trip */}
            {/* {outboundTrip && (
              <div className="flex items-center gap-2 lg:gap-3 min-w-0">
                <div className="w-3 h-3 bg-booking-blue rounded-full flex-shrink-0"></div>
                <div className="min-w-0">
                  <p className="text-xs lg:text-sm font-bold text-booking-text truncate">
                    Chiều đi
                  </p>
                  <p className="text-xs text-booking-muted truncate">
                    {outboundTrip.companyName} • {outboundTrip.departureTime}
                  </p>
                </div>
              </div>
            )} */}

            {/* Return Trip - Only show if current search is round trip */}
            {shouldShowReturnTrip && (
              <div className="flex items-center gap-2 lg:gap-3 min-w-0">
                <div className="w-3 h-3 bg-booking-orange rounded-full flex-shrink-0"></div>
                <div className="min-w-0">
                  <p className="text-xs lg:text-sm font-bold text-booking-text truncate">
                    Chiều về
                  </p>
                  <p className="text-xs text-booking-muted truncate">
                    {returnTrip.companyName} • {returnTrip.departureTime}
                  </p>
                </div>
              </div>
            )}

            {/* Round trip missing selection message - Only for current round trip searches */}
            {state.isRoundTrip && !readyForCheckout && (
              <div className="hidden lg:block lg:ml-6 min-w-0">
                <p className="text-sm text-amber-600 font-medium truncate">
                  {!outboundTrip
                    ? "Vui lòng chọn chuyến đi"
                    : "Vui lòng chọn chuyến về"}
                </p>
              </div>
            )}

            {/* Total Price */}
            {/* <div className="ml-auto lg:ml-6 text-right flex-shrink-0">
              <p className="text-xs lg:text-sm text-booking-muted">Tổng tiền</p>
              <p className="text-sm lg:text-lg font-bold text-booking-orange">
                {displayPrice.toLocaleString("vi-VN")}đ
              </p>
            </div> */}
          </div>

          {/* Checkout Button */}
          {/* {readyForCheckout ? (
            <Link
              to="/booking/checkout"
              search={checkoutSearchParams}
              className={clsx(
                "px-4 lg:px-8 py-2 lg:py-3 rounded-lg font-bold text-sm lg:text-lg transition-all flex-shrink-0",
                "bg-blue-50 text-blue-600 hover:bg-[#2D5BFF] hover:text-white"
              )}
            >
              <span className="lg:hidden">Thanh toán</span>
              <span className="hidden lg:inline">
                Thanh toán ({displayCount} chuyến)
              </span>
            </Link>
          ) : (
            <button
              disabled
              className={clsx(
                "px-4 lg:px-8 py-2 lg:py-3 rounded-lg font-bold text-sm lg:text-lg transition-all flex-shrink-0",
                "bg-gray-100 text-gray-400 cursor-not-allowed"
              )}
            >
              <span className="lg:hidden">Thanh toán</span>
              <span className="hidden lg:inline">
                Thanh toán ({displayCount} chuyến)
              </span>
            </button>
          )} */}
        </div>
      </div>
    </div>
  );
};

export default SearchResults;
