import {
  AUTH_SET_PASSWORD_API_ENDPOINT,
  AUTH_SIGNIN_API_ENDPOINT,
  AUTH_SIGNUP_API_ENDPOINT,
  AUTH_VERIFY_OTP_API_ENDPOINT,
  AUTH_PROFILE_API_ENDPOINT,
  AUTH_ADMIN_TEST_API_ENDPOINT,
  AUTH_FORGOT_PASSWORD_API_ENDPOINT,
  AUTH_FORGOT_PASSWORD_VERIFY_OTP_API_ENDPOINT,
  AUTH_RESET_PASSWORD_API_ENDPOINT,
  AUTH_UPDATE_PASSWORD_API_ENDPOINT,
  AUTH_UPDATE_AVATAR_API_ENDPOINT,
  AUTH_GET_AVATAR_API_ENDPOINT,
  AUTH_DELETE_AVATAR_API_ENDPOINT,
  AUTH_UPDATE_PROFILE_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";
import type {
  ForgotPasswordVerifyOtpResponse,
  ForgotPasswordVerifyOtpRequest,
  UserProfile,
  ResetPasswordRequest,
  ResetPasswordResponse,
  SigninRequest,
  SigninResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  SetPasswordRequest,
  SetPasswordResponse,
  SignupResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  SignupRequest,
} from "@/shared/types/auth";
import axiosInstance from "@/shared/api/axiosInstance";

// Sign up with phone number
export const signup = async (data: SignupRequest): Promise<SignupResponse> => {
  const response = await axiosInstance.post(AUTH_SIGNUP_API_ENDPOINT, data);
  return response.data;
};

// Verify OTP code
export const verifyOtp = async (
  data: VerifyOtpRequest
): Promise<VerifyOtpResponse> => {
  const response = await axiosInstance.post(AUTH_VERIFY_OTP_API_ENDPOINT, data);
  return response.data;
};

// Set password after OTP verification
export const setPassword = async (
  data: SetPasswordRequest
): Promise<SetPasswordResponse> => {
  const response = await axiosInstance.post(
    AUTH_SET_PASSWORD_API_ENDPOINT,
    data
  );
  return response.data;
};

// Sign in with password
export const signin = async (data: SigninRequest): Promise<SigninResponse> => {
  const response = await axiosInstance.post(AUTH_SIGNIN_API_ENDPOINT, data);
  return response.data;
};

// Get user profile (requires authentication)
export const getProfile = async (): Promise<UserProfile> => {
  const response = await axiosInstance.get(AUTH_PROFILE_API_ENDPOINT);
  return response.data;
};

// Admin test endpoint
export const adminTest = async (): Promise<{ message: string }> => {
  const response = await axiosInstance.get(AUTH_ADMIN_TEST_API_ENDPOINT);
  return response.data;
};

// Forgot password - send OTP
export const forgotPassword = async (
  data: ForgotPasswordRequest
): Promise<ForgotPasswordResponse> => {
  const response = await axiosInstance.post(
    AUTH_FORGOT_PASSWORD_API_ENDPOINT,
    data
  );
  return response.data;
};

// Forgot password - verify OTP
export const forgotPasswordVerifyOtp = async (
  data: ForgotPasswordVerifyOtpRequest
): Promise<ForgotPasswordVerifyOtpResponse> => {
  const response = await axiosInstance.post(
    AUTH_FORGOT_PASSWORD_VERIFY_OTP_API_ENDPOINT,
    data
  );
  return response.data;
};

// Reset password
export const resetPassword = async (
  data: ResetPasswordRequest,
  accessToken?: string
): Promise<ResetPasswordResponse> => {
  const config = accessToken
    ? {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    : {};

  const response = await axiosInstance.post(
    AUTH_RESET_PASSWORD_API_ENDPOINT,
    data,
    config
  );
  return response.data;
};

// Helper function to set authorization header
export const setAuthToken = (token: string) => {
  // Store token in localStorage
  localStorage.setItem("access_token", token);
  // Set token in axios instance headers
  axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
};

// Helper function to remove authorization header
export const clearAuthToken = () => {
  // Remove token from localStorage
  localStorage.removeItem("access_token");
  // Remove token from axios instance headers
  delete axiosInstance.defaults.headers.common["Authorization"];
};

// Update password
export const updatePassword = async (data: {
  current_password: string;
  new_password: string;
}) => {
  const response = await axiosInstance.post(
    AUTH_UPDATE_PASSWORD_API_ENDPOINT,
    data
  );
  return response.data;
};

// Update avatar (multipart/form-data)
export const updateAvatar = async (formData: FormData) => {
  const response = await axiosInstance.post(
    AUTH_UPDATE_AVATAR_API_ENDPOINT,
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );
  return response.data;
};

// Get avatar
export const getAvatar = async () => {
  const response = await axiosInstance.get(AUTH_GET_AVATAR_API_ENDPOINT);
  return response.data;
};

// Delete avatar
export const deleteAvatar = async () => {
  const response = await axiosInstance.delete(AUTH_DELETE_AVATAR_API_ENDPOINT);
  return response.data;
};

// Update profile/metadata (PUT, body là object bất kỳ)
export const updateProfile = async (data: any) => {
  const response = await axiosInstance.put(
    AUTH_UPDATE_PROFILE_API_ENDPOINT,
    data
  );
  return response.data;
};
