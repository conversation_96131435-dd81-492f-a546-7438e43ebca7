import React from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";

interface BusAmenitiesProps {
  amenities: string[];
}

const BusAmenities: React.FC<BusAmenitiesProps> = ({ amenities }) => {
  const { t } = useTranslation();

  const defaultAmenities = [
    t("bus.amenities.schedule"),
    t("bus.amenities.transfer"),
    t("bus.amenities.policy"),
  ];

  const displayAmenities = amenities.length > 0 ? amenities : defaultAmenities;

  return (
    <div className="flex flex-wrap gap-2">
      {displayAmenities.map((amenity, index) => (
        <span
          key={index}
          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-md"
        >
          {amenity}
        </span>
      ))}
    </div>
  );
};

export default BusAmenities;
