import type { CustomerInfo } from "@/shared/types/order-confirmation.types";
import React from "react";

interface CustomerInfoSectionProps {
  customerInfo: CustomerInfo;
  onCustomerInfoChange: (info: CustomerInfo) => void;
  disabled?: boolean;
}

export const CustomerInfoSection: React.FC<CustomerInfoSectionProps> = ({
  customerInfo,
  onCustomerInfoChange,
  disabled = false,
}) => {
  const handleNameChange = (name: string) => {
    onCustomerInfoChange({ ...customerInfo, name });
  };

  const handlePhoneChange = (phone: string) => {
    onCustomerInfoChange({ ...customerInfo, phone });
  };

  return (
    <div className="bg-white rounded-xl border border-[#EDEDED] p-3">
      <h2 className="text-base font-bold text-[#181818] mb-4">Người đặt</h2>

      <div className="h-px bg-[#EDEDED] mb-4" />

      <div className="space-y-4">
        <div className="bg-[#F8F8F8] rounded-lg p-4">
          <input
            type="text"
            value={customerInfo.name}
            onChange={(e) => handleNameChange(e.target.value)}
            className="w-full bg-transparent text-sm font-normal text-[#5C5C5C] outline-none"
            placeholder="Tên người đặt"
            disabled={disabled}
          />
        </div>

        <div className="bg-[#F8F8F8] rounded-lg p-4">
          <input
            type="text"
            value={customerInfo.phone}
            onChange={(e) => handlePhoneChange(e.target.value)}
            className="w-full bg-transparent text-sm font-normal text-[#5C5C5C] outline-none"
            placeholder="Số điện thoại"
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};
