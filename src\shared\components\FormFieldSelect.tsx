import React, { useState } from "react";
import { cn } from "@/lib/utils";

interface Option {
  label: string;
  value: string;
}

interface FormFieldSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const FormFieldSelect: React.FC<FormFieldSelectProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder = "Chọn...",
  className = "",
  disabled = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <div
      className={cn(
        "bg-[#f8f8f8] rounded-lg px-4 py-1.5 flex items-center gap-2 transition-all",
        isFocused && "bg-[#f0f0f0] ring-2 ring-blue-500 ring-opacity-50",
        disabled && "opacity-50",
        className
      )}
      style={{
        outline: "none !important",
        border: "none !important",
        boxShadow: isFocused ? "0 0 0 2px rgba(59, 130, 246, 0.5)" : "none",
      }}
    >
      <div className="flex flex-col w-full">
        <label className="block text-xs font-bold text-[#5c5c5c] mb-0.5 font-mulish">
          {label}
        </label>
        <div className="relative w-full flex items-center">
          <select
            className="appearance-none w-full bg-transparent text-[#181818] text-[14px] font-mulish font-normal leading-[18px] pr-8 cursor-pointer disabled:cursor-not-allowed"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            disabled={disabled}
            style={
              {
                WebkitAppearance: "none",
                MozAppearance: "none",
                outline: "none !important",
                border: "none !important",
                boxShadow: "none !important",
              } as React.CSSProperties & { WebkitFocusRingColor?: string }
            }
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((opt) => (
              <option key={opt.value} value={opt.value}>
                {opt.label}
              </option>
            ))}
          </select>
          {/* Custom dropdown icon */}
          <svg
            className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 pointer-events-none"
            fill="none"
            viewBox="0 0 14 6"
          >
            <path
              d="M13.8035 0.232536C13.5182 -0.0534395 13.0265 -0.0791986 12.7055 0.175002L7.17224 4.55607C7.07401 4.63385 6.92599 4.63384 6.82776 4.55607L1.29451 0.175002C0.973459 -0.0791986 0.481848 -0.0534395 0.196467 0.232536C-0.0889128 0.518512 -0.0599946 0.956411 0.261059 1.21061L5.7943 5.59168C6.48191 6.13611 7.51809 6.13611 8.2057 5.59168L13.7389 1.21061C14.06 0.956411 14.0889 0.518512 13.8035 0.232536Z"
              fill="#2D5BFF"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default FormFieldSelect;
