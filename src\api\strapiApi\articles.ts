import { axiosInstanceStrapi } from "@/shared/api/axiosInstance";
import type { StrapiArticlesResponse, StrapiArticle } from "@/shared/types/strapi";

/**
 * Fetch articles from Strapi CMS
 */
export const getArticles = async (): Promise<StrapiArticlesResponse> => {
  const response = await axiosInstanceStrapi.get("articles");
  return response.data;
};

/**
 * Fetch a single article by ID from Strapi CMS
 */
export const getArticleById = async (id: string): Promise<StrapiArticle> => {
  const response = await axiosInstanceStrapi.get(`articles/${id}`);
  return response.data;
};
