import React from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { formatPrice } from "@/shared/utils/formatPrice";
import { OrderStatusBadge } from "./OrderStatusBadge";
import type { Order } from "@/api/orders";

interface MobileTicketCardProps {
  order: Order;
  onTicketClick: (order: Order) => void;
}

export const MobileTicketCard: React.FC<MobileTicketCardProps> = ({
  order,
  onTicketClick,
}) => {
  const ticket = order.tickets[0];
  const ticketCount = order.tickets.length;

  const formatDepartureDateTime = () => {
    if (!ticket) return "-";
    const dateStr = `${ticket.departure_date}T${ticket.departure_time}`;
    const dateObj = new Date(dateStr);
    return isNaN(dateObj.getTime())
      ? "-"
      : format(dateObj, "dd/MM/yyyy", { locale: vi });
  };

  return (
    <div className="bg-white relative rounded-lg border border-[#ededed] p-3">
      <div className="flex flex-col gap-2.5">
        {/* First row: Ticket code and quantity */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1.5">
            <span className="text-[#5c5c5c] text-[16px] font-normal leading-[20px]">
              Mã vé
            </span>
            <button
              className="text-[#2d5bff] text-[16px] font-bold leading-[20px] hover:underline focus:outline-none"
              onClick={() => onTicketClick(order)}
              type="button"
            >
              {ticket?.ticket_number || "-"}
            </button>
          </div>
          <div className="text-[#5c5c5c] text-[16px] font-normal leading-[20px] text-right">
            x {ticketCount}
          </div>
        </div>

        {/* Second row: Route and date */}
        <div className="flex justify-between items-center">
          <span className="text-[#181818] text-[16px] font-normal leading-[20px]">
            {ticket?.busSchedule?.departure_place?.name || "-"} -{" "}
            {ticket?.busDropoff?.arrival_place?.name || "-"}
          </span>
          <span className="text-[#181818] text-[16px] font-normal leading-[20px] w-[86px] text-right">
            {formatDepartureDateTime()}
          </span>
        </div>

        {/* Third row: Price and status */}
        <div className="flex justify-between items-center">
          <span className="text-[#181818] text-[16px] font-normal leading-[20px] w-[88px]">
            {formatPrice(Number(order.total_amount))}
          </span>
          <OrderStatusBadge status={order.status} />
        </div>
      </div>
    </div>
  );
};
