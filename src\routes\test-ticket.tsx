import { createFileRoute } from "@tanstack/react-router";
import { memo } from "react";
import QRCodeSVG from "react-qr-code";

export const Route = createFileRoute("/test-ticket")({
  component: TestTicketPage,
});

// Mock data for testing
const mockOrderData = {
  id: "12345",
  customer_name: "D",
  customer_phone: "+84913548996",
  customer_email: "<EMAIL>",
};

const mockTripData = {
  route: "Hà Nội - Bắc Kạn",
  companyName: "Xe Khách Đức Hiền",
  departureTime: "20:30",
  arrivalTime: "09:00",
  duration: "12h30p",
  departureLocation: "Hà Nội",
  arrivalLocation: "Bắc Kạn",
};

const mockTicket = {
  id: "1",
  ticket_number: "VE001234567890",
  departure_date: "2025-06-07T20:30:00Z",
};

const TestTicketHTML = memo(() => {
  const isReturnTicket = false;
  const direction = isReturnTicket ? "Chiều về" : "<PERSON>ều đi";
  const primaryColor = isReturnTicket ? "#FF7F37" : "#2D5BFF";

  // Format departure date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const dayNames = [
        "Chủ Nhật",
        "Thứ 2",
        "Thứ 3",
        "Thứ 4",
        "Thứ 5",
        "Thứ 6",
        "Thứ 7",
      ];
      const dayName = dayNames[date.getDay()];
      const day = date.getDate().toString().padStart(2, "0");
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const year = date.getFullYear();
      return `${dayName}, ${day}/${month}/${year}`;
    } catch {
      return dateString;
    }
  };

  return (
    <div
      className="flex items-center bg-white border border-[#EDEDED] rounded-xl overflow-hidden"
      style={{ width: "800px", height: "180px" }}
    >
      {/* QR Code Section */}
      <div className="bg-white flex justify-center items-center p-4 border-r border-[#EDEDED] h-40">
        <div className="w-32 h-32 bg-white rounded-lg flex items-center justify-center">
          <QRCodeSVG
            value={mockTicket.ticket_number}
            size={120}
            bgColor="#FFFFFF"
            fgColor="#000000"
            level="M"
          />
        </div>
      </div>

      {/* Trip Information */}
      <div className="flex-1 bg-white p-4 w-[469px] h-40">
        {/* Header Row */}
        <div className="flex justify-between items-start gap-4">
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <div
                className="text-white text-xs font-bold px-3 py-1.5 rounded h-5 flex items-center justify-center w-fit"
                style={{ backgroundColor: primaryColor }}
              >
                {direction}
              </div>
              <span className="text-base font-bold text-[#2D5BFF]">
                {mockTripData.route}
              </span>
            </div>
            <span className="text-sm text-[#5C5C5C]">
              {formatDate(mockTicket.departure_date)}
            </span>
          </div>
        </div>

        {/* Company Name */}
        <div className="mb-[5px]">
          <h4 className="text-base font-bold text-[#181818] w-[437px]">
            {mockTripData.companyName}
          </h4>
        </div>

        {/* Journey Layout */}
        <div className="flex flex-col gap-2">
          {/* Time Row */}
          <div className="flex items-center gap-4">
            <span className="text-2xl font-extrabold text-[#181818]">
              {mockTripData.departureTime}
            </span>
            <div className="flex items-center gap-1 flex-1">
              <div className="w-4 h-4 bg-[#2D5BFF] rounded-full"></div>
              <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
              <span className="text-base text-[#5C5C5C]">
                {mockTripData.duration}
              </span>
              <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
              <div
                className="w-4 h-4 bg-[#FF7F37]"
                style={{ clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)" }}
              ></div>
            </div>
            <span className="text-2xl font-extrabold text-[#181818]">
              {mockTripData.arrivalTime}
            </span>
          </div>

          {/* Location Row */}
          <div className="flex justify-between gap-40">
            <div className="w-40">
              <p className="text-sm text-[#181818]">
                {mockTripData.departureLocation}
              </p>
            </div>
            <div className="w-40 text-right">
              <p className="text-sm text-[#181818]">
                {mockTripData.arrivalLocation}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Passenger Information */}
      <div className="w-[220px] h-40 bg-[#F8F8F8] p-4 rounded-r-xl flex flex-col justify-center gap-3">
        <div className="flex flex-col gap-0.5">
          <span className="text-sm text-[#5C5C5C]">Tên hành khách</span>
          <span className="text-sm font-bold text-[#5C5C5C]">
            {mockOrderData.customer_name}
          </span>
        </div>
        <div className="flex flex-col gap-0.5">
          <span className="text-sm text-[#5C5C5C]">Số điện thoại</span>
          <span className="text-sm font-bold text-[#5C5C5C]">
            {mockOrderData.customer_phone}
          </span>
        </div>
        {mockOrderData.customer_email && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-[#5C5C5C]">Email</span>
            <span className="text-sm font-bold text-[#5C5C5C]">
              {mockOrderData.customer_email}
            </span>
          </div>
        )}
      </div>
    </div>
  );
});

function TestTicketPage() {
  return (
    <div className="min-h-screen bg-[#F8F8F8] p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-[#181818] mb-8">
          Test Ticket UI
        </h1>

        <div className="space-y-8">
          {/* Chiều đi */}
          <div>
            <h2 className="text-lg font-bold text-[#181818] mb-4">
              Ticket Chiều đi (React Component)
            </h2>
            <div className="flex justify-center">
              <TestTicketHTML />
            </div>
          </div>

          {/* Chiều về */}
          <div>
            <h2 className="text-lg font-bold text-[#181818] mb-4">
              Ticket Chiều về (React Component)
            </h2>
            <div className="flex justify-center">
              <div
                className="flex items-center bg-white border border-[#EDEDED] rounded-xl overflow-hidden"
                style={{ width: "800px", height: "160px" }}
              >
                {/* QR Code Section */}
                <div className="bg-white flex justify-center items-center p-4 border-r border-[#EDEDED] h-40">
                  <div className="w-32 h-32 bg-white rounded-lg flex items-center justify-center">
                    <QRCodeSVG
                      value={mockTicket.ticket_number}
                      size={120}
                      bgColor="#FFFFFF"
                      fgColor="#000000"
                      level="M"
                    />
                  </div>
                </div>

                {/* Trip Information */}
                <div className="flex-1 bg-white border-b border-[#EDEDED] p-4 w-[469px] h-40">
                  {/* Header Row */}
                  <div className="flex justify-between items-start gap-4 mb-4">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <div className="bg-[#FF7F37] text-white text-xs font-bold px-3 py-1.5 rounded h-5 flex items-center justify-center w-fit">
                          Chiều về
                        </div>
                        <span className="text-base font-bold text-[#2D5BFF]">
                          Bắc Kạn - Hà Nội
                        </span>
                      </div>
                      <span className="text-sm text-[#5C5C5C]">
                        Thứ 7, 08/06/2025
                      </span>
                    </div>
                  </div>

                  {/* Company Name */}
                  <div className="mb-[15px]">
                    <h4 className="text-base font-bold text-[#181818] w-[437px]">
                      Xe Khách Đức Hiền
                    </h4>
                  </div>

                  {/* Journey Layout */}
                  <div className="flex flex-col gap-2">
                    {/* Time Row */}
                    <div className="flex items-center gap-4">
                      <span className="text-2xl font-extrabold text-[#181818]">
                        10:00
                      </span>
                      <div className="flex items-center gap-1 flex-1">
                        <div className="w-4 h-4 bg-[#2D5BFF] rounded-full"></div>
                        <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                        <span className="text-base text-[#5C5C5C]">12h30p</span>
                        <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                        <div
                          className="w-4 h-4 bg-[#FF7F37]"
                          style={{
                            clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)",
                          }}
                        ></div>
                      </div>
                      <span className="text-2xl font-extrabold text-[#181818]">
                        22:30
                      </span>
                    </div>

                    {/* Location Row */}
                    <div className="flex justify-between gap-40">
                      <div className="w-40">
                        <p className="text-sm text-[#181818]">Bắc Kạn</p>
                      </div>
                      <div className="w-40 text-right">
                        <p className="text-sm text-[#181818]">Hà Nội</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Passenger Information */}
                <div className="w-[220px] h-40 bg-[#F8F8F8] p-4 rounded-r-xl flex flex-col justify-center gap-3">
                  <div className="flex flex-col gap-0.5">
                    <span className="text-sm text-[#5C5C5C]">
                      Tên hành khách
                    </span>
                    <span className="text-sm font-bold text-[#5C5C5C]">D</span>
                  </div>
                  <div className="flex flex-col gap-0.5">
                    <span className="text-sm text-[#5C5C5C]">
                      Số điện thoại
                    </span>
                    <span className="text-sm font-bold text-[#5C5C5C]">
                      +84913548996
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-white p-6 rounded-lg border border-[#EDEDED]">
            <h3 className="text-lg font-bold text-[#181818] mb-3">
              Hướng dẫn sử dụng
            </h3>
            <ul className="list-disc list-inside space-y-2 text-sm text-[#5C5C5C]">
              <li>
                Trang này hiển thị chính xác UI của ticket sẽ được export ra
                image
              </li>
              <li>Bạn có thể inspect element để xem CSS và điều chỉnh</li>
              <li>Thay đổi dữ liệu mock ở đầu file để test với data khác</li>
              <li>
                URL:{" "}
                <code className="bg-gray-100 px-2 py-1 rounded">
                  /test-ticket
                </code>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
