import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { memo, useCallback, useState, useEffect } from "react";
import { z } from "zod";
import { Button } from "@/shared/components/button";
import { MapPinIcon, ArrowDownTrayIcon } from "@heroicons/react/24/solid";
import { useGetOrderById } from "@/shared/hooks/useOrders";
import { useContactFormStore } from "@/stores/contactFormStore";
import {
  downloadAllTicketImages,
  downloadSingleTicketImage,
  createTripDataFromTicket,
} from "@/shared/utils/ticket-html.utils";
import type { CreateOrderResponse, OrderTicket } from "@/api/orders";
import QRCodeSVG from "react-qr-code";

// Define search params schema
const paymentSuccessSearchSchema = z.object({
  orderId: z.string().optional(),
});

export const Route = createFileRoute("/payment-success")({
  validateSearch: paymentSuccessSearchSchema,
  component: PaymentSuccessPage,
});

// Success Icon Component with exact Figma SVG
const SuccessIcon = memo(() => (
  <div className="w-16 h-16 lg:w-[100px] lg:h-[100px] bg-white rounded-full flex items-center justify-center">
    <svg
      width="52"
      height="52"
      viewBox="0 0 81 81"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="lg:w-[81px] lg:h-[81px]"
    >
      <path
        d="M40.5 81C62.8153 81 81 62.8153 81 40.5C81 18.1847 62.8153 0 40.5 0C18.1847 0 0 18.1847 0 40.5C0 62.8153 18.1847 81 40.5 81Z"
        fill="#3AC922"
      />
      <path
        d="M25.3125 40.5L35.4375 50.625L55.6875 30.375"
        stroke="white"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
));

// Utility function to determine if ticket is return ticket
const isReturnTicket = (
  ticket: OrderTicket,
  allTickets: OrderTicket[]
): boolean => {
  // Method 1: Check ticket_type field
  if (ticket.ticket_type === "Return") {
    return true;
  }

  // Method 2: For round trip, second half are return tickets
  // This assumes the API returns outbound tickets first, then return tickets
  const ticketIndex = allTickets.findIndex((t) => t.id === ticket.id);
  const totalTickets = allTickets.length;

  // If we have an even number of tickets, assume first half is outbound, second half is return
  if (totalTickets % 2 === 0) {
    const halfPoint = totalTickets / 2;
    return ticketIndex >= halfPoint;
  }

  return false;
};

// Group tickets by direction
const groupTicketsByDirection = (tickets: OrderTicket[]) => {
  const outboundTickets: OrderTicket[] = [];
  const returnTickets: OrderTicket[] = [];

  tickets.forEach((ticket) => {
    if (isReturnTicket(ticket, tickets)) {
      returnTickets.push(ticket);
    } else {
      outboundTickets.push(ticket);
    }
  });

  return { outboundTickets, returnTickets };
};

// Mobile Ticket Card Component
export const MobileTicketCard = memo(
  ({
    ticket,
    orderData,
    isReturn,
    onDownloadTicket,
  }: {
    ticket: OrderTicket;
    orderData: CreateOrderResponse;
    isReturn: boolean;
    onDownloadTicket: (ticket: OrderTicket, isReturnTicket: boolean) => void;
  }) => {
    const tripData = createTripDataFromTicket(ticket);

    // Format departure date for display
    const formatDate = (dateString: string) => {
      try {
        const date = new Date(dateString);
        const dayNames = [
          "Chủ Nhật",
          "Thứ 2",
          "Thứ 3",
          "Thứ 4",
          "Thứ 5",
          "Thứ 6",
          "Thứ 7",
        ];
        const dayName = dayNames[date.getDay()];
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        return `${dayName}, ${day}/${month}`;
      } catch {
        return dateString;
      }
    };

    return (
      <div className="bg-white border border-[#EDEDED] rounded-xl overflow-hidden w-full">
        {/* Top section - Trip info (White background) */}
        <div className="bg-white border-b border-[#EDEDED] p-3">
          {/* Header Row */}
          <div className="flex items-start justify-between gap-4 mb-4">
            {/* Left: Tags and Route Info - Vertical layout */}
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">

                <span className="text-base font-bold text-[#2D5BFF]">
                  {tripData.route}
                </span>
              </div>
              <span className="text-sm text-[#5C5C5C]">
                {formatDate(ticket.departure_date)}
              </span>
            </div>

            {/* Download single ticket button */}
            <button
              onClick={() => onDownloadTicket(ticket, isReturn)}
              className="w-8 h-8 rounded-lg bg-[#ECF0FF] flex items-center justify-center hover:bg-[#2D5BFF] hover:text-white transition-colors"
              title="Tải vé này"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Company name */}
          <h4 className="text-base font-bold text-[#181818] mb-4">
            {tripData.companyName}
          </h4>

          {/* Time and route info */}
          <div className="space-y-2">
            {/* Time row */}
            <div className="flex items-center gap-4">
              <span className="text-2xl font-extrabold text-[#181818]">
                {tripData.departureTime}
              </span>
              <div className="flex items-center flex-1 gap-1">
                <div className="w-4 h-4 bg-[#2D5BFF] rounded-full"></div>
                <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                <span className="text-base text-[#5C5C5C]">
                  {tripData.duration}
                </span>
                <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                <MapPinIcon className="w-4 h-4 text-[#FF7F37]" />
              </div>
              <span className="text-2xl font-extrabold text-[#181818]">
                {tripData.arrivalTime}
              </span>
            </div>

            {/* Location row */}
            <div className="flex justify-between">
              <div className="flex-1 text-left">
                <p className="text-sm text-[#181818]">
                  {tripData.departureLocation}
                </p>
              </div>
              <div className="flex-1 text-right">
                <p className="text-sm text-[#181818]">
                  {tripData.arrivalLocation}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section - Passenger info (Gray background) */}
        <div className="bg-[#F8F8F8] p-3 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#5C5C5C]">Tên hành khách</span>
            <span className="text-sm font-bold text-[#5C5C5C]">
              {orderData.customer_name}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#5C5C5C]">Số điện thoại</span>
            <span className="text-sm font-bold text-[#5C5C5C]">
              {orderData.customer_phone}
            </span>
          </div>
          {!!orderData.customer_email && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#5C5C5C]">Email</span>
              <span className="text-sm font-bold text-[#5C5C5C]">
                {orderData.customer_email}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  }
);

// Desktop Ticket Card Component
export const DesktopTicketCard = memo(
  ({
    ticket,
    orderData,
    isReturn,
    onDownloadTicket,
  }: {
    ticket: OrderTicket;
    orderData: CreateOrderResponse;
    isReturn: boolean;
    onDownloadTicket: (ticket: OrderTicket, isReturnTicket: boolean) => void;
  }) => {
    const tripData = createTripDataFromTicket(ticket);

    // Format departure date for display
    const formatDate = (dateString: string) => {
      try {
        const date = new Date(dateString);
        const dayNames = [
          "Chủ Nhật",
          "Thứ 2",
          "Thứ 3",
          "Thứ 4",
          "Thứ 5",
          "Thứ 6",
          "Thứ 7",
        ];
        const dayName = dayNames[date.getDay()];
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        return `${dayName}, ${day}/${month}`;
      } catch {
        return dateString;
      }
    };

    return (
      <div className="flex h-full items-center bg-white border border-[#EDEDED] rounded-xl overflow-hidden">
        {/* Left: QR Code Section */}
        <div className="flex flex-col gap-2 bg-white justify-center items-center p-4 border-r border-[#EDEDED]">
          <div className="flex items-center justify-center w-32 h-32 bg-white rounded-lg">
            {ticket?.ticket_number ? (
              <QRCodeSVG
                value={ticket.ticket_number}
                size={120}
                bgColor="#FFFFFF"
                fgColor="#000000"
                level="M"
              />
            ) : (
              <div className="flex items-center justify-center w-full h-full bg-gray-200 rounded-lg">
                <span className="text-sm text-gray-500">QR Code</span>
              </div>
            )}
          </div>
          <div className="flex gap-1">
            <span className="text-sm font-bold text-[#181818]">Mã vé:</span>
            <span className="text-sm font-bold text-[#2D5BFF]">
              {ticket?.ticket_number || "-"}
            </span>
          </div>
        </div>

        {/* Center-Right: Trip Information */}
        <div className="flex-1 bg-white p-4 w-[469px] h-full">
          {/* Header Row */}
          <div className="flex items-start justify-between gap-4 mb-4">
            {/* Left: Tags and Route Info - Vertical layout */}
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <span className="text-base font-bold text-[#2D5BFF]">
                  {tripData.route}
                </span>
              </div>
              <span className="text-sm text-[#5C5C5C]">
                {formatDate(ticket.departure_date)}
              </span>
            </div>

            {/* Download single ticket button */}
            <button
              onClick={() => onDownloadTicket(ticket, isReturn)}
              className="w-8 h-8 rounded-lg bg-[#ECF0FF] flex items-center justify-center hover:bg-[#2D5BFF] hover:text-white transition-colors"
              title="Tải vé này"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
            </button>
          </div>

          {/* Company Name */}
          <div className="mb-[15px]">
            <h4 className="text-base font-bold text-[#181818] w-[437px]">
              {tripData.companyName}
            </h4>
          </div>

          {/* Time and Location Layout */}
          <div className="flex flex-col gap-2">
            {/* Time Row */}
            <div className="flex items-center gap-4">
              <span className="text-2xl font-extrabold text-[#181818]">
                {tripData.departureTime}
              </span>
              <div className="flex items-center flex-1 gap-1">
                <div className="w-4 h-4 bg-[#2D5BFF] rounded-full"></div>
                <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                <span className="text-base text-[#5C5C5C]">
                  {tripData.duration}
                </span>
                <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                <MapPinIcon className="w-4 h-4 text-[#FF7F37]" />
              </div>
              <span className="text-2xl font-extrabold text-[#181818]">
                {tripData.arrivalTime}
              </span>
            </div>

            {/* Location Row */}
            <div className="flex justify-between gap-40">
              <div className="w-40">
                <p className="text-sm text-[#181818]">
                  {tripData.departureLocation}
                </p>
              </div>
              <div className="w-40 text-right">
                <p className="text-sm text-[#181818]">
                  {tripData.arrivalLocation}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right: Passenger Information */}
        <div className="w-[220px] h-full bg-[#F8F8F8] p-6 rounded-r-xl flex flex-col justify-center gap-3">
          <div className="flex flex-col gap-0.5">
            <span className="text-sm text-[#5C5C5C]">Tên hành khách</span>
            <span className="text-sm font-bold text-[#5C5C5C]">
              {orderData.customer_name}
            </span>
          </div>
          <div className="flex flex-col gap-0.5">
            <span className="text-sm text-[#5C5C5C]">Số điện thoại</span>
            <span className="text-sm font-bold text-[#5C5C5C]">
              {orderData.customer_phone}
            </span>
          </div>
          {!!orderData.customer_email && (
            <div className="flex flex-col gap-0.5">
              <span className="text-sm text-[#5C5C5C]">Gmail</span>
              <span className="text-sm font-bold text-[#5C5C5C]">
                {orderData.customer_email}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  }
);

function PaymentSuccessPage() {
  const navigate = useNavigate();
  const searchParams = Route.useSearch();
  const [isDownloading, setIsDownloading] = useState(false);
  const { clearFormData } = useContactFormStore();

  // Fetch order data if orderId is provided
  const {
    data: orderData,
    isLoading,
    error,
  } = useGetOrderById(searchParams.orderId || "", !!searchParams.orderId);

  // Clear form data khi vào trang payment success
  useEffect(() => {
    clearFormData();
  }, [clearFormData]);

  const handleGoHome = useCallback(() => {
    navigate({ to: "/" });
  }, [navigate]);

  const handleDownloadAllTickets = useCallback(async () => {
    if (!orderData) return;

    setIsDownloading(true);
    try {
      await downloadAllTicketImages(orderData);
    } catch (error) {
      console.error("Error downloading tickets:", error);
      alert("Có lỗi xảy ra khi tải vé. Vui lòng thử lại.");
    } finally {
      setIsDownloading(false);
    }
  }, [orderData]);

  const handleDownloadSingleTicket = useCallback(
    async (ticket: OrderTicket, isReturnTicket: boolean) => {
      if (!orderData) return;

      try {
        await downloadSingleTicketImage(ticket, orderData, isReturnTicket);
      } catch (error) {
        console.error("Error downloading single ticket:", error);
        alert("Có lỗi xảy ra khi tải vé. Vui lòng thử lại.");
      }
    },
    [orderData]
  );

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F8F8F8] flex items-center justify-center">
        <div className="text-lg">Đang tải thông tin đơn hàng...</div>
      </div>
    );
  }

  // Error state
  if (error || !orderData) {
    return (
      <div className="min-h-screen bg-[#F8F8F8] flex items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-lg text-red-600">
            Không thể tải thông tin đơn hàng
          </div>
          <Button onClick={handleGoHome}>Về trang chủ</Button>
        </div>
      </div>
    );
  }

  // If no tickets, show error
  if (!orderData.tickets || orderData.tickets.length === 0) {
    return (
      <div className="min-h-screen bg-[#F8F8F8] flex items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-lg text-red-600">
            Không tìm thấy thông tin vé
          </div>
          <Button onClick={handleGoHome}>Về trang chủ</Button>
        </div>
      </div>
    );
  }

  // Group tickets by direction
  const { outboundTickets, returnTickets } = groupTicketsByDirection(
    orderData.tickets
  );

  return (
    <div className="min-h-screen bg-[#F8F8F8]">
      {/* Mobile Layout */}
      <div className="px-4 py-3 lg:hidden">
        <div className="p-3 space-y-6 bg-white rounded-3xl">
          {/* Success Message */}
          <div className="flex flex-col items-center gap-3 text-center">
            <SuccessIcon />
            <h1 className="text-xl font-extrabold text-[#181818]">
              Hoàn tất đặt vé
            </h1>
          </div>

          {/* Tickets Display */}
          <div className="space-y-3">
            {/* Outbound tickets section */}
            {outboundTickets.length > 0 && (
              <div className="space-y-3">
                {/* Section Header */}
                <div className="flex items-center gap-3">
                  <span className="text-base font-bold text-[#181818]">
                    Vé chiều đi
                  </span>
                  <div className="border-t border-[#EDEDED] border-dashed flex-1"></div>
                </div>

                {/* Tickets List */}
                <div className="space-y-3">
                  {outboundTickets.map((ticket) => (
                    <MobileTicketCard
                      key={`outbound-${ticket.id}`}
                      ticket={ticket}
                      orderData={orderData}
                      isReturn={false}
                      onDownloadTicket={handleDownloadSingleTicket}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Return tickets section */}
            {returnTickets.length > 0 && (
              <div className="space-y-3">
                {/* Section Header */}
                <div className="flex items-center gap-3">
                  <span className="text-base font-bold text-[#181818]">
                    Vé chiều về
                  </span>
                  <div className="border-t border-[#EDEDED] border-dashed flex-1"></div>
                </div>

                {/* Tickets List */}
                <div className="space-y-3">
                  {returnTickets.map((ticket) => (
                    <MobileTicketCard
                      key={`return-${ticket.id}`}
                      ticket={ticket}
                      orderData={orderData}
                      isReturn={true}
                      onDownloadTicket={handleDownloadSingleTicket}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Separator Line */}
          <div className="border-t border-[#EDEDED] w-full"></div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handleDownloadAllTickets}
              disabled={isDownloading}
              variant="secondary"
              className="w-full text-lg font-extrabold text-[#2D5BFF] bg-[#ECF0FF] border-0 py-6 flex items-center justify-center gap-2"
            >
              {isDownloading ? (
                <>
                  <div className="w-4 h-4 border-2 border-[#2D5BFF] border-t-transparent rounded-full animate-spin"></div>
                  Đang tải...
                </>
              ) : (
                <>
                  <ArrowDownTrayIcon className="w-5 h-5" />
                  Tải toàn bộ vé
                </>
              )}
            </Button>
            <Button
              onClick={handleGoHome}
              variant="ghost"
              className="w-full text-lg font-extrabold text-[#2D5BFF] hover:bg-transparent py-3"
            >
              Về trang chủ
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex lg:justify-center lg:items-center lg:p-12">
        {/* Main Content Container - Exact Figma layout */}
        <div className="flex flex-col items-center gap-6 px-12 py-6 bg-white rounded-3xl">
          {/* Success Message */}
          <div className="flex flex-col items-center gap-3">
            <SuccessIcon />
            <h1 className="text-2xl font-extrabold text-[#181818]">
              Hoàn tất đặt vé
            </h1>
          </div>

          {/* Tickets Display */}
          <div className="flex flex-col gap-6">
            {/* Outbound tickets section */}
            {outboundTickets.length > 0 && (
              <div className="flex flex-col gap-3">
                {/* Section Header */}
                <div className="flex items-center gap-3">
                  <span className="text-base font-bold text-[#181818]">
                    Vé chiều đi
                  </span>
                  <div className="border-t border-[#EDEDED] border-dashed flex-1"></div>
                </div>

                {/* Tickets List */}
                <div className="flex flex-col gap-3">
                  {outboundTickets.map((ticket) => (
                    <DesktopTicketCard
                      key={`outbound-${ticket.id}`}
                      ticket={ticket}
                      orderData={orderData}
                      isReturn={false}
                      onDownloadTicket={handleDownloadSingleTicket}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Return tickets section */}
            {returnTickets.length > 0 && (
              <div className="flex flex-col gap-3">
                {/* Section Header */}
                <div className="flex items-center gap-3">
                  <span className="text-base font-bold text-[#181818]">
                    Vé chiều về
                  </span>
                  <div className="border-t border-[#EDEDED] border-dashed flex-1"></div>
                </div>

                {/* Tickets List */}
                <div className="flex flex-col gap-3">
                  {returnTickets.map((ticket) => (
                    <DesktopTicketCard
                      key={`return-${ticket.id}`}
                      ticket={ticket}
                      orderData={orderData}
                      isReturn={true}
                      onDownloadTicket={handleDownloadSingleTicket}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Separator Line */}
          <div className="border-t border-[#EDEDED] w-full"></div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 w-[400px]">
            <Button
              onClick={handleDownloadAllTickets}
              disabled={isDownloading}
              variant="secondary"
              className="w-full text-lg font-extrabold text-[#2D5BFF] bg-[#ECF0FF] border-0 py-3 flex items-center justify-center gap-2"
            >
              {isDownloading ? (
                <>
                  <div className="w-4 h-4 border-2 border-[#2D5BFF] border-t-transparent rounded-full animate-spin"></div>
                  Đang tải...
                </>
              ) : (
                <>
                  <ArrowDownTrayIcon className="w-5 h-5" />
                  Tải toàn bộ vé
                </>
              )}
            </Button>
            <Button
              onClick={handleGoHome}
              variant="ghost"
              className="w-full text-lg font-extrabold text-[#2D5BFF] hover:bg-transparent py-3"
            >
              Về trang chủ
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
