import React from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";

interface BusPricingProps {
  price: number;
  currency: string;
  remainingTickets: number;
}

const BusPricing: React.FC<BusPricingProps> = ({
  price,
  currency,
  remainingTickets,
}) => {
  const { t } = useTranslation();

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN").format(price);
  };

  return (
    <div className="text-right">
      <div className="text-2xl font-bold text-orange-600">
        {formatPrice(price)}
        {currency}
      </div>
      <div className="text-sm text-gray-500">
        {remainingTickets} {t("bus.emptySeats")}
      </div>
    </div>
  );
};

export default BusPricing;
