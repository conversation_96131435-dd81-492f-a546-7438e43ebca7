// Local Storage keys
const STORAGE_KEYS = {
  LAST_ORDER_ID: "lastOrderId",
  ORDER_HISTORY: "orderHistory",
} as const;

// Order ID management
export const orderStorage = {
  // Save last order ID
  saveLastOrderId: (orderId: string): void => {
    try {
      localStorage.setItem(STORAGE_KEYS.LAST_ORDER_ID, orderId);
    } catch (error) {
      console.error("Failed to save order ID to localStorage:", error);
    }
  },

  // Get last order ID
  getLastOrderId: (): string | null => {
    try {
      return localStorage.getItem(STORAGE_KEYS.LAST_ORDER_ID);
    } catch (error) {
      console.error("Failed to get order ID from localStorage:", error);
      return null;
    }
  },

  // Clear last order ID
  clearLastOrderId: (): void => {
    try {
      localStorage.removeItem(STORAGE_KEYS.LAST_ORDER_ID);
    } catch (error) {
      console.error("Failed to clear order ID from localStorage:", error);
    }
  },

  // Add order to history (optional - for future use)
  addToOrderHistory: (orderId: string): void => {
    try {
      const history = getOrderHistory();
      const updatedHistory = [
        orderId,
        ...history.filter((id) => id !== orderId),
      ].slice(0, 10); // Keep last 10 orders
      localStorage.setItem(
        STORAGE_KEYS.ORDER_HISTORY,
        JSON.stringify(updatedHistory)
      );
    } catch (error) {
      console.error("Failed to add order to history:", error);
    }
  },

  // Get order history (optional - for future use)
  getOrderHistory: (): string[] => {
    try {
      const history = localStorage.getItem(STORAGE_KEYS.ORDER_HISTORY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error("Failed to get order history from localStorage:", error);
      return [];
    }
  },

  // Clear all order data
  clearAllOrderData: (): void => {
    try {
      localStorage.removeItem(STORAGE_KEYS.LAST_ORDER_ID);
      localStorage.removeItem(STORAGE_KEYS.ORDER_HISTORY);
    } catch (error) {
      console.error("Failed to clear order data from localStorage:", error);
    }
  },
};

// Helper function for getOrderHistory (to avoid hoisting issues)
const getOrderHistory = (): string[] => {
  try {
    const history = localStorage.getItem(STORAGE_KEYS.ORDER_HISTORY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error("Failed to get order history from localStorage:", error);
    return [];
  }
};
