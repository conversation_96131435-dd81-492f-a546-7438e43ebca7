import React, { forwardRef, useState } from "react";
import { Eye<PERSON><PERSON>, EyeSlashIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";

export interface PasswordInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  required?: boolean;
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, error, label, required, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    return (
      <div className="relative mb-6">
        {label && (
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          <input
            ref={ref}
            type={showPassword ? "text" : "password"}
            className={cn(
              "w-full px-4 py-3 pr-12 rounded-lg border bg-gray-50",
              "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
              "disabled:cursor-not-allowed disabled:opacity-50",
              "transition-colors duration-200",
              error ? "border-red-500 focus:ring-red-500" : "border-gray-200",
              className
            )}
            {...props}
          />
          <button
            type="button"
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeSlashIcon className="w-4 h-4" />
            ) : (
              <EyeIcon className="w-4 h-4" />
            )}
          </button>
        </div>
        {/* Error message with absolute positioning to prevent UI jumping */}
        <div className="absolute left-0 top-full mt-1 min-h-[20px]">
          {error && (
            <p className="text-red-500 text-sm leading-tight animate-in fade-in-0 duration-200">
              {error}
            </p>
          )}
        </div>
      </div>
    );
  }
);

PasswordInput.displayName = "PasswordInput";
