// Strapi API response types

export interface StrapiPopularRoute {
  id: number;
  published_at: string;
  created_at: string;
  updated_at: string;
  routeName: string;
  fromId: number | null;
  toId: number | null;
  fromName: string;
  toName: string;
  image_url: StrapiImage[];
  price: number;
  priceDescription: string;
  fromCode: string;
  toCode: string;
}

export interface StrapiPopularRoutesResponse
  extends Array<StrapiPopularRoute> {}

// Image format types for Strapi media
export interface StrapiImageFormat {
  ext: string;
  url: string;
  hash: string;
  mime: string;
  name: string;
  path: string | null;
  size: number;
  width: number;
  height: number;
}

export interface StrapiImageFormats {
  large?: StrapiImageFormat;
  medium?: StrapiImageFormat;
  small?: StrapiImageFormat;
  thumbnail?: StrapiImageFormat;
}

export interface StrapiImage {
  id: number;
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: StrapiImageFormats;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: any;
  created_at: string;
  updated_at: string;
}

// Articles
export interface StrapiArticle {
  id: number;
  title: string;
  content: string;
  published_at: string;
  created_at: string;
  updated_at: string;
  image_background: StrapiImage;
}

export interface StrapiArticlesResponse extends Array<StrapiArticle> {}

// Discount Places
export interface StrapiDiscountPlace {
  id: number;
  name: string;
  place_id: number;
  published_at: string;
  created_at: string;
  updated_at: string;
}

export interface StrapiDiscountPlacesResponse
  extends Array<StrapiDiscountPlace> {}

// Discount Bus Operators
export interface StrapiDiscountBusOperator {
  id: number;
  title: string;
  content: string;
  published_at: string;
  created_at: string;
  updated_at: string;
  image_background: StrapiImage;
  place: string;
  place_id: number;
  discount_place: StrapiDiscountPlace;
}

export interface StrapiDiscountBusOperatorsResponse
  extends Array<StrapiDiscountBusOperator> {}

// Discounts (general structure based on typical Strapi discounts)
export interface StrapiDiscount {
  id: number;
  title: string;
  content: string;
  discount_percentage?: number;
  discount_amount?: number;
  code?: string;
  valid_from?: string;
  valid_until?: string;
  published_at: string;
  created_at: string;
  updated_at: string;
  image_background?: StrapiImage;
}

export interface StrapiDiscountsResponse extends Array<StrapiDiscount> {}

// Common error type for Strapi API
export interface StrapiApiError {
  error: {
    status: number;
    name: string;
    message: string;
    details?: any;
  };
}
