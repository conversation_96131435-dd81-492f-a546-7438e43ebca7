/**
 * Date utility functions for consistent date formatting across the app
 * These functions have no external dependencies and can be used anywhere
 */

/**
 * Converts various date formats to ISO date format (YYYY-MM-DD)
 * @param dateStr - Date string in various formats
 * @returns ISO date string or empty string if invalid
 */
export const convertToISODate = (dateStr: string): string => {
  if (!dateStr || typeof dateStr !== "string") return "";

  // Already in YYYY-MM-DD format
  if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return dateStr;
  }

  // Handle dd/MM format (current year assumed)
  if (dateStr.match(/^\d{1,2}\/\d{1,2}$/)) {
    const [day, month] = dateStr.split("/");
    const currentYear = new Date().getFullYear();
    const paddedMonth = month.padStart(2, "0");
    const paddedDay = day.padStart(2, "0");
    return `${currentYear}-${paddedMonth}-${paddedDay}`;
  }

  // Handle dd/MM/yyyy format
  if (dateStr.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
    const [day, month, year] = dateStr.split("/");
    const paddedMonth = month.padStart(2, "0");
    const paddedDay = day.padStart(2, "0");
    return `${year}-${paddedMonth}-${paddedDay}`;
  }

  // Handle yyyy/MM/dd format
  if (dateStr.match(/^\d{4}\/\d{1,2}\/\d{1,2}$/)) {
    const [year, month, day] = dateStr.split("/");
    const paddedMonth = month.padStart(2, "0");
    const paddedDay = day.padStart(2, "0");
    return `${year}-${paddedMonth}-${paddedDay}`;
  }

  return dateStr;
};

/**
 * Formats date string for display as dd/MM (without year)
 * @param dateStr - Date string in various formats
 * @returns Formatted date string as dd/MM (with leading zeros) or original string if invalid
 */
export const formatDateForDisplay = (dateStr: string): string => {
  if (!dateStr) return "";

  try {
    let date: Date;

    // If it's ISO format, convert to Date
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      date = new Date(dateStr);
    } else if (dateStr.includes("/")) {
      // Handle dd/MM or dd/MM/yyyy format
      const parts = dateStr.split("/");
      if (parts.length >= 2) {
        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]);
        const year =
          parts.length >= 3 ? parseInt(parts[2]) : new Date().getFullYear();
        date = new Date(year, month - 1, day);
      } else {
        return dateStr;
      }
    } else {
      return dateStr;
    }

    // Format with 2-digit day and month
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
    });
  } catch {
    return dateStr;
  }
};

/**
 * Formats date string for display with full format
 * @param dateString - Date string to format
 * @returns Formatted date string as dd/MM or empty string if invalid
 */
export const formatDateDisplay = (dateString: string): string => {
  return formatDateForDisplay(dateString);
};

/**
 * Validates if a date string is in valid ISO format (YYYY-MM-DD)
 * @param dateStr - Date string to validate
 * @returns boolean indicating if the date is valid ISO format
 */
export const isValidISODate = (dateStr: string): boolean => {
  if (!dateStr) return false;
  const isoPattern = /^\d{4}-\d{2}-\d{2}$/;
  if (!isoPattern.test(dateStr)) return false;

  const date = new Date(dateStr);
  return date.toISOString().slice(0, 10) === dateStr;
};

/**
 * Checks if a date string represents a valid round trip scenario
 * @param departureDate - Departure date string
 * @param returnDate - Return date string
 * @returns boolean indicating if it's a valid round trip
 */
export const isValidRoundTrip = (
  departureDate: string,
  returnDate: string
): boolean => {
  if (!departureDate || !returnDate) return false;

  const depDateISO = convertToISODate(departureDate);
  const retDateISO = convertToISODate(returnDate);

  return !!(
    retDateISO &&
    depDateISO &&
    retDateISO !== depDateISO &&
    retDateISO.trim() !== "" &&
    depDateISO.trim() !== ""
  );
};

/**
 * Gets today's date as ISO string (YYYY-MM-DD)
 * @returns Today's date in ISO format
 */
export const getTodayISO = (): string => {
  const today = new Date();
  return today.toISOString().slice(0, 10);
};

/**
 * Checks if a date is in the past (before today)
 * @param dateStr - Date string to check
 * @returns boolean indicating if the date is in the past
 */
export const isDateInPast = (dateStr: string): boolean => {
  if (!dateStr) return false;

  const date = new Date(convertToISODate(dateStr));
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return date < today;
};

/**
 * Formats a date range for display
 * @param departureDate - Departure date string
 * @param returnDate - Return date string (optional)
 * @returns Formatted date range string
 */
export const formatDateRange = (
  departureDate: string,
  returnDate?: string
): string => {
  const departure = formatDateForDisplay(departureDate);

  if (!returnDate || returnDate === departureDate) {
    return departure;
  }

  const returnFormatted = formatDateForDisplay(returnDate);
  if (returnFormatted && returnFormatted !== departure) {
    return `${departure} - ${returnFormatted}`;
  }

  return departure;
};
