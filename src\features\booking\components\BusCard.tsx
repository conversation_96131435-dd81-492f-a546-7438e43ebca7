import React, { useState, useCallback } from "react";
import { clsx } from "clsx";
import { MapPinIcon } from "@heroicons/react/24/solid";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Skeleton } from "@/shared/components/skeleton";
import { TripTimeDisplay } from "@/shared/components";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import type { SelectedTrip } from "@/stores/selectedTripsStore";
import type { Bus, SearchParams } from "@/shared/types/bus";
import type { Dropoff } from "@/features/booking/api/bus-schedules/bus-schedules.types";
import { formatTime } from "@/lib/utils";
import { useSearchForm } from "@/contexts/SearchFormContext";
import { Link } from "@tanstack/react-router";

interface BusCardProps {
  bus: Bus;
  dropoffs?: Dropoff[];
  isHighlighted?: boolean;
  onSelect?: (busId: string) => void;
  searchParams?: SearchParams;
  tripType?: "outbound" | "return"; // Thêm prop để xác định chiều đi hay về
  onOutboundSelected?: () => void; // Callback khi chọn outbound trong round trip
}

// Schedule Tab Component
const ScheduleTab: React.FC<{ dropoffs?: Dropoff[]; bus: Bus }> = React.memo(
  ({ dropoffs, bus }) => {
    const { t } = useTranslation();

    // Create schedule items from dropoffs data
    const scheduleItems = React.useMemo(() => {
      if (!dropoffs || dropoffs.length === 0) {
        return [
          {
            time: bus.departureTime,
            location: bus.departureLocation,
            address: bus.departureLocation,
            isDeparture: true,
          },
          {
            time: bus.arrivalTime,
            location: bus.arrivalLocation,
            address: bus.arrivalLocation,
            isDeparture: false,
          },
        ];
      }

      // Start with departure
      const items = [
        {
          time: bus.departureTime,
          location: bus.departureLocation,
          address: bus.departureLocation,
          isDeparture: true,
        },
      ];

      // Add dropoffs
      dropoffs.forEach((dropoff) => {
        items.push({
          time: formatTime(dropoff.arrival_time),
          location: dropoff.arrival_place?.name || dropoff.location_name,
          address: dropoff.location_name,
          isDeparture: false,
        });
      });

      return items;
    }, [dropoffs, bus]);

    return (
      <div className="border-t border-gray-200 p-4 lg:p-6">
        <div className="flex gap-2">
          {/* Timeline Column */}
          <div className="flex flex-col gap-8 lg:gap-11">
            {scheduleItems.map((item, index) => (
              <div key={index} className="flex flex-col gap-1">
                <div className="bg-white px-2 py-1 text-xs font-bold text-gray-900 w-fit">
                  {item.time}
                </div>
              </div>
            ))}
          </div>

          {/* Vertical Line with Dots */}
          <div className="flex flex-col items-center gap-1 pt-2">
            {scheduleItems.map((_, index) => (
              <React.Fragment key={index}>
                <div
                  className={clsx(
                    "w-4 h-4 rounded-full flex-shrink-0",
                    index === 0
                      ? "bg-blue-600"
                      : index === scheduleItems.length - 1
                        ? "bg-orange-500"
                        : "bg-gray-300"
                  )}
                >
                  {index === scheduleItems.length - 1 && (
                    <MapPinIcon className="w-full h-full text-orange-500" />
                  )}
                </div>
                {index < scheduleItems.length - 1 && (
                  <div className="w-0 h-6 lg:h-9 border-l border-dashed border-gray-300"></div>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Location Details Column */}
          <div className="flex-1 flex flex-col gap-4 lg:gap-6">
            {scheduleItems.map((item, index) => (
              <div key={index} className="flex flex-col gap-1">
                <div className="text-xs font-bold text-gray-900">
                  {item.location}
                </div>
                <div className="text-xs text-gray-500">{item.address}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Warning Message */}
        <div className="flex items-start gap-3 lg:gap-4 mt-4 lg:mt-6 p-3 lg:p-4 bg-yellow-50 rounded-lg">
          <div className="w-5 h-5 lg:w-6 lg:h-6 flex-shrink-0">
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-yellow-500"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
            </svg>
          </div>
          <p className="text-xs text-gray-900 leading-relaxed">
            {t("bus.scheduleWarning")}
          </p>
        </div>
      </div>
    );
  }
);

ScheduleTab.displayName = "ScheduleTab";

// Policy Tab Component
const PolicyTab: React.FC<{ bus: Bus }> = React.memo(({ bus }) => {
  const { t } = useTranslation();

  // Use real policy content from fleet or fallback to mock data
  const policyContent = bus.fleetPolicy || t("bus.policyNotAvailable");

  return (
    <div className="border-t border-gray-200 p-4 lg:p-6">
      <div className="flex flex-col gap-4 lg:gap-6">
        <div className="flex flex-col gap-1">
          <div className="text-xs text-gray-500 leading-relaxed prose prose-xs max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-sm font-bold text-gray-900 mb-2">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-sm font-bold text-gray-900 mb-2">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-xs font-bold text-gray-900 mb-1">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-xs text-gray-500 mb-2 leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="text-xs text-gray-500 ml-4 mb-2 list-disc">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="text-xs text-gray-500 ml-4 mb-2 list-decimal">
                    {children}
                  </ol>
                ),
                li: ({ children }) => <li className="mb-1">{children}</li>,
                strong: ({ children }) => (
                  <strong className="font-bold text-gray-900">
                    {children}
                  </strong>
                ),
                em: ({ children }) => <em className="italic">{children}</em>,
              }}
            >
              {policyContent}
            </ReactMarkdown>
          </div>
        </div>
      </div>
    </div>
  );
});

PolicyTab.displayName = "PolicyTab";

const BusCard: React.FC<BusCardProps> = React.memo(
  ({
    bus,
    dropoffs,
    isHighlighted = false,
    onSelect,
    searchParams,
    tripType,
    onOutboundSelected,
  }) => {
    const { t } = useTranslation();
    const {
      outboundTrip,
      returnTrip,
      setOutboundTrip,
      setReturnTrip,
      setIsRoundTrip,
      getTotalPrice,
    } = useSelectedTripsStore();

    const { state } = useSearchForm(); // Get current search form state

    const basePrice = state.isRoundTrip
      ? getTotalPrice()
      : outboundTrip?.price || 0;

    const displayPrice = basePrice * state.numTickets;

    const checkoutSearchParams = {
      scheduleId: outboundTrip?.id || "",
      price: displayPrice,
      numTickets: state.numTickets,
      fromPlaceId: searchParams?.fromPlaceId,
      toPlaceId: searchParams?.toPlaceId,
      fromPlaceName: outboundTrip?.departureLocation || "",
      toPlaceName: outboundTrip?.arrivalLocation || "",
      departureDate: outboundTrip?.departureDate || "",
      returnDate: state.returnDate,
      isRoundTrip: state.isRoundTrip,
      busNumber: outboundTrip?.busNumber || "",
      departureTime: outboundTrip?.departureTime || "",
      arrivalTime: outboundTrip?.arrivalTime || "",
      dropoffId: outboundTrip?.dropoffId || "",
    };

    const [activeDetailTab, setActiveDetailTab] = useState<
      "schedule" | "policy" | null
    >(null);
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    // Check if this bus is selected
    const isSelected =
      tripType === "outbound"
        ? outboundTrip?.id === bus.id
        : returnTrip?.id === bus.id;

    const handleDetailTabChange = useCallback((tab: "schedule" | "policy") => {
      // Toggle functionality: if clicking the same tab, close it
      setActiveDetailTab((currentTab) => (currentTab === tab ? null : tab));
    }, []);

    const handleSelectBus = useCallback(() => {
      // Create selected trip object
      const selectedTrip: SelectedTrip = {
        id: bus.id,
        scheduleId: bus.originalScheduleId || bus.id,
        name: bus.name,
        busNumber: bus.busNumber,
        busDescription: bus.busDescription,
        departureTime: bus.departureTime,
        arrivalTime: bus.arrivalTime,
        price: bus.price,
        departureLocation: bus.departureLocation,
        arrivalLocation: bus.arrivalLocation,
        departureDate:
          tripType === "return"
            ? searchParams?.returnDate || searchParams?.departureDate || ""
            : searchParams?.departureDate || "",
        duration: bus.duration,
        dropoffId: bus.dropoffId,
        route: `${bus.departureLocation} - ${bus.arrivalLocation}`,
        companyName: bus.name,
        note: bus.busDescription,
        image: bus.image,
      };

      // Add to store based on trip type
      if (tripType === "outbound") {
        setOutboundTrip(selectedTrip);
        // Set round trip flag based on search params
        setIsRoundTrip(!!searchParams?.isRoundTrip);

        // Automatically switch to return tab if it's a round trip
        if (searchParams?.isRoundTrip && onOutboundSelected) {
          onOutboundSelected();
        }
      } else {
        setReturnTrip(selectedTrip);
      }

      // Call onSelect if provided
      onSelect?.(bus.id);
    }, [
      bus,
      searchParams,
      tripType,
      setOutboundTrip,
      setReturnTrip,
      setIsRoundTrip,
      onSelect,
      onOutboundSelected,
    ]);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
    }, []);

    const handleImageError = useCallback(() => {
      setImageError(true);
      setImageLoaded(true);
    }, []);

    const formatBusType = (type: string) => {
      if (type === "limousine") return "Limousine";
      if (type === "bed") return "Giường nằm";
      return "Ghế ngồi";
    };

    const formatNexDate = (nextDate: number) => {
      const date = new Date(String(searchParams?.departureDate));

      date.setDate(date.getDate() + nextDate);

      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${day}-${month}`;
    };

    const getCrossedDays = (
      startDateString: string,
      departureTime: string,
      duration: string
    ) => {
      // Parse departure time
      const [year, month, day] = startDateString.split("-").map(Number);
      const [depHour, depMin] = departureTime.split(":").map(Number);
      const startDate = new Date(year, month - 1, day, depHour, depMin);

      // Parse duration: "9h", "1h 30m", etc.
      const hourMatch = duration.match(/(\d+)\s*h/);
      const minuteMatch = duration.match(/(\d+)\s*m/);
      const durHours = hourMatch ? parseInt(hourMatch[1], 10) : 0;
      const durMins = minuteMatch ? parseInt(minuteMatch[1], 10) : 0;

      // Tính ngày đến
      const arrivalDate = new Date(startDate);
      arrivalDate.setHours(arrivalDate.getHours() + durHours);
      arrivalDate.setMinutes(arrivalDate.getMinutes() + durMins);

      // So sánh ngày
      const dayDiff = arrivalDate.getDate() - startDate.getDate();
      return dayDiff;
    };

    return (
      <div
        className={clsx(
          "bg-white rounded-xl border transition-all duration-200",
          isHighlighted || isSelected
            ? "border-blue-600 shadow-lg"
            : "border-gray-200 hover:border-[#2D5BFF] hover:shadow-md"
        )}
      >
        {/* Mobile Layout */}
        <div className="lg:hidden cursor-pointer" onClick={handleSelectBus}>
          {/* Main Content - Mobile */}
          <Link to="/booking/checkout" search={checkoutSearchParams}>
            <div className="p-4">
              {/* Company Name */}
              <h3 className="text-xl font-extrabold text-[#181818] mb-3">
                {bus.name}
              </h3>

              {/* Time and Route Info - Mobile */}
              <div className="mb-4">
                <TripTimeDisplay
                  departureTime={bus.departureTime}
                  arrivalTime={bus.arrivalTime}
                  departureLocation={bus.departureLocation}
                  arrivalLocation={bus.arrivalLocation}
                  duration={bus.duration}
                  variant="bus-card"
                  showLocations={true}
                />
              </div>

              {/* Divider */}
              <div className="border-t border-[#EDEDED] my-3"></div>

              {/* Bottom Row - Mobile: Giường nằm • Chỗ trống • Giá tiền */}
              <div className="flex justify-between items-center">
                {/* Left: Bus Type and Available Seats in one row */}
                <div className="flex items-center gap-2">
                  <span className="text-base font-normal text-[#181818]">
                    {formatBusType(bus.bus.bus_type)}
                  </span>
                  <div className="w-1.5 h-1.5 bg-[#D7D7D7] rounded-full"></div>
                  <span className="text-base font-normal text-[#2D5BFF]">
                    {bus.remainingTickets} {t("bus.emptySeats")}
                  </span>
                </div>

                {/* Right: Price */}
                <div className="text-xl font-extrabold text-[#FF7F37]">
                  {bus.price.toLocaleString("vi-VN")}đ
                </div>
              </div>
            </div>
          </Link>{" "}
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:flex gap-3 p-4">
          {/* Bus Image */}
          <div className="w-32 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0 relative self-stretch">
            {!imageLoaded && <Skeleton className="w-full h-full rounded-lg" />}
            <img
              src={
                imageError
                  ? "/placeholder-bus.png"
                  : bus.image || "/placeholder-bus.png"
              }
              alt={bus.name}
              className={`w-full h-full object-cover transition-opacity duration-200 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* Trip Information Section */}
            <div className="flex justify-between gap-6 mb-3">
              {/* Left: Company and Route Info */}
              <div className="flex-1 min-w-0">
                {/* Company Name */}
                <h3 className="text-xl font-extrabold text-[#181818] leading-[1.4] mb-3">
                  {bus.name}
                </h3>

                {/* Time and Route */}
                <div className="flex flex-col gap-2">
                  {/* Time Row */}
                  <div className="flex items-center gap-4">
                    <span className="text-2xl font-extrabold text-[#181818] leading-[1.33]">
                      {bus.departureTime}
                    </span>
                    <div className="flex items-center gap-1 flex-1">
                      <div className="w-4 h-4 bg-[#2D5BFF] rounded-full"></div>
                      <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                      <span className="text-base font-normal text-[#5C5C5C] leading-[1.25]">
                        {bus.duration}
                      </span>
                      <div className="border-t border-[#D7D7D7] border-dashed flex-1"></div>
                      <MapPinIcon className="w-4 h-4 text-[#FF7F37]" />
                    </div>
                    <span className="text-2xl font-extrabold text-[#181818] leading-[1.33]">
                      {bus.arrivalTime}{" "}
                      <span className="text-xs ">
                        (
                        {formatNexDate(
                          getCrossedDays(
                            String(searchParams?.departureDate),
                            bus.departureTime,
                            bus.duration
                          )
                        )}
                        )
                      </span>
                    </span>
                  </div>

                  {/* Location Row */}
                  <div className="flex justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-normal text-[#181818] leading-[1.29]">
                        {bus.departureLocation}
                      </p>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-normal text-[#181818] leading-[1.29] text-right">
                        {bus.arrivalLocation}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right: Seat Type and Price */}
              <div className="flex flex-col justify-between items-end gap-3 flex-shrink-0">
                {/* Seat Info */}
                <div className="flex items-center gap-2">
                  <span className="text-base font-normal text-[#181818] leading-[1.25]">
                    {formatBusType(bus.bus.bus_type)}
                  </span>
                  <div className="w-1.5 h-1.5 bg-[#D7D7D7] rounded-full"></div>
                  <span className="text-base font-normal text-[#2D5BFF] leading-[1.25]">
                    {bus.remainingTickets} {t("bus.emptySeats")}
                  </span>
                </div>

                {/* Price */}
                <div className="text-xl font-extrabold text-[#FF7F37] leading-[1.4] text-right">
                  {bus.price.toLocaleString("vi-VN")}đ
                </div>
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-[#EDEDED] mb-3"></div>

            {/* Bottom Section - Tabs and Button */}
            <div className="flex justify-between items-center gap-4">
              {/* Detail Tabs */}
              <div className="flex">
                {[
                  { key: "schedule", label: t("bus.schedule") },
                  { key: "policy", label: t("bus.policy") },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => handleDetailTabChange(tab.key as any)}
                    className={clsx(
                      "py-2 px-4 text-sm font-bold leading-[1.29] text-center transition-colors",
                      activeDetailTab === tab.key
                        ? "text-[#2D5BFF] border-b-2 border-[#2D5BFF]"
                        : "text-[#5C5C5C] hover:text-[#2D5BFF]"
                    )}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Select Button */}

              <button onClick={handleSelectBus}>
                <Link
                  to="/booking/checkout"
                  search={checkoutSearchParams}
                  className={clsx(
                    "px-6 py-2 rounded-lg text-sm font-extrabold leading-[1.14] tracking-[0.02em] text-center transition-all flex-shrink-0",
                    isHighlighted
                      ? "bg-[#2D5BFF] text-white hover:bg-blue-700"
                      : "bg-[#ECF0FF] text-[#2D5BFF] hover:bg-[#2D5BFF] hover:text-white"
                  )}
                >
                  {t("bus.selectTrip")}
                </Link>
              </button>
            </div>
          </div>
        </div>

        {/* Tab Content Sections */}
        {activeDetailTab === "schedule" && (
          <ScheduleTab dropoffs={dropoffs} bus={bus} />
        )}
        {activeDetailTab === "policy" && <PolicyTab bus={bus} />}
      </div>
    );
  }
);

BusCard.displayName = "BusCard";

export default BusCard;
