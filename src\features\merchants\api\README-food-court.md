# Food Court API Documentation

## Overview

API implementation for fetching merchant menu data from the food court service.

## API Endpoint

```
GET https://dev.api.gtech-ecom.gone.vn/api/v1/food-court/merchants/{merchantId}/menu
```

## Files Structure

```
src/
├── api/merchants/
│   ├── food-court.types.ts    # TypeScript type definitions
│   ├── food-court.api.ts      # API client functions
│   └── index.ts              # Exports
├── hooks/
│   └── useFoodCourt.ts       # React Query hooks
└── components/examples/
    └── FoodCourtMenuExample.tsx # Usage example
```

## Types

### Main Response Type

```typescript
interface FoodCourtMenuResponse {
  merchant: FoodCourtMerchant;
  categories: MenuCategory[];
}
```

### Merchant Type

```typescript
interface FoodCourtMerchant {
  id: string;
  name: string;
  description: string;
  cover_image: FoodCourtImage;
  logo_image: FoodCourtImage;
  contact_phone: string;
  operating_hours_start: string;
  operating_hours_end: string;
}
```

### Food Item Type

```typescript
interface FoodItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  preparation_time_minutes: number;
}
```

## Usage Examples

### 1. Basic API Call

```typescript
import { getMerchantMenuById } from "@/api/merchants/food-court.api";

const fetchMenu = async () => {
  try {
    const menuData = await getMerchantMenuById(
      "2a329944-34a7-4d2b-85f8-901c44bade2d"
    );
    console.log(menuData);
  } catch (error) {
    console.error("Error fetching menu:", error);
  }
};
```

### 2. Using React Query Hook

```typescript
import { useGetMerchantMenuById } from "@/hooks/useFoodCourt";

const MenuComponent = () => {
  const { data, isLoading, error } = useGetMerchantMenuById("merchant-id");

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>{data?.merchant.name}</h1>
      {data?.categories.map(category => (
        <div key={category.id}>
          <h2>{category.name}</h2>
          {category.food_items.map(item => (
            <div key={item.id}>
              <h3>{item.name}</h3>
              <p>{item.price.toLocaleString()} VND</p>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};
```

### 3. With Options

```typescript
const { data, isLoading, error } = useGetMerchantMenuById("merchant-id", {
  enabled: true,
  refetchOnWindowFocus: false,
  retry: 3,
});
```

## Error Handling

The API includes proper error handling with typed error responses:

```typescript
interface FoodCourtApiError {
  message: string;
  code?: string;
  details?: any;
}
```

## Features

- ✅ Full TypeScript support
- ✅ React Query integration
- ✅ Error handling
- ✅ Caching with stale time (5 minutes)
- ✅ Automatic retries
- ✅ Query key factory
- ✅ Example component

## Query Keys

The hook uses structured query keys for optimal caching:

```typescript
foodCourtQueryKeys.menu(merchantId); // ['food-court', 'merchants', merchantId, 'menu']
```

## Testing the API

Use the example component `FoodCourtMenuExample` to test the integration:

```typescript
<FoodCourtMenuExample merchantId="2a329944-34a7-4d2b-85f8-901c44bade2d" />
```
