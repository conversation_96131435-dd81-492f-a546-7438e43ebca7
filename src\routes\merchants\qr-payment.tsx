import { useMemo } from "react";
import { createFileRoute, useNavi<PERSON> } from "@tanstack/react-router";
import { VietQRPopup } from "@/components/merchants-order-confirmation";
import { simpleCartSelectors } from "@/stores/simpleCartStore";

export const Route = createFileRoute("/merchants/qr-payment")({
  component: QRPaymentPage,
  validateSearch: (search: Record<string, unknown>) => ({
    qr: typeof search.qr === "string" ? search.qr : undefined,
    expiry: typeof search.expiry === "string" ? search.expiry : undefined,
  }),
});

function formatExpiry(dateStr?: string) {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  const h = d.getHours().toString().padStart(2, "0");
  const m = d.getMinutes().toString().padStart(2, "0");
  const weekday = [
    "Chủ nhật",
    "Thứ 2",
    "Th<PERSON> 3",
    "<PERSON><PERSON><PERSON> 4",
    "Th<PERSON> 5",
    "Th<PERSON> 6",
    "Th<PERSON> 7",
  ][d.getDay()];
  const day = d.getDate();
  const month = d.getMonth() + 1;
  return `Mã sẽ hết hạn vào lúc ${h}:${m} ${weekday}, ${day}/${month}`;
}

function QRPaymentPage() {
  const navigate = useNavigate();
  const { qr, expiry } = Route.useSearch();

  // Dữ liệu QR mẫu (có thể lấy từ API nếu cần)
  const vietQRData = {
    accountNumber: "***********",
    accountName: "Nguyen Van A",
    bank: "VIB",
    qrImage: "/assets/qr-fake.png",
    content: "663FGCS",
  };

  const expiryText = useMemo(() => formatExpiry(expiry), [expiry]);

  return (
    <div className="min-h-screen flex">
      <VietQRPopup
        totalPrice={simpleCartSelectors
          .getAllItems()
          .reduce((sum, item) => sum + item.price * item.quantity, 0)}
        show={true}
        onClose={() =>
          navigate({ to: "/merchants", search: { qr: qr ?? undefined } })
        }
        qrData={vietQRData}
        expiryText={expiryText}
        onDownloadQR={() => {
          const link = document.createElement("a");
          link.href = vietQRData.qrImage;
          link.download = "vietqr.png";
          link.click();
        }}
        onConfirmTransfer={() => {
          navigate({
            to: "/merchants/check-payment-status",
            search: {
              qr: qr ?? undefined,
              expiry,
            },
          });
        }}
      />
    </div>
  );
}
