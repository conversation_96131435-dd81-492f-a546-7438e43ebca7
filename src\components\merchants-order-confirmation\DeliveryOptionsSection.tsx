import React from "react";
import { cn } from "@/lib/utils";
import {
  DeliveryTypeEnum,
  PaymentTypeEnum,
} from "@/features/merchants/constants";
import type { DeliveryOption } from "@/shared/types/order-confirmation.types";
import { QrCodeIcon, BanknotesIcon } from "@heroicons/react/24/outline";

interface DeliveryOptionsSectionProps {
  deliveryOption: DeliveryOption;
  onDeliveryOptionChange: (option: DeliveryOption) => void;
  disabled?: boolean;
}

export const DeliveryOptionsSection: React.FC<DeliveryOptionsSectionProps> = ({
  deliveryOption,
  onDeliveryOptionChange,
  disabled = false,
}) => {
  return (
    <div className="bg-white rounded-xl border border-[#EDEDED] p-3">
      <div className="flex items-center gap-8">
        <label className="flex items-center gap-4">
          <div className="relative">
            <input
              type="radio"
              name="delivery"
              value={DeliveryTypeEnum.TABLE_DELIVERY}
              checked={deliveryOption === DeliveryTypeEnum.TABLE_DELIVERY}
              onChange={(e) =>
                onDeliveryOptionChange(e.target.value as DeliveryOption)
              }
              className="sr-only"
              disabled={disabled}
            />
            <div
              className={cn(
                "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                deliveryOption === DeliveryTypeEnum.TABLE_DELIVERY
                  ? "border-[#2D5BFF] bg-[#2D5BFF]"
                  : "border-[#D7D7D7] bg-white"
              )}
            >
              {deliveryOption === DeliveryTypeEnum.TABLE_DELIVERY && (
                <div className="w-3 h-3 rounded-full bg-white" />
              )}
            </div>
          </div>
          <span className="text-base font-normal text-[#181818]">
            Giao tới bàn
          </span>
        </label>

        <label className="flex items-center gap-4">
          <div className="relative">
            <input
              type="radio"
              name="delivery"
              value={DeliveryTypeEnum.SELF_PICKUP}
              checked={deliveryOption === DeliveryTypeEnum.SELF_PICKUP}
              onChange={(e) =>
                onDeliveryOptionChange(e.target.value as DeliveryOption)
              }
              className="sr-only"
              disabled={disabled}
            />
            <div
              className={cn(
                "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                deliveryOption === DeliveryTypeEnum.SELF_PICKUP
                  ? "border-[#2D5BFF] bg-[#2D5BFF]"
                  : "border-[#D7D7D7] bg-white"
              )}
            >
              {deliveryOption === DeliveryTypeEnum.SELF_PICKUP && (
                <div className="w-3 h-3 rounded-full bg-white" />
              )}
            </div>
          </div>
          <span className="text-base font-normal text-[#181818]">
            Tự đến lấy
          </span>
        </label>
      </div>
    </div>
  );
};

interface PaymentOptionsSectionProps {
  paymentType: PaymentTypeEnum;
  onPaymentTypeChange: (type: PaymentTypeEnum) => void;
  disabled?: boolean;
}

export const PaymentOptionsSection: React.FC<PaymentOptionsSectionProps> = ({
  paymentType,
  onPaymentTypeChange,
  disabled = false,
}) => {
  return (
    <div className="flex items-center justify-between gap-4">
      {/* Thanh toán tiền mặt */}
      <button
        type="button"
        disabled={disabled}
        onClick={() => onPaymentTypeChange(PaymentTypeEnum.CASH)}
        className={
          `flex flex-1 items-center justify-center text-start gap-2 p-3 rounded-xl transition-all duration-200 border ` +
          (paymentType === PaymentTypeEnum.CASH
            ? "border-[#2D5BFF] bg-white"
            : "border-[#EDEDED] text-gray-500 bg-white")
        }
      >
        <BanknotesIcon
          className={`w-10 h-10 ${paymentType === PaymentTypeEnum.CASH ? "text-[#2D5BFF]" : "text-gray-500"}`}
        />
        <span
          className={`text-base ${paymentType === PaymentTypeEnum.CASH ? "font-bold" : "font-normal"} text-start`}
        >
          Thanh toán tiền mặt
        </span>
      </button>
      {/* Thanh toán online */}
      <button
        type="button"
        disabled={disabled}
        onClick={() => onPaymentTypeChange(PaymentTypeEnum.CARD)}
        className={
          `flex flex-1 items-center justify-center text-start gap-2 p-3 rounded-xl transition-all duration-200 border ` +
          (paymentType === PaymentTypeEnum.CARD
            ? "border-[#2D5BFF] bg-white"
            : "border-[#EDEDED] text-gray-500 bg-white")
        }
      >
        <QrCodeIcon
          className={`w-10 h-10 ${paymentType === PaymentTypeEnum.CARD ? "text-[#2D5BFF]" : "text-gray-500"}`}
        />
        <span
          className={`text-base ${paymentType === PaymentTypeEnum.CARD ? "font-bold" : "font-normal"} text-start`}
        >
          Thanh toán online
        </span>
      </button>
    </div>
  );
};
