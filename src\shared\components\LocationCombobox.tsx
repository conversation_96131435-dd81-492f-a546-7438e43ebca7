import React, { useState, useMemo, useCallback, useRef } from "react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/shared/components/popover";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { LocationModal } from "@/shared/components/LocationModal";
import { useIsMobile } from "@/shared/hooks/useMediaQuery";

interface Place {
  id: number;
  name: string;
  code: string;
}

interface LocationComboboxProps {
  places: Place[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  error?: string | null;
  excludedValue?: string; // Value to exclude from selection
  excludedLabel?: string; // Label to show for excluded item
  onAutoOpen?: () => void; // Callback when this field should trigger auto-open of another field
  disabled?: boolean; // Disable the combobox
}

export interface LocationComboboxRef {
  openDropdown: () => void;
  closeDropdown: () => void;
}

export const LocationCombobox = React.forwardRef<
  LocationComboboxRef,
  LocationComboboxProps
>(
  (
    {
      places,
      value,
      onValueChange,
      placeholder,
      label,
      className,
      error,
      excludedValue,
      excludedLabel,
      onAutoOpen,
      disabled,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    const [focused, setFocused] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const isMobile = useIsMobile();

    // Expose imperative methods via ref
    React.useImperativeHandle(
      ref,
      () => ({
        openDropdown: () => {
          if (isMobile) {
            setIsModalOpen(true);
          } else {
            inputRef.current?.focus();
            setOpen(true);
            setFocused(true);
          }
        },
        closeDropdown: () => {
          setOpen(false);
          setIsModalOpen(false);
          setFocused(false);
        },
      }),
      [isMobile]
    );

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        if (disabled) return;
        onValueChange(e.target.value);
        if (!open && !isMobile) setOpen(true);
      },
      [onValueChange, open, isMobile, disabled]
    );

    const handleSelectPlace = useCallback(
      (place: Place, e?: React.MouseEvent) => {
        if (disabled) return;
        e?.preventDefault();
        e?.stopPropagation();

        // Don't allow selecting excluded value
        if (excludedValue && place.name === excludedValue) {
          return;
        }

        onValueChange(place.name);
        setOpen(false);

        // Delay blur to ensure the popover closes properly
        setTimeout(() => {
          inputRef.current?.blur();
          setFocused(false);

          // Trigger auto-open callback after selection
          if (onAutoOpen) {
            onAutoOpen();
          }
        }, 100);
      },
      [onValueChange, excludedValue, onAutoOpen, disabled]
    );

    const handleContainerClick = useCallback(
      (e: React.MouseEvent) => {
        if (disabled) return;
        e.preventDefault();
        e.stopPropagation();

        if (isMobile) {
          // Open modal on mobile
          setIsModalOpen(true);
        } else {
          // Always focus the input and open dropdown on desktop
          inputRef.current?.focus();
          setOpen(true);
          setFocused(true);
        }
      },
      [isMobile, disabled]
    );

    const handleInputFocus = useCallback(() => {
      if (disabled) return;
      setFocused(true);
      if (!isMobile) {
        setOpen(true);
      }
    }, [isMobile, disabled]);

    const handleInputBlur = useCallback(() => {
      // Only blur if not clicking on dropdown items
      setTimeout(() => {
        setFocused(false);
        // Don't close immediately, let the popover handle it
      }, 150);
    }, []);

    const handleOpenChange = useCallback(
      (newOpen: boolean) => {
        if (!isMobile) {
          setOpen(newOpen);
          if (!newOpen) {
            setFocused(false);
          }
        }
      },
      [isMobile]
    );

    const handleModalClose = useCallback(() => {
      setIsModalOpen(false);
    }, []);

    const handleModalValueChange = useCallback(
      (selectedValue: string) => {
        // Don't allow selecting excluded value
        if (excludedValue && selectedValue === excludedValue) {
          return;
        }

        onValueChange(selectedValue);
        setIsModalOpen(false);

        // Trigger auto-open callback after selection
        if (onAutoOpen) {
          setTimeout(() => {
            onAutoOpen();
          }, 100);
        }
      },
      [onValueChange, excludedValue, onAutoOpen]
    );

    const handleClearValue = useCallback(
      (e: React.MouseEvent) => {
        if (disabled) return;
        e.preventDefault();
        e.stopPropagation();
        onValueChange("");
        if (!isMobile) {
          inputRef.current?.focus();
          setOpen(true);
          setFocused(true);
        }
      },
      [onValueChange, isMobile, disabled]
    );

    // Filter for desktop dropdown only - based on current input value
    const filteredPlaces = useMemo(() => {
      if (!value.trim()) return places;
      return places.filter(
        (place) =>
          place.name.toLowerCase().includes(value.toLowerCase()) ||
          place.code?.toLowerCase().includes(value.toLowerCase())
      );
    }, [places, value]);

    return (
      <div className={cn("relative flex-1", className)}>
        {/* Desktop Dropdown */}
        {!isMobile && (
          <Popover open={open && !disabled} onOpenChange={handleOpenChange}>
            <PopoverTrigger asChild>
              <div
                ref={containerRef}
                onClick={handleContainerClick}
                className={cn(
                  "h-[50px] px-[16px] bg-gray-100 rounded-lg flex flex-col justify-center border transition-all relative",
                  disabled
                    ? "cursor-not-allowed bg-gray-100 border-gray-200 opacity-60"
                    : "cursor-text",
                  error && !disabled
                    ? "bg-red-50 border-red-500"
                    : focused && !disabled
                      ? "bg-white border-[#FF7F37] shadow-sm"
                      : !disabled && "border-transparent hover:border-gray-300"
                )}
              >
                {label && (
                  <label
                    className={cn(
                      "block text-xs font-bold leading-4 mb-1",
                      disabled
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-[#5C5C5C] cursor-text"
                    )}
                  >
                    {label}
                  </label>
                )}
                <div className="flex items-center w-full">
                  <input
                    ref={inputRef}
                    type="text"
                    value={value}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    placeholder={placeholder}
                    disabled={disabled}
                    readOnly={disabled}
                    className={cn(
                      "w-full bg-transparent outline-none text-sm font-normal leading-[18px]",
                      disabled
                        ? "cursor-not-allowed text-gray-500"
                        : "cursor-text",
                      error && value && !disabled
                        ? "text-red-600"
                        : error && !value && !disabled
                          ? "text-red-400 placeholder:text-red-400"
                          : value && !disabled
                            ? "text-black"
                            : !disabled &&
                              "text-[#747474] placeholder:text-[#747474]"
                    )}
                  />
                  {value && !disabled && (
                    <button
                      type="button"
                      onClick={handleClearValue}
                      className="p-1 hover:text-[#FF7F37] transition-colors"
                      tabIndex={-1}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent
              className="w-full p-0 rounded-t-none"
              align="start"
              alignOffset={-16}
              side="bottom"
              sideOffset={5}
              style={{ width: containerRef.current?.offsetWidth }}
            >
              <div className="max-h-[300px] overflow-y-auto bg-white rounded-lg shadow-lg border border-t-0">
                <div className="px-3 py-2 text-sm font-semibold text-gray-900 bg-gray-50 border-b">
                  {t("search.placesTitle")}
                </div>
                {filteredPlaces.length === 0 ? (
                  <div className="px-3 py-4 text-sm text-gray-500 text-center">
                    {t("search.noPlacesFound")}
                  </div>
                ) : (
                  filteredPlaces.map((place) => {
                    const isExcluded =
                      excludedValue && place.name === excludedValue;
                    return (
                      <div
                        key={place.id}
                        onMouseDown={
                          isExcluded
                            ? undefined
                            : (e) => handleSelectPlace(place, e)
                        }
                        className={cn(
                          "px-3 py-3 flex items-center",
                          isExcluded
                            ? "cursor-not-allowed bg-gray-50 opacity-60"
                            : "cursor-pointer hover:bg-blue-50"
                        )}
                      >
                        <CheckIcon
                          className={cn(
                            "mr-3 h-4 w-4 text-blue-600",
                            value === place.name ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex-1">
                          <div
                            className={cn(
                              "font-medium",
                              isExcluded ? "text-gray-400" : "text-gray-900"
                            )}
                          >
                            {place.name}
                          </div>
                          {isExcluded && excludedLabel && (
                            <div className="text-xs text-gray-400 mt-1">
                              {excludedLabel}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* Mobile Field (triggers modal) */}
        {isMobile && (
          <div
            onClick={handleContainerClick}
            className={cn(
              "h-[50px] px-[16px] bg-gray-100 rounded-lg flex flex-col justify-center border transition-all relative",
              disabled
                ? "cursor-not-allowed bg-gray-100 border-gray-200 opacity-60"
                : "cursor-pointer",
              error && !disabled
                ? "bg-red-50 border-red-500"
                : !disabled && "border-transparent hover:border-gray-300"
            )}
          >
            {label && (
              <label
                className={cn(
                  "block text-xs font-bold leading-4 mb-1 pointer-events-none",
                  disabled ? "text-gray-400" : "text-[#5C5C5C]"
                )}
              >
                {label}
              </label>
            )}
            <div className="flex items-center justify-between">
              <div
                className={cn(
                  "text-sm font-normal leading-[18px]",
                  disabled
                    ? "text-gray-500"
                    : error && value
                      ? "text-red-600"
                      : error && !value
                        ? "text-red-400"
                        : value
                          ? "text-black"
                          : "text-[#747474]"
                )}
              >
                {value || placeholder}
              </div>
              {value && !disabled && (
                <button
                  type="button"
                  onClick={handleClearValue}
                  className="p-1 hover:text-[#FF7F37] transition-colors"
                  tabIndex={-1}
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        )}

        {/* Mobile Modal - Pass all places, not filtered */}
        {!disabled && (
          <LocationModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
            places={places}
            value={value}
            onValueChange={handleModalValueChange}
            title={label || "Chọn điểm"}
            placeholder={placeholder}
            excludedValue={excludedValue}
            excludedLabel={excludedLabel}
          />
        )}
      </div>
    );
  }
);
