{"app": {"title": "Super Booking App", "description": "Your optimal booking solution"}, "navigation": {"home": "Home", "schedule": "Schedule", "search": "Ticket Search", "news": "News", "contact": "Contact", "about": "About", "login": "<PERSON><PERSON>", "register": "Register"}, "header": {"home": "Home", "schedule": "Schedule", "search": "Ticket Search", "promotions": "Packages", "news": "News", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register"}, "home": {"hero": {"title1": "BOOK TICKETS", "title2": "DEPART NOW", "subtitle": "easy, fast, convenient"}, "form": {"oneWay": "One way", "roundTrip": "Round trip", "guide": "Ticket purchase guide", "departure": "Departure", "departurePlaceholder": "Where do you want to go from?", "destination": "Destination", "destinationPlaceholder": "Where do you want to go?", "departureDate": "Departure date", "returnDate": "Return date", "numberOfTickets": "Tickets", "search": "FIND BUSES", "selectDate": "Select date", "selectedAsDestination": "Selected as destination", "selectedAsDeparture": "Selected as departure"}, "promotions": "Promotions", "news": "News"}, "bus": {"schedule": "Schedule", "policy": "Policy", "selectTrip": "Select trip", "selected": "Selected", "availableSeats": "seats available", "remainingTickets": "tickets remaining", "emptySeats": "empty seats", "sleeperBus": "Sleeper bus", "scheduleWarning": "The times of schedule milestones are estimated times. This schedule may change depending on the actual situation of early or late departure.", "policyNotAvailable": "Policy not available.", "durationUnknown": "Unknown", "noTripsFound": "No trips found matching your search criteria.", "invalidLocation": "Invalid location", "amenities": {"schedule": "Schedule", "transfer": "Transfer", "policy": "Policy"}}, "search": {"noPlacesFound": "No places found.", "placesTitle": "Province/City", "departureTrip": "Departure trip", "returnTrip": "Return trip", "busesCount": "buses", "missingSearchInfo": "Missing search information. Please perform a new search.", "newSearch": "New search", "updateSearch": "Update search"}, "filters": {"departureTime": "Departure time", "searchFilters": "Search filters", "sortLabel": "Sort", "busType": "Bus type", "sort": {"default": "<PERSON><PERSON><PERSON>", "earliest": "Earliest departure", "latest": "Latest departure", "cheapest": "Price ascending", "most_expensive": "Price descending"}}, "time": {"hoursAgo": "hours ago", "daysAgo": "days ago", "weeksAgo": "weeks ago"}, "common": {"loading": "Loading...", "searchingBuses": "Searching for buses...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "done": "Done", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset"}, "booking": {"title": "Booking", "new": "New Booking", "edit": "Edit Booking", "details": "Booking Details", "date": "Date", "time": "Time", "duration": "Duration", "status": "Status", "confirmed": "Confirmed", "pending": "Pending", "cancelled": "Cancelled"}, "form": {"validation": {"required": "This field is required", "email": "Please enter a valid email", "phone": "Please enter a valid phone number", "date": "Please select a valid date", "time": "Please select a valid time", "selectDeparture": "Please select departure location", "selectDestination": "Please select destination", "selectDepartureDate": "Please select departure date", "selectReturnDate": "Please select return date", "enterTickets": "Please enter number of tickets", "ticketsGreaterThanZero": "Tickets > 0", "enterTicketNumber": "Enter ticket number", "departureDatePast": "Departure date cannot be in the past", "returnDateAfterDeparture": "Return date must be after departure date"}}, "language": {"switch": "Switch Language", "english": "English", "vietnamese": "Tiếng <PERSON>"}}