"use client";

import React from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { Button } from "@/shared/components/button";
import { Calendar } from "@/shared/components/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/shared/components/popover";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { DateModal } from "@/shared/components/DateModal";
import { useIsMobile } from "@/shared/hooks/useMediaQuery";
import { useSearchForm } from "@/contexts/SearchFormContext";

interface DatePickerProps {
  date?: Date;
  onDateChange?: (date: Date | undefined) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  error?: string | null;
  disabled?: (date: Date) => boolean;
  fromDate?: Date;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  // Optional override props - if not provided, will use SearchFormContext
  showTabs?: boolean;
  isRoundTrip?: boolean;
  onRoundTripToggle?: () => void;
  // New props for displaying reference date
  referenceDate?: Date;
  referenceDateLabel?: string;
  // Tab to open when modal opens
  // initialActiveTab?: "departure" | "return";
}

export interface DatePickerRef {
  openCalendar: () => void;
  closeCalendar: () => void;
}

export const DatePicker = React.forwardRef<DatePickerRef, DatePickerProps>(
  (
    {
      date,
      onDateChange,
      placeholder,
      label,
      className,
      error,
      disabled,
      fromDate,
      open: controlledOpen,
      onOpenChange,
      showTabs,
      isRoundTrip,
      onRoundTripToggle,
      referenceDate,
      referenceDateLabel,
      // initialActiveTab,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [internalOpen, setInternalOpen] = React.useState(false);
    const [isModalOpen, setIsModalOpen] = React.useState(false);
    const buttonRef = React.useRef<HTMLButtonElement>(null);
    const isMobile = useIsMobile();

    // Get SearchFormContext values as fallback
    const searchFormContext = useSearchForm();

    // Use provided props or fallback to SearchFormContext
    const modalShowTabs = showTabs ?? isMobile; // Enable tabs on mobile by default
    const modalIsRoundTrip = isRoundTrip ?? searchFormContext.state.isRoundTrip;
    const modalOnRoundTripToggle =
      onRoundTripToggle ??
      (() =>
        searchFormContext.setIsRoundTrip(!searchFormContext.state.isRoundTrip));

    // Use controlled open if provided, otherwise use internal state
    const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
    const setOpen = React.useCallback(
      (newOpen: boolean) => {
        if (isMobile) {
          setIsModalOpen(newOpen);
        } else {
          if (onOpenChange) {
            onOpenChange(newOpen);
          } else {
            setInternalOpen(newOpen);
          }

          // Clear focus khi calendar đóng
          if (!newOpen) {
            // Blur button để tránh focus state
            setTimeout(() => {
              buttonRef.current?.blur();
            }, 100);
          }
        }
      },
      [onOpenChange, isMobile]
    );

    // Expose imperative methods via ref
    React.useImperativeHandle(
      ref,
      () => ({
        openCalendar: () => setOpen(true),
        closeCalendar: () => setOpen(false),
      }),
      [setOpen]
    );

    const defaultPlaceholder = placeholder || t("home.form.selectDate");
    const hasError = !!error;

    const handleContainerClick = React.useCallback(() => {
      if (isMobile) {
        setIsModalOpen(true);
      } else if (!isOpen) {
        setOpen(true);
      }
    }, [isMobile, isOpen, setOpen]);

    const handleModalClose = React.useCallback(() => {
      setIsModalOpen(false);
    }, []);

    return (
      <div className={cn("relative", className)}>
        {/* Desktop Popover */}
        {!isMobile && (
          <div
            onClick={handleContainerClick}
            className={cn(
              "h-[50px] px-[16px] rounded-lg flex flex-col justify-center relative border transition-all cursor-pointer",
              hasError
                ? "bg-red-50 border-red-500"
                : isOpen
                  ? "bg-white border-[#FF7F37] shadow-sm"
                  : "bg-gray-100 border-transparent hover:border-gray-300"
            )}
          >
            {label && (
              <label className="block text-xs text-[#5C5C5C] font-bold leading-4 mb-1 pointer-events-none">
                {label}
              </label>
            )}
            <Popover open={isOpen} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  ref={buttonRef}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-left font-normal p-0 h-auto bg-transparent hover:bg-transparent focus:ring-0 focus:ring-offset-0",
                    hasError && date
                      ? "text-red-600"
                      : hasError && !date
                        ? "text-red-400"
                        : date
                          ? "text-black"
                          : "text-[#747474]"
                  )}
                >
                  {date ? (
                    format(date, "dd/MM/yyyy", { locale: vi })
                  ) : (
                    <span
                      className={cn(
                        "text-sm font-normal leading-[18px]",
                        hasError ? "text-red-400" : "text-[#747474]"
                      )}
                    >
                      {defaultPlaceholder}
                    </span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0"
                align="start"
                alignOffset={-16}
                side="bottom"
                sideOffset={5}
              >
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(selectedDate) => {
                    onDateChange?.(selectedDate);
                    setOpen(false);
                  }}
                  disabled={disabled}
                  fromDate={fromDate}
                  initialFocus
                  locale={vi}
                  className="rounded-md border"
                  modifiers={{
                    reference: referenceDate ? [referenceDate] : [],
                  }}
                  modifiersClassNames={{
                    reference: "bg-orange-100 text-orange-700 font-medium",
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>
        )}

        {/* Mobile Field (triggers modal) */}
        {isMobile && (
          <div
            onClick={handleContainerClick}
            className={cn(
              "h-[50px] px-[16px] bg-gray-100 rounded-lg flex flex-col justify-center cursor-pointer border transition-all",
              hasError
                ? "bg-red-50 border-red-500"
                : "border-transparent hover:border-gray-300"
            )}
          >
            {label && (
              <label className="block text-xs text-[#5C5C5C] font-bold leading-4 mb-1 pointer-events-none">
                {label}
              </label>
            )}
            <div
              className={cn(
                "text-sm font-normal leading-[18px]",
                hasError && date
                  ? "text-red-600"
                  : hasError && !date
                    ? "text-red-400"
                    : date
                      ? "text-black"
                      : "text-[#747474]"
              )}
            >
              {date
                ? format(date, "dd/MM/yyyy", { locale: vi })
                : defaultPlaceholder}
            </div>
          </div>
        )}

        {/* Mobile Modal */}
        <DateModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          date={date}
          onDateChange={onDateChange || (() => {})}
          title={label || "Chọn ngày"}
          showTabs={modalShowTabs}
          isRoundTrip={modalIsRoundTrip}
          onRoundTripToggle={modalOnRoundTripToggle}
          disabled={disabled}
          fromDate={fromDate}
          referenceDate={referenceDate}
          referenceDateLabel={referenceDateLabel}
          // initialActiveTab={initialActiveTab}
        />
      </div>
    );
  }
);

DatePicker.displayName = "DatePicker";
