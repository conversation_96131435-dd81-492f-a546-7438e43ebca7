import { axiosInstanceStrapi } from "@/shared/api/axiosInstance";
import type { StrapiDiscountsResponse, StrapiDiscount } from "@/shared/types/strapi";

/**
 * Fetch discounts from Strapi CMS
 */
export const getDiscounts = async (): Promise<StrapiDiscountsResponse> => {
  const response = await axiosInstanceStrapi.get("discounts");
  return response.data;
};

/**
 * Fetch a single discount by ID from Strapi CMS
 */
export const getDiscountById = async (id: string): Promise<StrapiDiscount> => {
  const response = await axiosInstanceStrapi.get(`discounts/${id}`);
  return response.data;
};
