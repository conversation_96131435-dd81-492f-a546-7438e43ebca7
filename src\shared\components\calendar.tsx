"use client";

import * as React from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { DayPicker } from "react-day-picker";
import { clsx } from "clsx";
import { vi } from "date-fns/locale";

// Simple utility function for class names
const cn = (...classes: (string | undefined)[]) => clsx(classes);

// Button variants specifically for calendar
// const calendarButtonVariants = ({
//   variant = "default",
//   size = "default",
// }: { variant?: string; size?: string } = {}) => {
//   const variants = {
//     default: "bg-blue-600 hover:bg-blue-700 text-white",
//     outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-900",
//     ghost: "hover:bg-gray-100 text-gray-900",
//   };

//   const sizes = {
//     default: "px-3 py-2 text-sm",
//     sm: "px-2 py-1 text-xs",
//   };

//   const baseClasses =
//     "font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500";

//   return clsx(
//     baseClasses,
//     variants[variant as keyof typeof variants] || variants.default,
//     sizes[size as keyof typeof sizes] || sizes.default
//   );
// };

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: React.ComponentProps<typeof DayPicker>) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      locale={vi}
      className={cn("p-4", className)}
      classNames={{
        months: "flex flex-col sm:flex-row gap-2",
        month: "flex flex-col gap-4",
        caption: "flex justify-center pt-1 relative items-center w-full mb-4",
        caption_label: "text-base font-semibold text-blue-600",
        nav: "flex items-center gap-1",
        nav_button: cn(
          "h-8 w-8 bg-transparent p-0 opacity-70 hover:opacity-100 hover:bg-blue-50 rounded-full flex items-center justify-center transition-all"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse",
        head_row: "flex mb-2",
        head_cell:
          "text-gray-600 rounded-md w-10 h-8 font-medium text-sm flex items-center justify-center",
        row: "flex w-full",
        cell: cn(
          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 w-10 h-10",
          props.mode === "range"
            ? "first:rounded-l-md last:rounded-r-md"
            : "rounded-md"
        ),
        day: cn(
          "h-10 w-10 p-0 font-normal rounded-md hover:bg-blue-50 transition-colors flex items-center justify-center text-sm"
        ),
        day_range_start:
          "day-range-start bg-blue-600 text-white hover:bg-blue-700",
        day_range_end: "day-range-end bg-blue-600 text-white hover:bg-blue-700",
        day_selected:
          "bg-blue-600 text-white hover:bg-blue-700 focus:bg-blue-600 font-semibold",
        day_today: "bg-blue-100 text-blue-900 font-semibold",
        day_outside: "day-outside text-gray-400 opacity-50",
        day_disabled: "text-gray-400 opacity-30 cursor-not-allowed",
        day_range_middle: "bg-blue-100 text-blue-900",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ className, ...props }) => (
          <ChevronLeftIcon
            className={cn("h-5 w-5 text-blue-600", className)}
            {...props}
          />
        ),
        IconRight: ({ className, ...props }) => (
          <ChevronRightIcon
            className={cn("h-5 w-5 text-blue-600", className)}
            {...props}
          />
        ),
      }}
      formatters={{
        formatCaption: (date) => {
          return new Intl.DateTimeFormat("vi-VN", {
            month: "long",
            year: "numeric",
          }).format(date);
        },
        formatWeekdayName: (date) => {
          const weekdays = ["CN", "T2", "T3", "T4", "T5", "T6", "T7"];
          return weekdays[date.getDay()];
        },
      }}
      {...props}
    />
  );
}

export { Calendar };
