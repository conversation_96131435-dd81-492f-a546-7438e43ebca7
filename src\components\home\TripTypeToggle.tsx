import React from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { ToggleButton } from "@/shared/components/FormFieldSearch";
import { useSearchForm } from "../../contexts/SearchFormContext";

export const TripTypeToggle: React.FC = React.memo(() => {
  const { t } = useTranslation();
  const { state, setIsRoundTrip } = useSearchForm();

  return (
    <div className="flex bg-[#F4F6FB] rounded-lg p-1">
      <ToggleButton
        isActive={!state.isRoundTrip}
        onClick={() => setIsRoundTrip(false)}
      >
        {t("home.form.oneWay")}
      </ToggleButton>
      {/* <ToggleButton
        isActive={state.isRoundTrip}
        onClick={() => setIsRoundTrip(true)}
      >
        {t("home.form.roundTrip")}
      </ToggleButton> */}
    </div>
  );
});
