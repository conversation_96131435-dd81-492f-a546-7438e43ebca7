# Internationalization (i18n) Setup

This project uses `react-i18next` for internationalization with TypeScript support and TanStack Router integration.

## Features

- ✅ **Type-safe translations** with TypeScript
- ✅ **Language detection** (localStorage, browser, HTML tag)
- ✅ **Language persistence** in localStorage
- ✅ **HTTP backend** for loading translation files
- ✅ **Suspense support** for loading states
- ✅ **Custom hooks** for easy usage
- ✅ **Language switcher component**

## Supported Languages

- **English (en)** - Default language
- **Vietnamese (vi)** - Tiếng Việt

## File Structure

```
src/
├── lib/
│   └── i18n.ts                 # i18n configuration
├── hooks/
│   └── useTranslation.ts       # Custom translation hooks
├── components/
│   ├── LanguageSwitcher.tsx    # Language switcher component
│   └── I18nDemo.tsx           # Demo component
├── types/
│   └── i18next.d.ts           # TypeScript declarations
├── locales/
│   ├── en/
│   │   └── common.json        # English translations
│   └── vi/
│       └── common.json        # Vietnamese translations
└── routes/
    └── i18n-demo.tsx          # Demo route

public/
└── locales/
    ├── en/
    │   └── common.json        # English translations (HTTP backend)
    └── vi/
        └── common.json        # Vietnamese translations (HTTP backend)
```

## Usage

### Basic Translation

```tsx
import { useTranslation } from "@/hooks/useTranslation";

function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t("app.title")}</h1>
      <p>{t("app.description")}</p>
    </div>
  );
}
```

### Language Switching

```tsx
import { useLanguage } from "@/hooks/useTranslation";

function LanguageSelector() {
  const { currentLanguage, availableLanguages, changeLanguage } = useLanguage();

  return (
    <select
      value={currentLanguage}
      onChange={(e) => changeLanguage(e.target.value)}
    >
      {availableLanguages.map((lang) => (
        <option key={lang.code} value={lang.code}>
          {lang.name}
        </option>
      ))}
    </select>
  );
}
```

### Using the Language Switcher Component

```tsx
import { LanguageSwitcher } from "@/components/LanguageSwitcher";

function Header() {
  return (
    <header>
      <nav>
        {/* Other nav items */}
        <LanguageSwitcher className="ml-auto" />
      </nav>
    </header>
  );
}
```

## Translation Keys

### App Information

- `app.title` - Application title
- `app.description` - Application description

### Navigation

- `navigation.home` - Home
- `navigation.bookings` - Bookings
- `navigation.profile` - Profile
- `navigation.settings` - Settings
- `navigation.logout` - Logout

### Common Actions

- `common.loading` - Loading...
- `common.error` - Error
- `common.success` - Success
- `common.cancel` - Cancel
- `common.confirm` - Confirm
- `common.save` - Save
- `common.edit` - Edit
- `common.delete` - Delete
- `common.search` - Search
- `common.filter` - Filter
- `common.sort` - Sort
- `common.next` - Next
- `common.previous` - Previous
- `common.submit` - Submit
- `common.reset` - Reset

### Booking Related

- `booking.title` - Booking
- `booking.new` - New Booking
- `booking.edit` - Edit Booking
- `booking.details` - Booking Details
- `booking.date` - Date
- `booking.time` - Time
- `booking.duration` - Duration
- `booking.status` - Status
- `booking.confirmed` - Confirmed
- `booking.pending` - Pending
- `booking.cancelled` - Cancelled

### Form Validation

- `form.validation.required` - This field is required
- `form.validation.email` - Please enter a valid email
- `form.validation.phone` - Please enter a valid phone number
- `form.validation.date` - Please select a valid date
- `form.validation.time` - Please select a valid time

### Language Switching

- `language.switch` - Switch Language
- `language.english` - English
- `language.vietnamese` - Tiếng Việt

## Adding New Languages

1. **Create translation files:**

   ```bash
   mkdir -p src/locales/[lang-code]
   mkdir -p public/locales/[lang-code]
   ```

2. **Add translations:**

   ```json
   // src/locales/[lang-code]/common.json
   {
     "app": {
       "title": "Your App Title",
       "description": "Your app description"
     }
     // ... other translations
   }
   ```

3. **Copy to public directory:**

   ```bash
   cp src/locales/[lang-code]/common.json public/locales/[lang-code]/
   ```

4. **Update the language list:**
   ```typescript
   // src/hooks/useTranslation.ts
   const availableLanguages = [
     { code: "en", name: "English" },
     { code: "vi", name: "Tiếng Việt" },
     { code: "[lang-code]", name: "[Language Name]" }, // Add new language
   ];
   ```

## Adding New Translation Keys

1. **Add to English file:**

   ```json
   // src/locales/en/common.json
   {
     "newSection": {
       "newKey": "New translation"
     }
   }
   ```

2. **Add to all other language files:**

   ```json
   // src/locales/vi/common.json
   {
     "newSection": {
       "newKey": "Bản dịch mới"
     }
   }
   ```

3. **Copy to public directory:**

   ```bash
   cp src/locales/*/common.json public/locales/*/
   ```

4. **Use in components:**
   ```tsx
   const { t } = useTranslation();
   return <span>{t("newSection.newKey")}</span>;
   ```

## TypeScript Support

The setup includes full TypeScript support with:

- **Type-safe translation keys** - IntelliSense for all translation keys
- **Compile-time validation** - Errors for invalid translation keys
- **Auto-completion** - IDE support for translation keys

## Demo

Visit `/i18n-demo` to see the i18n setup in action with:

- Language switching
- All translation categories
- Real-time language updates
- Form examples with validation messages

## Best Practices

1. **Use nested keys** for better organization:

   ```json
   {
     "user": {
       "profile": {
         "name": "Name",
         "email": "Email"
       }
     }
   }
   ```

2. **Keep translations consistent** across all languages

3. **Use meaningful key names** that describe the content

4. **Group related translations** under common namespaces

5. **Always update all language files** when adding new keys

6. **Test with different languages** to ensure UI layout works

## Troubleshooting

### Translations not loading

- Check that translation files exist in `public/locales/[lang]/common.json`
- Verify the HTTP backend can access the files
- Check browser network tab for 404 errors

### TypeScript errors

- Ensure `src/types/i18next.d.ts` is properly configured
- Verify translation keys exist in the English translation file
- Check that the namespace is correctly specified

### Language not persisting

- Check localStorage in browser dev tools
- Verify language detection order in `src/lib/i18n.ts`
- Ensure the language code is valid
