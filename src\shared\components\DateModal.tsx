import React, { useState, useCallback, useRef, useEffect } from "react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { Dialog, DialogContent } from "@/shared/components/dialog";
import { Calendar } from "@/shared/components/calendar";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { useSearchForm } from "@/contexts/SearchFormContext";

interface DateModalProps {
  isOpen: boolean;
  onClose: () => void;
  date?: Date;
  onDateChange: (date: Date | undefined) => void;
  title: string;
  showTabs?: boolean;
  isRoundTrip?: boolean;
  onRoundTripToggle?: () => void;
  disabled?: (date: Date) => boolean;
  fromDate?: Date;
  // Optional separate dates for tabs - if not provided, will use SearchFormContext
  departureDate?: Date;
  returnDate?: Date;
  onDepartureDateChange?: (date: Date | undefined) => void;
  onReturnDateChange?: (date: Date | undefined) => void;
  // Reference date display
  referenceDate?: Date;
  referenceDateLabel?: string;
  // Initial active tab
  initialActiveTab?: "departure" | "return";
}

// Tab component for date modal
const DateTabs: React.FC<{
  activeTab: "departure" | "return";
  onTabChange: (tab: "departure" | "return") => void;
  departureDate?: Date;
  returnDate?: Date;
  isRoundTrip: boolean;
  onRoundTripToggle: () => void;
}> = ({
  activeTab,
  onTabChange,
  departureDate,
  returnDate,
  isRoundTrip,
  // onRoundTripToggle,
}) => {
  const formatTabDate = (date: Date) => {
    return format(date, "EEEE, dd/MM", { locale: vi });
  };

  return (
    <div className="flex justify-center items-center gap-2 py-2 px-1 pb-0 bg-white rounded-lg">
      {/* Departure Tab */}
      <div className="flex flex-col justify-center items-center min-w-[120px]">
        <button
          onClick={() => onTabChange("departure")}
          className={cn(
            "flex justify-center items-center self-stretch gap-3 py-[7px] px-4 text-center text-sm font-bold font-mulish transition-all min-w-[120px]",
            activeTab === "departure" ? "text-[#181818]" : "text-[#5C5C5C]"
          )}
        >
          <div className="flex flex-col items-center">
            <span className="leading-tight whitespace-nowrap">Ngày đi</span>
            {departureDate && (
              <span className="text-xs mt-1 font-normal leading-tight whitespace-nowrap">
                {formatTabDate(departureDate)}
              </span>
            )}
          </div>
        </button>
        {activeTab === "departure" && (
          <div className="w-full h-0.5 bg-[#2D5BFF] rounded-sm"></div>
        )}
      </div>

      {/* Return Tab - Only show when round trip */}
      {isRoundTrip && (
        <div className="flex flex-col justify-center items-center min-w-[120px]">
          <button
            onClick={() => onTabChange("return")}
            className={cn(
              "flex justify-center items-center self-stretch gap-3 py-[7px] px-4 text-center text-sm font-bold font-mulish transition-all min-w-[120px]",
              activeTab === "return" ? "text-[#181818]" : "text-[#5C5C5C]"
            )}
          >
            <div className="flex flex-col items-center">
              <span className="leading-tight whitespace-nowrap">Ngày về</span>
              {returnDate && (
                <span className="text-xs mt-1 font-normal leading-tight whitespace-nowrap">
                  {formatTabDate(returnDate)}
                </span>
              )}
            </div>
          </button>
          {activeTab === "return" && (
            <div className="w-full h-0.5 bg-[#2D5BFF] rounded-sm"></div>
          )}
        </div>
      )}
    </div>
  );
};

export const DateModal: React.FC<DateModalProps> = ({
  isOpen,
  onClose,
  date,
  onDateChange,
  showTabs = false,
  isRoundTrip = false,
  onRoundTripToggle,
  disabled,
  fromDate,
  departureDate,
  returnDate,
  onDepartureDateChange,
  onReturnDateChange,
  referenceDate,
  // referenceDateLabel,
  initialActiveTab,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<"departure" | "return">(
    initialActiveTab || "departure"
  );

  // Swipe gesture state
  const modalRef = useRef<HTMLDivElement>(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const startX = useRef<number>(0);
  const startY = useRef<number>(0);
  const isDragging = useRef<boolean>(false);

  // Reset swipe state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSwipeActive(false);
      setSwipeOffset(0);
      isDragging.current = false;
    } else {
      // Reset to initial active tab when modal opens
      // if (initialActiveTab) {
      //   setActiveTab(initialActiveTab);
      // }
    }
  }, [isOpen, initialActiveTab]);

  // Auto switch to departure tab when round trip is disabled
  // useEffect(() => {
  //   if (!isRoundTrip && activeTab === "return") {
  //     setActiveTab("departure");
  //   }
  // }, [isRoundTrip, activeTab]);

  // Touch event handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX;
    startY.current = e.touches[0].clientY;
    isDragging.current = false;
    setIsSwipeActive(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isSwipeActive) return;

    const currentX = e.touches[0].clientX;
    const currentY = e.touches[0].clientY;
    const deltaX = currentX - startX.current;
    const deltaY = currentY - startY.current;

    // Only handle horizontal right swipes
    if (
      deltaX > 0 &&
      Math.abs(deltaX) > Math.abs(deltaY) &&
      Math.abs(deltaX) > 10
    ) {
      isDragging.current = true;
      // Remove preventDefault to avoid passive event listener warnings
      setSwipeOffset(Math.min(deltaX, 300));
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const endX = e.changedTouches[0].clientX;
    const deltaX = endX - startX.current;

    setIsSwipeActive(false);
    setSwipeOffset(0);

    // Close modal if swipe distance is significant
    if (isDragging.current && deltaX > 100) {
      onClose();
    }

    isDragging.current = false;
  };

  // Get SearchFormContext for fallback values
  const searchFormContext = useSearchForm();

  // Use provided dates or fallback to SearchFormContext
  const contextDepartureDate =
    departureDate ||
    (searchFormContext.state.departureDate
      ? new Date(searchFormContext.state.departureDate)
      : undefined);
  const contextReturnDate =
    returnDate ||
    (searchFormContext.state.returnDate
      ? new Date(searchFormContext.state.returnDate)
      : undefined);

  // Current active date based on tab
  const currentDate =
    showTabs && isRoundTrip
      ? activeTab === "departure"
        ? contextDepartureDate
        : contextReturnDate
      : date;

  // Enhanced disabled logic for date validation
  const getDisabledFunction = React.useCallback(() => {
    if (showTabs && isRoundTrip) {
      if (activeTab === "return" && contextDepartureDate) {
        // For return date: disable dates <= departure date (return must be > departure)
        return (date: Date) => {
          const departureDateOnly = new Date(contextDepartureDate);
          departureDateOnly.setHours(0, 0, 0, 0);
          const checkDate = new Date(date);
          checkDate.setHours(0, 0, 0, 0);

          // Also apply the original disabled function if provided
          const originalDisabled = disabled ? disabled(date) : false;
          return originalDisabled || checkDate <= departureDateOnly;
        };
      } else if (activeTab === "departure") {
        // For departure date: use original disabled function
        return disabled;
      }
    }
    return disabled;
  }, [showTabs, isRoundTrip, activeTab, contextDepartureDate, disabled]);

  // Get the appropriate fromDate for each tab
  const getFromDate = React.useCallback(() => {
    if (
      showTabs &&
      isRoundTrip &&
      activeTab === "return" &&
      contextDepartureDate
    ) {
      return contextDepartureDate;
    }
    return fromDate;
  }, [showTabs, isRoundTrip, activeTab, contextDepartureDate, fromDate]);

  const handleDateSelect = useCallback(
    (selectedDate: Date | undefined) => {
      if (showTabs && isRoundTrip) {
        // Handle tab-based date selection
        if (activeTab === "departure") {
          if (onDepartureDateChange) {
            onDepartureDateChange(selectedDate);
          } else {
            // Fallback to SearchFormContext
            const formattedDate = selectedDate
              ? format(selectedDate, "yyyy-MM-dd")
              : "";
            searchFormContext.setDepartureDate(formattedDate);
          }

          // Auto switch to return tab if round trip and departure date selected
          // BUT only if return date is not already set
          // if (selectedDate && isRoundTrip && !contextReturnDate) {
          //   setTimeout(() => setActiveTab("return"), 300);
          // }
        } else {
          if (onReturnDateChange) {
            onReturnDateChange(selectedDate);
          } else {
            // Fallback to SearchFormContext
            const formattedDate = selectedDate
              ? format(selectedDate, "yyyy-MM-dd")
              : "";
            searchFormContext.setReturnDate(formattedDate);
          }
        }
      } else {
        // Handle single date selection
        onDateChange(selectedDate);

        // Auto close modal after single date selection
        if (selectedDate) {
          setTimeout(() => {
            onClose();
          }, 200);
        }
      }
    },
    [
      showTabs,
      isRoundTrip,
      activeTab,
      onDateChange,
      onDepartureDateChange,
      onReturnDateChange,
      searchFormContext,
      contextReturnDate,
      onClose,
    ]
  );

  const handleTabChange = useCallback((tab: "departure" | "return") => {
    setActiveTab(tab);
  }, []);

  const handleRoundTripToggle = useCallback(() => {
    if (onRoundTripToggle) {
      onRoundTripToggle();
    }
  }, [onRoundTripToggle]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full h-full max-w-none max-h-none p-0 gap-0 bg-[#F8F8F8] rounded-none [&>button]:hidden"
        ref={modalRef}
        style={{
          transform: `translateX(${swipeOffset}px)`,
          opacity: isSwipeActive ? Math.max(0.5, 1 - swipeOffset / 300) : 1,
          transition: isSwipeActive
            ? "none"
            : "transform 0.2s ease-out, opacity 0.2s ease-out",
        }}
      >
        <div
          className="flex flex-col h-full"
          onTouchStart={(e) => {
            handleTouchStart(e);
          }}
          onTouchMove={(e) => {
            handleTouchMove(e);
          }}
          onTouchEnd={(e) => {
            handleTouchEnd(e);
          }}
        >
          {/* Header */}
          <div className="bg-white px-6 py-4">
            {/* Header row - Back button và Tabs theo Figma */}
            <div className="flex items-center h-[60px] relative">
              <button
                onClick={onClose}
                className="flex-shrink-0 w-6 h-6 text-[#7C7B7B] hover:text-[#181818] transition-colors"
                aria-label={t("common.previous")}
              >
                <ArrowLeftIcon className="w-6 h-6" />
              </button>

              {/* Tabs - căn giữa màn hình */}
              {showTabs && onRoundTripToggle && isRoundTrip && (
                <div className="absolute left-1/2 transform -translate-x-1/2">
                  <DateTabs
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    departureDate={contextDepartureDate}
                    returnDate={contextReturnDate}
                    isRoundTrip={isRoundTrip}
                    onRoundTripToggle={handleRoundTripToggle}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto bg-[#F8F8F8] px-4 py-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <Calendar
                mode="single"
                selected={currentDate}
                onSelect={handleDateSelect}
                disabled={getDisabledFunction()}
                fromDate={getFromDate()}
                locale={vi}
                className="w-full"
                modifiers={{
                  // In tab mode: show the other date as reference
                  reference:
                    showTabs && isRoundTrip
                      ? activeTab === "departure" && contextReturnDate
                        ? [contextReturnDate]
                        : activeTab === "return" && contextDepartureDate
                          ? [contextDepartureDate]
                          : []
                      : referenceDate
                        ? [referenceDate]
                        : [],
                }}
                modifiersClassNames={{
                  reference: "bg-orange-100 text-orange-700 font-medium",
                }}
                classNames={{
                  months: "flex flex-col space-y-6",
                  month: "space-y-6",
                  caption: "flex justify-center items-center relative mb-6",
                  caption_label:
                    "text-base font-bold text-[#2D5BFF] font-mulish",
                  nav: "flex items-center",
                  nav_button: cn(
                    "h-8 w-8 bg-white p-0 text-[#2D5BFF] hover:text-[#1E4FFF] rounded-full border border-[#E5E5E5] hover:border-[#2D5BFF] transition-all duration-200 shadow-sm hover:shadow-md flex items-center justify-center"
                  ),
                  nav_button_previous: "absolute left-0",
                  nav_button_next: "absolute right-0",
                  table: "w-full border-collapse space-y-4",
                  head_row: "flex w-full",
                  head_cell:
                    "text-[#5C5C5C] rounded-md w-8 font-normal text-base font-mulish flex-1 text-center py-2",
                  row: "flex w-full mt-4",
                  cell: "relative flex-1 text-center text-sm p-0 [&:has([aria-selected])]:bg-transparent",
                  day: cn(
                    "h-8 w-8 p-0 font-normal text-sm hover:bg-[#EEF4FF] hover:text-[#2D5BFF] rounded-full mx-auto font-mulish transition-colors",
                    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#2D5BFF] focus-visible:ring-offset-1"
                  ),
                  day_today: "bg-[#F0F0F0] text-[#181818] font-bold",
                  day_selected:
                    "bg-[#2D5BFF] text-white hover:bg-[#1E4FFF] hover:text-white font-bold",
                  day_disabled: "text-[#8A8A8A] opacity-50 cursor-not-allowed",
                  day_outside: "text-[#8A8A8A] opacity-50",
                  day_range_middle:
                    "aria-selected:bg-[#EEF4FF] aria-selected:text-[#2D5BFF]",
                }}
                components={{
                  IconLeft: () => (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      className="transition-transform duration-200"
                    >
                      <path
                        d="M10 12L6 8L10 4"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ),
                  IconRight: () => (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      className="transition-transform duration-200"
                    >
                      <path
                        d="M6 12L10 8L6 4"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  ),
                }}
                showOutsideDays={true}
              />
            </div>
          </div>

          {/* Bottom Button */}
          <div className="bg-white px-6 py-4 border-t border-gray-100">
            <button
              onClick={onClose}
              className="w-full h-12 bg-[#2D5BFF] hover:bg-[#1E4FFF] text-white rounded-lg font-bold text-base transition-colors duration-200 shadow-md hover:shadow-lg font-mulish"
            >
              {t("common.done")}
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
