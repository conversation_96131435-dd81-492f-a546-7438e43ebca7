import React from "react";
import { cn } from "@/lib/utils";

export interface FormFieldProps {
  label?: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
  labelClassName?: string;
  errorClassName?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  required,
  children,
  className,
  labelClassName,
  errorClassName,
}) => {
  return (
    <div className={cn("space-y-1", className)}>
      {label && (
        <label
          className={cn("text-sm font-medium text-gray-700", labelClassName)}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {children}
      {error && (
        <p className={cn("text-red-500 text-sm", errorClassName)}>{error}</p>
      )}
    </div>
  );
};
