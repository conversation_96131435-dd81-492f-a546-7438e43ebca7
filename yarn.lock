# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  "integrity" "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw=="
  "resolved" "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  "version" "5.2.0"

"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.3.0":
  "integrity" "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw=="
  "resolved" "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@asamuzakjp/css-color@^3.1.2":
  "integrity" "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw=="
  "resolved" "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@csstools/css-calc" "^2.1.3"
    "@csstools/css-color-parser" "^3.0.9"
    "@csstools/css-parser-algorithms" "^3.0.4"
    "@csstools/css-tokenizer" "^3.0.3"
    "lru-cache" "^10.4.3"

"@babel/code-frame@^7.10.4", "@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.27.2":
  "integrity" "sha512-V42wFfx1ymFte+ecf6iXghnnP8kWTO+ZLXIyZq+1LAXHHvTZdVxicn4yiVYdYMGaCO3tmqub11AorKkv+iodqw=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.3.tgz"
  "version" "7.27.3"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.23.7", "@babel/core@^7.26.10", "@babel/core@^7.26.8":
  "integrity" "sha512-hyrN8ivxfvJ4i0fIJuV4EOlV0WDMz5Ui4StRTgVaAvWeiRCilXgwVvxJKtFQ3TKtHgJscB2YiXKGNJuVwhQMtA=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.3"
    "@babel/parser" "^7.27.3"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.27.3"
    "@babel/types" "^7.27.3"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.26.8", "@babel/generator@^7.27.3":
  "integrity" "sha512-xnlJYj5zepml8NXtjkG0WquFUv8RskFqyFcVgTBp5k+NaA/8uw/K+OSVf8AMGw5e9HKP2ETd5xpK5MLZQD6b4Q=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/parser" "^7.27.3"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "jsesc" "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  "integrity" "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-module-imports@^7.27.1":
  "integrity" "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  "integrity" "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.27.1":
  "integrity" "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helpers@^7.27.3":
  "integrity" "sha512-h/eKy9agOya1IGuLaZ9tEUgz+uIRXcbtOhRtUyyMf8JFmn1iT13vnl/IGVWSkdOCG/pC57U4S1jnAabAavTMwg=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.23.6", "@babel/parser@^7.26.8", "@babel/parser@^7.27.2", "@babel/parser@^7.27.3":
  "integrity" "sha512-xyYxRj6+tLNDTWi0KCBcZ9V7yg3/lwL9DWh9Uwh/RIVlIfFidggcgxKX3GCXwCiswwcGRawBKbEg2LG/Y8eJhw=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/plugin-syntax-jsx@^7.25.9":
  "integrity" "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-typescript@^7.25.9":
  "integrity" "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.25.9":
  "integrity" "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.25.9":
  "integrity" "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.25.0", "@babel/runtime@^7.26.7", "@babel/runtime@^7.27.1":
  "integrity" "sha512-7EYtGezsdiDMyY80+65EzwiGmcJqpmcZCojSXaRgdrBaGtWTgDZKq69cPIVped6MkIM78cTQ2GOiEYjwOlG4xw=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.3.tgz"
  "version" "7.27.3"

"@babel/template@^7.26.8", "@babel/template@^7.27.2":
  "integrity" "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.23.7", "@babel/traverse@^7.26.8", "@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3":
  "integrity" "sha512-lId/IfN/Ye1CIu8xG7oKBHXd2iNb2aW1ilPszzGcJug6M8RCKfVNcYhpI5+bMvFYjK7lXIM0R+a+6r8xhHp2FQ=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/parser" "^7.27.3"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    "debug" "^4.3.1"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.23.6", "@babel/types@^7.26.8", "@babel/types@^7.27.1", "@babel/types@^7.27.3":
  "integrity" "sha512-Y1GkI4ktrtvmawoSq+4FCVHNryea6uR+qUQy0AGxLSsjCX0nVmkYQMBLHDkXZuo5hGx7eYdnIaslsdBFm7zbUw=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@csstools/color-helpers@^5.0.2":
  "integrity" "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA=="
  "resolved" "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.2.tgz"
  "version" "5.0.2"

"@csstools/css-calc@^2.1.3", "@csstools/css-calc@^2.1.4":
  "integrity" "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ=="
  "resolved" "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.4.tgz"
  "version" "2.1.4"

"@csstools/css-color-parser@^3.0.9":
  "integrity" "sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg=="
  "resolved" "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.10.tgz"
  "version" "3.0.10"
  dependencies:
    "@csstools/color-helpers" "^5.0.2"
    "@csstools/css-calc" "^2.1.4"

"@csstools/css-parser-algorithms@^3.0.4", "@csstools/css-parser-algorithms@^3.0.5":
  "integrity" "sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ=="
  "resolved" "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.5.tgz"
  "version" "3.0.5"

"@csstools/css-tokenizer@^3.0.3", "@csstools/css-tokenizer@^3.0.4":
  "integrity" "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw=="
  "resolved" "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz"
  "version" "3.0.4"

"@esbuild/win32-x64@0.25.5":
  "integrity" "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g=="
  "resolved" "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz"
  "version" "0.25.5"

"@floating-ui/core@^1.7.0":
  "integrity" "sha512-FRdBLykrPPA6P76GGGqlex/e7fbe0F1ykgxHYNXQsH/iTEtjMj/f9bpY5oQqbjt5VgZvgz/uKXbGuROijh3VLA=="
  "resolved" "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  "integrity" "sha512-lGTor4VlXcesUMh1cupTUTDoCxMb0V6bm3CnxHzQcw8Eaf1jQbgQX4i02fYgT0vJ82tb5MZ4CZk1LRGkktJCzg=="
  "resolved" "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "@floating-ui/core" "^1.7.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.0.0":
  "integrity" "sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A=="
  "resolved" "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/utils@^0.2.9":
  "integrity" "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg=="
  "resolved" "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz"
  "version" "0.2.9"

"@heroicons/react@^2.2.0":
  "integrity" "sha512-LMcepvRaS9LYHJGsF0zzmgKCUim/X3N/DQKc4jepAXJ7l8QxJ1PmxJzqplF2Z3FE4PqBAIGyJAQ/w4B5dsqbtQ=="
  "resolved" "https://registry.npmjs.org/@heroicons/react/-/react-2.2.0.tgz"
  "version" "2.2.0"

"@hookform/resolvers@^5.1.1":
  "integrity" "sha512-J/NVING3LMAEvexJkyTLjruSm7aOFx7QX21pzkiJfMoNG0wl5aFEjLTl7ay7IQb9EWY6AkrBy7tHL2Alijpdcg=="
  "resolved" "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@isaacs/fs-minipass@^4.0.0":
  "integrity" "sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w=="
  "resolved" "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "minipass" "^7.0.4"

"@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz"
  "version" "0.3.8"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/set-array@^1.2.1":
  "integrity" "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="
  "resolved" "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  "integrity" "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  "version" "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  "integrity" "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  "version" "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@radix-ui/primitive@1.1.2":
  "integrity" "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-arrow@1.1.7":
  "integrity" "sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-collection@1.1.7":
  "integrity" "sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-compose-refs@^1.1.1", "@radix-ui/react-compose-refs@1.1.2":
  "integrity" "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-context@1.1.2":
  "integrity" "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-dialog@^1.1.14", "@radix-ui/react-dialog@^1.1.6":
  "integrity" "sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  "integrity" "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-dismissable-layer@1.1.10":
  "integrity" "sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.15":
  "integrity" "sha512-mIBnOjgwo9AH3FyKaSWoSu/dYj6VdhJ7frEPiGTeXCdUFHjl9h3mFh2wwhEtINOmYXWhdpf1rY2minFsmaNgVQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.15.tgz"
  "version" "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.2":
  "integrity" "sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-focus-scope@1.1.7":
  "integrity" "sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-id@^1.1.0", "@radix-ui/react-id@1.1.1":
  "integrity" "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.7":
  "integrity" "sha512-YT1GqPSL8kJn20djelMX7/cTRp/Y9w5IZHvfxQTVHrOqa2yMl7i/UfMqKRU5V7mEyKTrUVgJXhNQPVCG8PBLoQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-menu@2.1.15":
  "integrity" "sha512-tVlmA3Vb9n8SZSd+YSbuFR66l87Wiy4du+YE+0hzKQEANA+7cWKH1WgqcEX4pXqxUFQKrWQGHdvEfw00TjFiew=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.15.tgz"
  "version" "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-popover@^1.1.14":
  "integrity" "sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-popper@1.2.7":
  "integrity" "sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.9":
  "integrity" "sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.9.tgz"
  "version" "1.1.9"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.4":
  "integrity" "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@^2.0.2", "@radix-ui/react-primitive@2.1.3":
  "integrity" "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-roving-focus@1.1.10":
  "integrity" "sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-slot@^1.2.3", "@radix-ui/react-slot@1.2.3":
  "integrity" "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.2.5":
  "integrity" "sha512-5ijLkak6ZMylXsaImpZ8u4Rlf5grRmoc0p0QeX9VJtlrM4f5m3nCTX8tWga/zOA8PZYIR/t0p2Mnvd7InrJ6yQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-use-callback-ref@1.1.1":
  "integrity" "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-controllable-state@1.2.2":
  "integrity" "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  "integrity" "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz"
  "version" "0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  "integrity" "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-layout-effect@1.1.1":
  "integrity" "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-previous@1.1.1":
  "integrity" "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-rect@1.1.1":
  "integrity" "sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  "integrity" "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/rect@1.1.1":
  "integrity" "sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.1.tgz"
  "version" "1.1.1"

"@rolldown/pluginutils@1.0.0-beta.9":
  "integrity" "sha512-e9MeMtVWo186sgvFFJOPGy7/d2j2mZhLJIdVW0C/xDluuOvymEATqz6zKsP0ZmXGzQtqlyjz5sC1sYQUoJG98w=="
  "resolved" "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9.tgz"
  "version" "1.0.0-beta.9"

"@standard-schema/utils@^0.3.0":
  "integrity" "sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g=="
  "resolved" "https://registry.npmjs.org/@standard-schema/utils/-/utils-0.3.0.tgz"
  "version" "0.3.0"

"@tailwindcss/node@4.1.7":
  "integrity" "sha512-9rsOpdY9idRI2NH6CL4wORFY0+Q6fnx9XP9Ju+iq/0wJwGD5IByIgFmwVbyy4ymuyprj8Qh4ErxMKTUL4uNh3g=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.7.tgz"
  "version" "4.1.7"
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    "enhanced-resolve" "^5.18.1"
    "jiti" "^2.4.2"
    "lightningcss" "1.30.1"
    "magic-string" "^0.30.17"
    "source-map-js" "^1.2.1"
    "tailwindcss" "4.1.7"

"@tailwindcss/oxide-win32-x64-msvc@4.1.7":
  "integrity" "sha512-rYHGmvoHiLJ8hWucSfSOEmdCBIGZIq7SpkPRSqLsH2Ab2YUNgKeAPT1Fi2cx3+hnYOrAb0jp9cRyode3bBW4mQ=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.7.tgz"
  "version" "4.1.7"

"@tailwindcss/oxide@4.1.7":
  "integrity" "sha512-5SF95Ctm9DFiUyjUPnDGkoKItPX/k+xifcQhcqX5RA85m50jw1pT/KzjdvlqxRja45Y52nR4MR9fD1JYd7f8NQ=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.7.tgz"
  "version" "4.1.7"
  dependencies:
    "detect-libc" "^2.0.4"
    "tar" "^7.4.3"
  optionalDependencies:
    "@tailwindcss/oxide-android-arm64" "4.1.7"
    "@tailwindcss/oxide-darwin-arm64" "4.1.7"
    "@tailwindcss/oxide-darwin-x64" "4.1.7"
    "@tailwindcss/oxide-freebsd-x64" "4.1.7"
    "@tailwindcss/oxide-linux-arm-gnueabihf" "4.1.7"
    "@tailwindcss/oxide-linux-arm64-gnu" "4.1.7"
    "@tailwindcss/oxide-linux-arm64-musl" "4.1.7"
    "@tailwindcss/oxide-linux-x64-gnu" "4.1.7"
    "@tailwindcss/oxide-linux-x64-musl" "4.1.7"
    "@tailwindcss/oxide-wasm32-wasi" "4.1.7"
    "@tailwindcss/oxide-win32-arm64-msvc" "4.1.7"
    "@tailwindcss/oxide-win32-x64-msvc" "4.1.7"

"@tailwindcss/postcss@^4.1.7":
  "integrity" "sha512-88g3qmNZn7jDgrrcp3ZXEQfp9CVox7xjP1HN2TFKI03CltPVd/c61ydn5qJJL8FYunn0OqBaW5HNUga0kmPVvw=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/postcss/-/postcss-4.1.7.tgz"
  "version" "4.1.7"
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    "@tailwindcss/node" "4.1.7"
    "@tailwindcss/oxide" "4.1.7"
    "postcss" "^8.4.41"
    "tailwindcss" "4.1.7"

"@tailwindcss/vite@^4.1.7":
  "integrity" "sha512-tYa2fO3zDe41I7WqijyVbRd8oWT0aEID1Eokz5hMT6wShLIHj3yvwj9XbfuloHP9glZ6H+aG2AN/+ZrxJ1Y5RQ=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.7.tgz"
  "version" "4.1.7"
  dependencies:
    "@tailwindcss/node" "4.1.7"
    "@tailwindcss/oxide" "4.1.7"
    "tailwindcss" "4.1.7"

"@tanstack/history@1.115.0":
  "integrity" "sha512-K7JJNrRVvyjAVnbXOH2XLRhFXDkeP54Kt2P4FR1Kl2KDGlIbkua5VqZQD2rot3qaDrpufyUa63nuLai1kOLTsQ=="
  "resolved" "https://registry.npmjs.org/@tanstack/history/-/history-1.115.0.tgz"
  "version" "1.115.0"

"@tanstack/query-core@5.77.2":
  "integrity" "sha512-1lqJwPsR6GX6nZFw06erRt518O19tWU6Q+x0fJUygl4lxHCYF2nhzBPwLKk2NPjYOrpR0K567hxPc5K++xDe9Q=="
  "resolved" "https://registry.npmjs.org/@tanstack/query-core/-/query-core-5.77.2.tgz"
  "version" "5.77.2"

"@tanstack/query-devtools@5.76.0":
  "integrity" "sha512-1p92nqOBPYVqVDU0Ua5nzHenC6EGZNrLnB2OZphYw8CNA1exuvI97FVgIKON7Uug3uQqvH/QY8suUKpQo8qHNQ=="
  "resolved" "https://registry.npmjs.org/@tanstack/query-devtools/-/query-devtools-5.76.0.tgz"
  "version" "5.76.0"

"@tanstack/react-query-devtools@^5.77.2":
  "integrity" "sha512-TxB9boB0dmTJByAfh36kbhvohNHZiPJe+m+PCnbnfL+gdDHJbp174S6BwbU2dHx980njeAKFmWz8tgHNKG3itw=="
  "resolved" "https://registry.npmjs.org/@tanstack/react-query-devtools/-/react-query-devtools-5.77.2.tgz"
  "version" "5.77.2"
  dependencies:
    "@tanstack/query-devtools" "5.76.0"

"@tanstack/react-query@^5.77.2":
  "integrity" "sha512-BRHxWdy1mHmgAcYA/qy2IPLylT81oebLgkm9K85viN2Qol/Vq48t1dzDFeDIVQjTWDV96AmqsLNPlH5HjyKCxA=="
  "resolved" "https://registry.npmjs.org/@tanstack/react-query/-/react-query-5.77.2.tgz"
  "version" "5.77.2"
  dependencies:
    "@tanstack/query-core" "5.77.2"

"@tanstack/react-router-devtools@^1.114.3":
  "integrity" "sha512-bk34Kn7SubkUq3TbVN6wfALvOZ63ou/dzPqhijZAwHKXpatE90BwB/Y8mLhcoH+64iXtpf/ZP2lqqsrxLXz0pw=="
  "resolved" "https://registry.npmjs.org/@tanstack/react-router-devtools/-/react-router-devtools-1.120.11.tgz"
  "version" "1.120.11"
  dependencies:
    "@tanstack/router-devtools-core" "^1.120.10"
    "solid-js" "^1.9.5"

"@tanstack/react-router@^1.114.3", "@tanstack/react-router@^1.120.11":
  "integrity" "sha512-VpG8gT+kibsdF9yQIOMfnCGe1pmUlrAG/fOoTm0gru1OEkJ2Tzc80codqiocRHQ9ULmlB4H/Zx56EZyQyF3ELw=="
  "resolved" "https://registry.npmjs.org/@tanstack/react-router/-/react-router-1.120.11.tgz"
  "version" "1.120.11"
  dependencies:
    "@tanstack/history" "1.115.0"
    "@tanstack/react-store" "^0.7.0"
    "@tanstack/router-core" "1.120.10"
    "jsesc" "^3.1.0"
    "tiny-invariant" "^1.3.3"
    "tiny-warning" "^1.0.3"

"@tanstack/react-store@^0.7.0", "@tanstack/react-store@^0.7.1":
  "integrity" "sha512-qUTEKdId6QPWGiWyKAPf/gkN29scEsz6EUSJ0C3HgLMgaqTAyBsQ2sMCfGVcqb+kkhEXAdjleCgH6LAPD6f2sA=="
  "resolved" "https://registry.npmjs.org/@tanstack/react-store/-/react-store-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "@tanstack/store" "0.7.1"
    "use-sync-external-store" "^1.5.0"

"@tanstack/router-core@^1.120.10", "@tanstack/router-core@1.120.10":
  "integrity" "sha512-AmEJAYt+6w/790zTnfddVhnK1QJCnd96H4xg1aD65Oohc8+OTQBxgWky/wzqwhHRdkdsBgRT7iWac9x5Y8UrQA=="
  "resolved" "https://registry.npmjs.org/@tanstack/router-core/-/router-core-1.120.10.tgz"
  "version" "1.120.10"
  dependencies:
    "@tanstack/history" "1.115.0"
    "@tanstack/store" "^0.7.0"
    "tiny-invariant" "^1.3.3"

"@tanstack/router-devtools-core@^1.120.10":
  "integrity" "sha512-fysPrKH7dL/G/guHm0HN+ceFEBZnbKaU9R8KZHo/Qzue7WxQV+g4or2EWnbBJ8/aF+C/WYgxR1ATFqfZEjHSfg=="
  "resolved" "https://registry.npmjs.org/@tanstack/router-devtools-core/-/router-devtools-core-1.120.10.tgz"
  "version" "1.120.10"
  dependencies:
    "clsx" "^2.1.1"
    "goober" "^2.1.16"

"@tanstack/router-generator@^1.120.11":
  "integrity" "sha512-66B8jt5S+33fyvSA9/RgHZ0h6uxvAEWwyvCz/Ify7YCtIjZhQ+bTtSzikzUqWpe864EORx1ZZnQdz/7uGOL7Vw=="
  "resolved" "https://registry.npmjs.org/@tanstack/router-generator/-/router-generator-1.120.11.tgz"
  "version" "1.120.11"
  dependencies:
    "@tanstack/virtual-file-routes" "^1.115.0"
    "prettier" "^3.5.0"
    "tsx" "^4.19.2"
    "zod" "^3.24.2"

"@tanstack/router-plugin@^1.114.3":
  "integrity" "sha512-4p/rn9AHPc6HDJZ+DrfdoZad3H02vqn1FMunDasuR+xQ46C/qAfP5L+/O6Kaq6jqiAM5s9tpqy4p0tL53+FW3Q=="
  "resolved" "https://registry.npmjs.org/@tanstack/router-plugin/-/router-plugin-1.120.11.tgz"
  "version" "1.120.11"
  dependencies:
    "@babel/core" "^7.26.8"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/plugin-syntax-typescript" "^7.25.9"
    "@babel/template" "^7.26.8"
    "@babel/traverse" "^7.26.8"
    "@babel/types" "^7.26.8"
    "@tanstack/router-core" "^1.120.10"
    "@tanstack/router-generator" "^1.120.11"
    "@tanstack/router-utils" "^1.115.0"
    "@tanstack/virtual-file-routes" "^1.115.0"
    "@types/babel__core" "^7.20.5"
    "@types/babel__template" "^7.4.4"
    "@types/babel__traverse" "^7.20.6"
    "babel-dead-code-elimination" "^1.0.10"
    "chokidar" "^3.6.0"
    "unplugin" "^2.1.2"
    "zod" "^3.24.2"

"@tanstack/router-utils@^1.115.0":
  "integrity" "sha512-Dng4y+uLR9b5zPGg7dHReHOTHQa6x+G6nCoZshsDtWrYsrdCcJEtLyhwZ5wG8OyYS6dVr/Cn+E5Bd2b6BhJ89w=="
  "resolved" "https://registry.npmjs.org/@tanstack/router-utils/-/router-utils-1.115.0.tgz"
  "version" "1.115.0"
  dependencies:
    "@babel/generator" "^7.26.8"
    "@babel/parser" "^7.26.8"
    "ansis" "^3.11.0"
    "diff" "^7.0.0"

"@tanstack/store@^0.7.0", "@tanstack/store@0.7.1":
  "integrity" "sha512-PjUQKXEXhLYj2X5/6c1Xn/0/qKY0IVFxTJweopRfF26xfjVyb14yALydJrHupDh3/d+1WKmfEgZPBVCmDkzzwg=="
  "resolved" "https://registry.npmjs.org/@tanstack/store/-/store-0.7.1.tgz"
  "version" "0.7.1"

"@tanstack/virtual-file-routes@^1.115.0":
  "integrity" "sha512-XLUh1Py3AftcERrxkxC5Y5m5mfllRH3YR6YVlyjFgI2Tc2Ssy2NKmQFQIafoxfW459UJ8Dn81nWKETEIJifE4g=="
  "resolved" "https://registry.npmjs.org/@tanstack/virtual-file-routes/-/virtual-file-routes-1.115.0.tgz"
  "version" "1.115.0"

"@testing-library/dom@^10.0.0", "@testing-library/dom@^10.4.0":
  "integrity" "sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ=="
  "resolved" "https://registry.npmjs.org/@testing-library/dom/-/dom-10.4.0.tgz"
  "version" "10.4.0"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    "aria-query" "5.3.0"
    "chalk" "^4.1.0"
    "dom-accessibility-api" "^0.5.9"
    "lz-string" "^1.5.0"
    "pretty-format" "^27.0.2"

"@testing-library/react@^16.2.0":
  "integrity" "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw=="
  "resolved" "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz"
  "version" "16.3.0"
  dependencies:
    "@babel/runtime" "^7.12.5"

"@types/aria-query@^5.0.1":
  "integrity" "sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw=="
  "resolved" "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz"
  "version" "5.0.4"

"@types/babel__core@^7.20.5":
  "integrity" "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  "version" "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*", "@types/babel__template@^7.4.4":
  "integrity" "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  "version" "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.20.6":
  "integrity" "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/types" "^7.20.7"

"@types/debug@^4.0.0", "@types/debug@^4.1.12":
  "integrity" "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ=="
  "resolved" "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz"
  "version" "4.1.12"
  dependencies:
    "@types/ms" "*"

"@types/estree-jsx@^1.0.0":
  "integrity" "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg=="
  "resolved" "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@^1.0.0", "@types/estree@1.0.7":
  "integrity" "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz"
  "version" "1.0.7"

"@types/file-saver@^2.0.7":
  "integrity" "sha512-dNKVfHd/jk0SkR/exKGj2ggkB45MAkzvWCaqLUUgkyjITkGNzH8H+yUwr+BLJUBjZOe9w8X3wgmXhZDRg1ED6A=="
  "resolved" "https://registry.npmjs.org/@types/file-saver/-/file-saver-2.0.7.tgz"
  "version" "2.0.7"

"@types/hast@^3.0.0":
  "integrity" "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ=="
  "resolved" "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "@types/unist" "*"

"@types/html2canvas@^1.0.0":
  "integrity" "sha512-BJpVf+FIN9UERmzhbtUgpXj6XBZpG67FMgBLLoj9HZKd9XifcCpSV+UnFcwTZfEyun4U/KmCrrVOG7829L589w=="
  "resolved" "https://registry.npmjs.org/@types/html2canvas/-/html2canvas-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "html2canvas" "*"

"@types/jszip@^3.4.1":
  "integrity" "sha512-TezXjmf3lj+zQ651r6hPqvSScqBLvyPI9FxdXBqpEwBijNGQ2NXpaFW/7joGzveYkKQUil7iiDHLo6LV71Pc0A=="
  "resolved" "https://registry.npmjs.org/@types/jszip/-/jszip-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "jszip" "*"

"@types/mdast@^4.0.0":
  "integrity" "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA=="
  "resolved" "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "@types/unist" "*"

"@types/ms@*":
  "integrity" "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA=="
  "resolved" "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz"
  "version" "2.1.0"

"@types/node@*", "@types/node@^18.0.0 || ^20.0.0 || >=22.0.0", "@types/node@^22.15.23":
  "integrity" "sha512-7Ec1zaFPF4RJ0eXu1YT/xgiebqwqoJz8rYPDi/O2BcZ++Wpt0Kq9cl0eg6NN6bYbPnR67ZLo7St5Q3UK0SnARw=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-22.15.23.tgz"
  "version" "22.15.23"
  dependencies:
    "undici-types" "~6.21.0"

"@types/qrcode.react@^1.0.5":
  "integrity" "sha512-BghPtnlwvrvq8QkGa1H25YnN+5OIgCKFuQruncGWLGJYOzeSKiix/4+B9BtfKF2wf5ja8yfyWYA3OXju995G8w=="
  "resolved" "https://registry.npmjs.org/@types/qrcode.react/-/qrcode.react-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "@types/react" "*"

"@types/qrcode@^1.5.5":
  "integrity" "sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg=="
  "resolved" "https://registry.npmjs.org/@types/qrcode/-/qrcode-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "@types/node" "*"

"@types/raf@^3.4.0":
  "integrity" "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw=="
  "resolved" "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz"
  "version" "3.4.3"

"@types/react-dom@*", "@types/react-dom@^18.0.0 || ^19.0.0", "@types/react-dom@^19.0.3":
  "integrity" "sha512-CMCjrWucUBZvohgZxkjd6S9h0nZxXjzus6yDfUb+xLxYM7VvjKNH1tQrE9GWLql1XoOP4/Ds3bwFqShHUYraGg=="
  "resolved" "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.5.tgz"
  "version" "19.1.5"

"@types/react@*", "@types/react@^18.0.0 || ^19.0.0", "@types/react@^19.0.0", "@types/react@^19.0.8", "@types/react@>=18", "@types/react@>=18.0.0":
  "integrity" "sha512-JeG0rEWak0N6Itr6QUx+X60uQmN+5t3j9r/OVDtWzFXKaj6kD1BwJzOksD0FF6iWxZlbE1kB0q9vtnU2ekqa1Q=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-19.1.6.tgz"
  "version" "19.1.6"
  dependencies:
    "csstype" "^3.0.2"

"@types/trusted-types@^2.0.7":
  "integrity" "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw=="
  "resolved" "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz"
  "version" "2.0.7"

"@types/unist@*", "@types/unist@^3.0.0":
  "integrity" "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q=="
  "resolved" "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz"
  "version" "3.0.3"

"@types/unist@^2.0.0":
  "integrity" "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
  "resolved" "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz"
  "version" "2.0.11"

"@ungap/structured-clone@^1.0.0":
  "integrity" "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g=="
  "resolved" "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  "version" "1.3.0"

"@vitejs/plugin-react@^4.3.4":
  "integrity" "sha512-JuLWaEqypaJmOJPLWwO335Ig6jSgC1FTONCWAxnqcQthLTK/Yc9aH6hr9z/87xciejbQcnP3GnA1FWUSWeXaeg=="
  "resolved" "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "@babel/core" "^7.26.10"
    "@babel/plugin-transform-react-jsx-self" "^7.25.9"
    "@babel/plugin-transform-react-jsx-source" "^7.25.9"
    "@rolldown/pluginutils" "1.0.0-beta.9"
    "@types/babel__core" "^7.20.5"
    "react-refresh" "^0.17.0"

"@vitest/expect@3.1.4":
  "integrity" "sha512-xkD/ljeliyaClDYqHPNCiJ0plY5YIcM0OlRiZizLhlPmpXWpxnGMyTZXOHFhFeG7w9P5PBeL4IdtJ/HeQwTbQA=="
  "resolved" "https://registry.npmjs.org/@vitest/expect/-/expect-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/spy" "3.1.4"
    "@vitest/utils" "3.1.4"
    "chai" "^5.2.0"
    "tinyrainbow" "^2.0.0"

"@vitest/mocker@3.1.4":
  "integrity" "sha512-8IJ3CvwtSw/EFXqWFL8aCMu+YyYXG2WUSrQbViOZkWTKTVicVwZ/YiEZDSqD00kX+v/+W+OnxhNWoeVKorHygA=="
  "resolved" "https://registry.npmjs.org/@vitest/mocker/-/mocker-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/spy" "3.1.4"
    "estree-walker" "^3.0.3"
    "magic-string" "^0.30.17"

"@vitest/pretty-format@^3.1.4", "@vitest/pretty-format@3.1.4":
  "integrity" "sha512-cqv9H9GvAEoTaoq+cYqUTCGscUjKqlJZC7PRwY5FMySVj5J+xOm1KQcCiYHJOEzOKRUhLH4R2pTwvFlWCEScsg=="
  "resolved" "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "tinyrainbow" "^2.0.0"

"@vitest/runner@3.1.4":
  "integrity" "sha512-djTeF1/vt985I/wpKVFBMWUlk/I7mb5hmD5oP8K9ACRmVXgKTae3TUOtXAEBfslNKPzUQvnKhNd34nnRSYgLNQ=="
  "resolved" "https://registry.npmjs.org/@vitest/runner/-/runner-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/utils" "3.1.4"
    "pathe" "^2.0.3"

"@vitest/snapshot@3.1.4":
  "integrity" "sha512-JPHf68DvuO7vilmvwdPr9TS0SuuIzHvxeaCkxYcCD4jTk67XwL45ZhEHFKIuCm8CYstgI6LZ4XbwD6ANrwMpFg=="
  "resolved" "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/pretty-format" "3.1.4"
    "magic-string" "^0.30.17"
    "pathe" "^2.0.3"

"@vitest/spy@3.1.4":
  "integrity" "sha512-Xg1bXhu+vtPXIodYN369M86K8shGLouNjoVI78g8iAq2rFoHFdajNvJJ5A/9bPMFcfQqdaCpOgWKEoMQg/s0Yg=="
  "resolved" "https://registry.npmjs.org/@vitest/spy/-/spy-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "tinyspy" "^3.0.2"

"@vitest/utils@3.1.4":
  "integrity" "sha512-yriMuO1cfFhmiGc8ataN51+9ooHRuURdfAZfwFd3usWynjzpLslZdYnRegTv32qdgtJTsj15FoeZe2g15fY1gg=="
  "resolved" "https://registry.npmjs.org/@vitest/utils/-/utils-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/pretty-format" "3.1.4"
    "loupe" "^3.1.3"
    "tinyrainbow" "^2.0.0"

"acorn@^8.14.1":
  "integrity" "sha512-OvQ/2pUDKmgfCg++xsTX1wGxfTaszcHVcTctW4UJB4hibJx2HXxxO5UmVgyjMa+ZDsiaf5wWLXYpRWMmBI0QHg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz"
  "version" "8.14.1"

"agent-base@^7.1.0", "agent-base@^7.1.2":
  "integrity" "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw=="
  "resolved" "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  "version" "7.1.3"

"ansi-regex@^5.0.1":
  "integrity" "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="
  "resolved" "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^5.0.0":
  "integrity" "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  "version" "5.2.0"

"ansis@^3.11.0":
  "integrity" "sha512-0qWUglt9JEqLFr3w1I1pbrChn1grhaiAR2ocX1PP/flRmxgtwTzPFFFnfIlD6aMOLQZgSuCRlidD70lvx8yhzg=="
  "resolved" "https://registry.npmjs.org/ansis/-/ansis-3.17.0.tgz"
  "version" "3.17.0"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"aria-hidden@^1.2.4":
  "integrity" "sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA=="
  "resolved" "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "tslib" "^2.0.0"

"aria-query@5.3.0":
  "integrity" "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A=="
  "resolved" "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz"
  "version" "5.3.0"
  dependencies:
    "dequal" "^2.0.3"

"assertion-error@^2.0.1":
  "integrity" "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA=="
  "resolved" "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.1.tgz"
  "version" "2.0.1"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg=="
  "resolved" "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^10.4.21":
  "integrity" "sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz"
  "version" "10.4.21"
  dependencies:
    "browserslist" "^4.24.4"
    "caniuse-lite" "^1.0.30001702"
    "fraction.js" "^4.3.7"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.1.1"
    "postcss-value-parser" "^4.2.0"

"axios@^1.9.0":
  "integrity" "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"babel-dead-code-elimination@^1.0.10":
  "integrity" "sha512-DV5bdJZTzZ0zn0DC24v3jD7Mnidh6xhKa4GfKCbq3sfW8kaWhDdZjP3i81geA8T33tdYqWKw4D3fVv0CwEgKVA=="
  "resolved" "https://registry.npmjs.org/babel-dead-code-elimination/-/babel-dead-code-elimination-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "@babel/core" "^7.23.7"
    "@babel/parser" "^7.23.6"
    "@babel/traverse" "^7.23.7"
    "@babel/types" "^7.23.6"

"bail@^2.0.0":
  "integrity" "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw=="
  "resolved" "https://registry.npmjs.org/bail/-/bail-2.0.2.tgz"
  "version" "2.0.2"

"base64-arraybuffer@^1.0.2":
  "integrity" "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ=="
  "resolved" "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  "version" "1.0.2"

"binary-extensions@^2.0.0":
  "integrity" "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  "version" "2.3.0"

"braces@~3.0.2":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"browserslist@^4.24.0", "browserslist@^4.24.4", "browserslist@>= 4.21.0":
  "integrity" "sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.24.5.tgz"
  "version" "4.24.5"
  dependencies:
    "caniuse-lite" "^1.0.30001716"
    "electron-to-chromium" "^1.5.149"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.3"

"btoa@^1.2.1":
  "integrity" "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g=="
  "resolved" "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  "version" "1.2.1"

"cac@^6.7.14":
  "integrity" "sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ=="
  "resolved" "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz"
  "version" "6.7.14"

"call-bind-apply-helpers@^1.0.1", "call-bind-apply-helpers@^1.0.2":
  "integrity" "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ=="
  "resolved" "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"

"camelcase@^5.0.0":
  "integrity" "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg=="
  "resolved" "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"caniuse-lite@^1.0.30001702", "caniuse-lite@^1.0.30001716":
  "integrity" "sha512-AflseV1ahcSunK53NfEs9gFWgOEmzr0f+kaMFA4xiLZlr9Hzt7HxcSpIFcnNCUkz6R6dWKa54rUz3HUmI3nVcw=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001718.tgz"
  "version" "1.0.30001718"

"canvg@^3.0.11":
  "integrity" "sha512-5ON+q7jCTgMp9cjpu4Jo6XbvfYwSB2Ow3kzHKfIyJfaCAOHLbdKPQqGKgfED/R5B+3TFFfe8pegYA+b423SRyA=="
  "resolved" "https://registry.npmjs.org/canvg/-/canvg-3.0.11.tgz"
  "version" "3.0.11"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    "core-js" "^3.8.3"
    "raf" "^3.4.1"
    "regenerator-runtime" "^0.13.7"
    "rgbcolor" "^1.0.1"
    "stackblur-canvas" "^2.0.0"
    "svg-pathdata" "^6.0.3"

"ccount@^2.0.0":
  "integrity" "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg=="
  "resolved" "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz"
  "version" "2.0.1"

"chai@^5.2.0":
  "integrity" "sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw=="
  "resolved" "https://registry.npmjs.org/chai/-/chai-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "assertion-error" "^2.0.1"
    "check-error" "^2.1.1"
    "deep-eql" "^5.0.1"
    "loupe" "^3.1.0"
    "pathval" "^2.0.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"character-entities-html4@^2.0.0":
  "integrity" "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA=="
  "resolved" "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz"
  "version" "2.1.0"

"character-entities-legacy@^3.0.0":
  "integrity" "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ=="
  "resolved" "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz"
  "version" "3.0.0"

"character-entities@^2.0.0":
  "integrity" "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ=="
  "resolved" "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz"
  "version" "2.0.2"

"character-reference-invalid@^2.0.0":
  "integrity" "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw=="
  "resolved" "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz"
  "version" "2.0.1"

"check-error@^2.1.1":
  "integrity" "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw=="
  "resolved" "https://registry.npmjs.org/check-error/-/check-error-2.1.1.tgz"
  "version" "2.1.1"

"chokidar@^3.6.0":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chownr@^3.0.0":
  "integrity" "sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz"
  "version" "3.0.0"

"class-variance-authority@^0.7.1":
  "integrity" "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg=="
  "resolved" "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "clsx" "^2.1.1"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"clsx@^2.1.1":
  "integrity" "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA=="
  "resolved" "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  "version" "2.1.1"

"cmdk@^1.1.1":
  "integrity" "sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg=="
  "resolved" "https://registry.npmjs.org/cmdk/-/cmdk-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"comma-separated-tokens@^2.0.0":
  "integrity" "sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg=="
  "resolved" "https://registry.npmjs.org/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz"
  "version" "2.0.3"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"core-js@^3.6.0", "core-js@^3.8.3":
  "integrity" "sha512-Sz4PP4ZA+Rq4II21qkNqOEDTDrCvcANId3xpIgB34NDkWc3UduWj2dqEtN9yZIq8Dk3HyPI33x9sqqU5C8sr0g=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.42.0.tgz"
  "version" "3.42.0"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cross-fetch@4.0.0":
  "integrity" "sha512-e4a5N8lVvuLgAWgnCrLr2PP0YyDOTHa9H/Rj54dirp61qXnNq46m82bRhNqIA5VccJtWBvPTFRV3TtvHUKPB1g=="
  "resolved" "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "node-fetch" "^2.6.12"

"css-line-break@^2.1.0":
  "integrity" "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w=="
  "resolved" "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "utrie" "^1.0.2"

"cssstyle@^4.2.1":
  "integrity" "sha512-ZgW+Jgdd7i52AaLYCriF8Mxqft0gD/R9i9wi6RWBhs1pqdPEzPjym7rvRKi397WmQFf3SlyUsszhw+VVCbx79Q=="
  "resolved" "https://registry.npmjs.org/cssstyle/-/cssstyle-4.3.1.tgz"
  "version" "4.3.1"
  dependencies:
    "@asamuzakjp/css-color" "^3.1.2"
    "rrweb-cssom" "^0.8.0"

"csstype@^3.0.10", "csstype@^3.0.2", "csstype@^3.1.0":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"data-urls@^5.0.0":
  "integrity" "sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg=="
  "resolved" "https://registry.npmjs.org/data-urls/-/data-urls-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "whatwg-mimetype" "^4.0.0"
    "whatwg-url" "^14.0.0"

"date-fns@^2.28.0 || ^3.0.0", "date-fns@^4.1.0":
  "integrity" "sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg=="
  "resolved" "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz"
  "version" "4.1.0"

"debug@^4.0.0", "debug@^4.1.0", "debug@^4.3.1", "debug@^4.3.4", "debug@^4.4.0", "debug@4":
  "integrity" "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"decamelize@^1.2.0":
  "integrity" "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA=="
  "resolved" "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decimal.js@^10.5.0":
  "integrity" "sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw=="
  "resolved" "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz"
  "version" "10.5.0"

"decode-named-character-reference@^1.0.0":
  "integrity" "sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w=="
  "resolved" "https://registry.npmjs.org/decode-named-character-reference/-/decode-named-character-reference-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "character-entities" "^2.0.0"

"deep-eql@^5.0.1":
  "integrity" "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q=="
  "resolved" "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.2.tgz"
  "version" "5.0.2"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"dequal@^2.0.0", "dequal@^2.0.3":
  "integrity" "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA=="
  "resolved" "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  "version" "2.0.3"

"detect-libc@^2.0.3", "detect-libc@^2.0.4":
  "integrity" "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA=="
  "resolved" "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz"
  "version" "2.0.4"

"detect-node-es@^1.1.0":
  "integrity" "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ=="
  "resolved" "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  "version" "1.1.0"

"devlop@^1.0.0", "devlop@^1.1.0":
  "integrity" "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA=="
  "resolved" "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "dequal" "^2.0.0"

"diff@^7.0.0":
  "integrity" "sha512-PJWHUb1RFevKCwaFA9RlG5tCd+FO5iRh9A8HEtkmBH2Li03iJriB6m6JIN4rGz3K3JLawI7/veA1xzRKP6ISBw=="
  "resolved" "https://registry.npmjs.org/diff/-/diff-7.0.0.tgz"
  "version" "7.0.0"

"dijkstrajs@^1.0.1":
  "integrity" "sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA=="
  "resolved" "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz"
  "version" "1.0.3"

"dom-accessibility-api@^0.5.9":
  "integrity" "sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg=="
  "resolved" "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz"
  "version" "0.5.16"

"dompurify@^3.2.4":
  "integrity" "sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ=="
  "resolved" "https://registry.npmjs.org/dompurify/-/dompurify-3.2.6.tgz"
  "version" "3.2.6"
  optionalDependencies:
    "@types/trusted-types" "^2.0.7"

"dunder-proto@^1.0.1":
  "integrity" "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A=="
  "resolved" "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind-apply-helpers" "^1.0.1"
    "es-errors" "^1.3.0"
    "gopd" "^1.2.0"

"electron-to-chromium@^1.5.149":
  "integrity" "sha512-CEvHptWAMV5p6GJ0Lq8aheyvVbfzVrv5mmidu1D3pidoVNkB3tTBsTMVtPJ+rzRK5oV229mCLz9Zj/hNvU8GBA=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.159.tgz"
  "version" "1.5.159"

"embla-carousel-react@^8.6.0":
  "integrity" "sha512-0/PjqU7geVmo6F734pmPqpyHqiM99olvyecY7zdweCw+6tKEXnrE90pBiBbMMU8s5tICemzpQ3hi5EpxzGW+JA=="
  "resolved" "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.6.0.tgz"
  "version" "8.6.0"
  dependencies:
    "embla-carousel" "8.6.0"
    "embla-carousel-reactive-utils" "8.6.0"

"embla-carousel-reactive-utils@8.6.0":
  "integrity" "sha512-fMVUDUEx0/uIEDM0Mz3dHznDhfX+znCCDCeIophYb1QGVM7YThSWX+wz11zlYwWFOr74b4QLGg0hrGPJeG2s4A=="
  "resolved" "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.6.0.tgz"
  "version" "8.6.0"

"embla-carousel@8.6.0":
  "integrity" "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA=="
  "resolved" "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.6.0.tgz"
  "version" "8.6.0"

"emoji-regex@^8.0.0":
  "integrity" "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="
  "resolved" "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"enhanced-resolve@^5.18.1":
  "integrity" "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz"
  "version" "5.18.1"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"entities@^6.0.0":
  "integrity" "sha512-aKstq2TDOndCn4diEyp9Uq/Flu2i1GlLkc6XIDQSDMuaFE3OPW5OphLCyQ5SpSJZTb4reN+kTcYru5yIfXoRPw=="
  "resolved" "https://registry.npmjs.org/entities/-/entities-6.0.0.tgz"
  "version" "6.0.0"

"es-define-property@^1.0.1":
  "integrity" "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="
  "resolved" "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  "version" "1.0.1"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-module-lexer@^1.7.0":
  "integrity" "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA=="
  "resolved" "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz"
  "version" "1.7.0"

"es-object-atoms@^1.0.0", "es-object-atoms@^1.1.1":
  "integrity" "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA=="
  "resolved" "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.1.0":
  "integrity" "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA=="
  "resolved" "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"esbuild@^0.25.0", "esbuild@~0.25.0":
  "integrity" "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.25.5.tgz"
  "version" "0.25.5"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

"escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-string-regexp@^5.0.0":
  "integrity" "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz"
  "version" "5.0.0"

"estree-util-is-identifier-name@^3.0.0":
  "integrity" "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg=="
  "resolved" "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz"
  "version" "3.0.0"

"estree-walker@^3.0.3":
  "integrity" "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g=="
  "resolved" "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@types/estree" "^1.0.0"

"expect-type@^1.2.1":
  "integrity" "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw=="
  "resolved" "https://registry.npmjs.org/expect-type/-/expect-type-1.2.1.tgz"
  "version" "1.2.1"

"extend@^3.0.0":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"fdir@^6.4.4":
  "integrity" "sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg=="
  "resolved" "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz"
  "version" "6.4.4"

"fflate@^0.8.1":
  "integrity" "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="
  "resolved" "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz"
  "version" "0.8.2"

"file-saver@^2.0.5":
  "integrity" "sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA=="
  "resolved" "https://registry.npmjs.org/file-saver/-/file-saver-2.0.5.tgz"
  "version" "2.0.5"

"fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"find-up@^4.1.0":
  "integrity" "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"follow-redirects@^1.15.6":
  "integrity" "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  "version" "1.15.9"

"form-data@^4.0.0":
  "integrity" "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "es-set-tostringtag" "^2.1.0"
    "mime-types" "^2.1.12"

"fraction.js@^4.3.7":
  "integrity" "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew=="
  "resolved" "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  "version" "4.3.7"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1":
  "integrity" "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg=="
  "resolved" "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.2.6":
  "integrity" "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "math-intrinsics" "^1.1.0"

"get-nonce@^1.0.0":
  "integrity" "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q=="
  "resolved" "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  "version" "1.0.1"

"get-proto@^1.0.1":
  "integrity" "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g=="
  "resolved" "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-object-atoms" "^1.0.0"

"get-tsconfig@^4.7.5":
  "integrity" "sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ=="
  "resolved" "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.1.tgz"
  "version" "4.10.1"
  dependencies:
    "resolve-pkg-maps" "^1.0.0"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"globals@^11.1.0":
  "integrity" "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  "version" "11.12.0"

"goober@^2.1.16":
  "integrity" "sha512-erjk19y1U33+XAMe1VTvIONHYoSqE4iS7BYUZfHaqeohLmnC0FdxEh7rQU+6MZ4OajItzjZFSRtVANrQwNq6/g=="
  "resolved" "https://registry.npmjs.org/goober/-/goober-2.1.16.tgz"
  "version" "2.1.16"

"gopd@^1.2.0":
  "integrity" "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  "version" "1.2.0"

"graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.3", "has-symbols@^1.1.0":
  "integrity" "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  "version" "1.1.0"

"has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"hast-util-to-jsx-runtime@^2.0.0":
  "integrity" "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg=="
  "resolved" "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    "comma-separated-tokens" "^2.0.0"
    "devlop" "^1.0.0"
    "estree-util-is-identifier-name" "^3.0.0"
    "hast-util-whitespace" "^3.0.0"
    "mdast-util-mdx-expression" "^2.0.0"
    "mdast-util-mdx-jsx" "^3.0.0"
    "mdast-util-mdxjs-esm" "^2.0.0"
    "property-information" "^7.0.0"
    "space-separated-tokens" "^2.0.0"
    "style-to-js" "^1.0.0"
    "unist-util-position" "^5.0.0"
    "vfile-message" "^4.0.0"

"hast-util-whitespace@^3.0.0":
  "integrity" "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw=="
  "resolved" "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/hast" "^3.0.0"

"html-encoding-sniffer@^4.0.0":
  "integrity" "sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ=="
  "resolved" "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "whatwg-encoding" "^3.1.1"

"html-parse-stringify@^3.0.1":
  "integrity" "sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg=="
  "resolved" "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "void-elements" "3.1.0"

"html-url-attributes@^3.0.0":
  "integrity" "sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ=="
  "resolved" "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz"
  "version" "3.0.1"

"html2canvas@*", "html2canvas@^1.0.0-rc.5", "html2canvas@^1.4.1":
  "integrity" "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA=="
  "resolved" "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "css-line-break" "^2.1.0"
    "text-segmentation" "^1.0.3"

"http-proxy-agent@^7.0.2":
  "integrity" "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig=="
  "resolved" "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "agent-base" "^7.1.0"
    "debug" "^4.3.4"

"https-proxy-agent@^7.0.6":
  "integrity" "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw=="
  "resolved" "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "agent-base" "^7.1.2"
    "debug" "4"

"husky@^9.1.7":
  "integrity" "sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA=="
  "resolved" "https://registry.npmjs.org/husky/-/husky-9.1.7.tgz"
  "version" "9.1.7"

"i18next-browser-languagedetector@^8.1.0":
  "integrity" "sha512-mHZxNx1Lq09xt5kCauZ/4bsXOEA2pfpwSoU11/QTJB+pD94iONFwp+ohqi///PwiFvjFOxe1akYCdHyFo1ng5Q=="
  "resolved" "https://registry.npmjs.org/i18next-browser-languagedetector/-/i18next-browser-languagedetector-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "@babel/runtime" "^7.23.2"

"i18next-http-backend@^3.0.2":
  "integrity" "sha512-PdlvPnvIp4E1sYi46Ik4tBYh/v/NbYfFFgTjkwFl0is8A18s7/bx9aXqsrOax9WUbeNS6mD2oix7Z0yGGf6m5g=="
  "resolved" "https://registry.npmjs.org/i18next-http-backend/-/i18next-http-backend-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "cross-fetch" "4.0.0"

"i18next@^25.2.1", "i18next@>= 23.2.3":
  "integrity" "sha512-+UoXK5wh+VlE1Zy5p6MjcvctHXAhRwQKCxiJD8noKZzIXmnAX8gdHX5fLPA3MEVxEN4vbZkQFy8N0LyD9tUqPw=="
  "resolved" "https://registry.npmjs.org/i18next/-/i18next-25.2.1.tgz"
  "version" "25.2.1"
  dependencies:
    "@babel/runtime" "^7.27.1"

"iconv-lite@0.6.3":
  "integrity" "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"immediate@~3.0.5":
  "integrity" "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="
  "resolved" "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz"
  "version" "3.0.6"

"inherits@~2.0.3":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inline-style-parser@0.2.4":
  "integrity" "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q=="
  "resolved" "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz"
  "version" "0.2.4"

"is-alphabetical@^2.0.0":
  "integrity" "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ=="
  "resolved" "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz"
  "version" "2.0.1"

"is-alphanumerical@^2.0.0":
  "integrity" "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw=="
  "resolved" "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-alphabetical" "^2.0.0"
    "is-decimal" "^2.0.0"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-decimal@^2.0.0":
  "integrity" "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A=="
  "resolved" "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz"
  "version" "2.0.1"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="
  "resolved" "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-hexadecimal@^2.0.0":
  "integrity" "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg=="
  "resolved" "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz"
  "version" "2.0.1"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-plain-obj@^4.0.0":
  "integrity" "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg=="
  "resolved" "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz"
  "version" "4.1.0"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ=="
  "resolved" "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"isarray@~1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"jiti@^2.4.2", "jiti@>=1.21.0":
  "integrity" "sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A=="
  "resolved" "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz"
  "version" "2.4.2"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"jsdom@*", "jsdom@^26.0.0":
  "integrity" "sha512-Cvc9WUhxSMEo4McES3P7oK3QaXldCfNWp7pl2NNeiIFlCoLr3kfq9kb1fxftiwk1FLV7CvpvDfonxtzUDeSOPg=="
  "resolved" "https://registry.npmjs.org/jsdom/-/jsdom-26.1.0.tgz"
  "version" "26.1.0"
  dependencies:
    "cssstyle" "^4.2.1"
    "data-urls" "^5.0.0"
    "decimal.js" "^10.5.0"
    "html-encoding-sniffer" "^4.0.0"
    "http-proxy-agent" "^7.0.2"
    "https-proxy-agent" "^7.0.6"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.16"
    "parse5" "^7.2.1"
    "rrweb-cssom" "^0.8.0"
    "saxes" "^6.0.0"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^5.1.1"
    "w3c-xmlserializer" "^5.0.0"
    "webidl-conversions" "^7.0.0"
    "whatwg-encoding" "^3.1.1"
    "whatwg-mimetype" "^4.0.0"
    "whatwg-url" "^14.1.1"
    "ws" "^8.18.0"
    "xml-name-validator" "^5.0.0"

"jsesc@^3.0.2", "jsesc@^3.1.0":
  "integrity" "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  "version" "3.1.0"

"json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"jspdf@^3.0.1":
  "integrity" "sha512-qaGIxqxetdoNnFQQXxTKUD9/Z7AloLaw94fFsOiJMxbfYdBbrBuhWmbzI8TVjrw7s3jBY1PFHofBKMV/wZPapg=="
  "resolved" "https://registry.npmjs.org/jspdf/-/jspdf-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@babel/runtime" "^7.26.7"
    "atob" "^2.1.2"
    "btoa" "^1.2.1"
    "fflate" "^0.8.1"
  optionalDependencies:
    "canvg" "^3.0.11"
    "core-js" "^3.6.0"
    "dompurify" "^3.2.4"
    "html2canvas" "^1.0.0-rc.5"

"jszip@*", "jszip@^3.10.1":
  "integrity" "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g=="
  "resolved" "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "lie" "~3.3.0"
    "pako" "~1.0.2"
    "readable-stream" "~2.3.6"
    "setimmediate" "^1.0.5"

"lie@~3.3.0":
  "integrity" "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ=="
  "resolved" "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "immediate" "~3.0.5"

"lightningcss-win32-x64-msvc@1.30.1":
  "integrity" "sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg=="
  "resolved" "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz"
  "version" "1.30.1"

"lightningcss@^1.21.0", "lightningcss@1.30.1":
  "integrity" "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg=="
  "resolved" "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz"
  "version" "1.30.1"
  dependencies:
    "detect-libc" "^2.0.3"
  optionalDependencies:
    "lightningcss-darwin-arm64" "1.30.1"
    "lightningcss-darwin-x64" "1.30.1"
    "lightningcss-freebsd-x64" "1.30.1"
    "lightningcss-linux-arm-gnueabihf" "1.30.1"
    "lightningcss-linux-arm64-gnu" "1.30.1"
    "lightningcss-linux-arm64-musl" "1.30.1"
    "lightningcss-linux-x64-gnu" "1.30.1"
    "lightningcss-linux-x64-musl" "1.30.1"
    "lightningcss-win32-arm64-msvc" "1.30.1"
    "lightningcss-win32-x64-msvc" "1.30.1"

"locate-path@^5.0.0":
  "integrity" "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"longest-streak@^3.0.0":
  "integrity" "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g=="
  "resolved" "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz"
  "version" "3.1.0"

"loose-envify@^1.4.0":
  "integrity" "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  "resolved" "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"loupe@^3.1.0", "loupe@^3.1.3":
  "integrity" "sha512-kkIp7XSkP78ZxJEsSxW3712C6teJVoeHHwgo9zJ380de7IYyJ2ISlxojcH2pC5OFLewESmnRi/+XCDIEEVyoug=="
  "resolved" "https://registry.npmjs.org/loupe/-/loupe-3.1.3.tgz"
  "version" "3.1.3"

"lru-cache@^10.4.3":
  "integrity" "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  "version" "10.4.3"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lucide-react@^0.511.0":
  "integrity" "sha512-VK5a2ydJ7xm8GvBeKLS9mu1pVK6ucef9780JVUjw6bAjJL/QXnd4Y0p7SPeOUMC27YhzNCZvm5d/QX0Tp3rc0w=="
  "resolved" "https://registry.npmjs.org/lucide-react/-/lucide-react-0.511.0.tgz"
  "version" "0.511.0"

"lz-string@^1.5.0":
  "integrity" "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ=="
  "resolved" "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz"
  "version" "1.5.0"

"magic-string@^0.30.17":
  "integrity" "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"
  "version" "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

"markdown-table@^3.0.0":
  "integrity" "sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw=="
  "resolved" "https://registry.npmjs.org/markdown-table/-/markdown-table-3.0.4.tgz"
  "version" "3.0.4"

"math-intrinsics@^1.1.0":
  "integrity" "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="
  "resolved" "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  "version" "1.1.0"

"mdast-util-find-and-replace@^3.0.0":
  "integrity" "sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg=="
  "resolved" "https://registry.npmjs.org/mdast-util-find-and-replace/-/mdast-util-find-and-replace-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "escape-string-regexp" "^5.0.0"
    "unist-util-is" "^6.0.0"
    "unist-util-visit-parents" "^6.0.0"

"mdast-util-from-markdown@^2.0.0":
  "integrity" "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA=="
  "resolved" "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "mdast-util-to-string" "^4.0.0"
    "micromark" "^4.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-decode-string" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unist-util-stringify-position" "^4.0.0"

"mdast-util-gfm-autolink-literal@^2.0.0":
  "integrity" "sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm-autolink-literal/-/mdast-util-gfm-autolink-literal-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    "ccount" "^2.0.0"
    "devlop" "^1.0.0"
    "mdast-util-find-and-replace" "^3.0.0"
    "micromark-util-character" "^2.0.0"

"mdast-util-gfm-footnote@^2.0.0":
  "integrity" "sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm-footnote/-/mdast-util-gfm-footnote-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.1.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"

"mdast-util-gfm-strikethrough@^2.0.0":
  "integrity" "sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm-strikethrough/-/mdast-util-gfm-strikethrough-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm-table@^2.0.0":
  "integrity" "sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm-table/-/mdast-util-gfm-table-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "markdown-table" "^3.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm-task-list-item@^2.0.0":
  "integrity" "sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm-task-list-item/-/mdast-util-gfm-task-list-item-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-gfm@^3.0.0":
  "integrity" "sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ=="
  "resolved" "https://registry.npmjs.org/mdast-util-gfm/-/mdast-util-gfm-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-gfm-autolink-literal" "^2.0.0"
    "mdast-util-gfm-footnote" "^2.0.0"
    "mdast-util-gfm-strikethrough" "^2.0.0"
    "mdast-util-gfm-table" "^2.0.0"
    "mdast-util-gfm-task-list-item" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-mdx-expression@^2.0.0":
  "integrity" "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ=="
  "resolved" "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-mdx-jsx@^3.0.0":
  "integrity" "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q=="
  "resolved" "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "ccount" "^2.0.0"
    "devlop" "^1.1.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "parse-entities" "^4.0.0"
    "stringify-entities" "^4.0.0"
    "unist-util-stringify-position" "^4.0.0"
    "vfile-message" "^4.0.0"

"mdast-util-mdxjs-esm@^2.0.0":
  "integrity" "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg=="
  "resolved" "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "mdast-util-to-markdown" "^2.0.0"

"mdast-util-phrasing@^4.0.0":
  "integrity" "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w=="
  "resolved" "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "unist-util-is" "^6.0.0"

"mdast-util-to-hast@^13.0.0":
  "integrity" "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA=="
  "resolved" "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz"
  "version" "13.2.0"
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "trim-lines" "^3.0.0"
    "unist-util-position" "^5.0.0"
    "unist-util-visit" "^5.0.0"
    "vfile" "^6.0.0"

"mdast-util-to-markdown@^2.0.0":
  "integrity" "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA=="
  "resolved" "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    "longest-streak" "^3.0.0"
    "mdast-util-phrasing" "^4.0.0"
    "mdast-util-to-string" "^4.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-decode-string" "^2.0.0"
    "unist-util-visit" "^5.0.0"
    "zwitch" "^2.0.0"

"mdast-util-to-string@^4.0.0":
  "integrity" "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg=="
  "resolved" "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"

"micromark-core-commonmark@^2.0.0":
  "integrity" "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg=="
  "resolved" "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-factory-destination" "^2.0.0"
    "micromark-factory-label" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-factory-title" "^2.0.0"
    "micromark-factory-whitespace" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-html-tag-name" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-subtokenize" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-autolink-literal@^2.0.0":
  "integrity" "sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-autolink-literal/-/micromark-extension-gfm-autolink-literal-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-footnote@^2.0.0":
  "integrity" "sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-footnote/-/micromark-extension-gfm-footnote-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-core-commonmark" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-strikethrough@^2.0.0":
  "integrity" "sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-strikethrough/-/micromark-extension-gfm-strikethrough-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-classify-character" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-table@^2.0.0":
  "integrity" "sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-table/-/micromark-extension-gfm-table-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-tagfilter@^2.0.0":
  "integrity" "sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-tagfilter/-/micromark-extension-gfm-tagfilter-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm-task-list-item@^2.0.0":
  "integrity" "sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm-task-list-item/-/micromark-extension-gfm-task-list-item-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-extension-gfm@^3.0.0":
  "integrity" "sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w=="
  "resolved" "https://registry.npmjs.org/micromark-extension-gfm/-/micromark-extension-gfm-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "micromark-extension-gfm-autolink-literal" "^2.0.0"
    "micromark-extension-gfm-footnote" "^2.0.0"
    "micromark-extension-gfm-strikethrough" "^2.0.0"
    "micromark-extension-gfm-table" "^2.0.0"
    "micromark-extension-gfm-tagfilter" "^2.0.0"
    "micromark-extension-gfm-task-list-item" "^2.0.0"
    "micromark-util-combine-extensions" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-destination@^2.0.0":
  "integrity" "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA=="
  "resolved" "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-label@^2.0.0":
  "integrity" "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg=="
  "resolved" "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-space@^2.0.0":
  "integrity" "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg=="
  "resolved" "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-title@^2.0.0":
  "integrity" "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw=="
  "resolved" "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-factory-whitespace@^2.0.0":
  "integrity" "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ=="
  "resolved" "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-character@^2.0.0":
  "integrity" "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q=="
  "resolved" "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-chunked@^2.0.0":
  "integrity" "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA=="
  "resolved" "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-classify-character@^2.0.0":
  "integrity" "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q=="
  "resolved" "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-combine-extensions@^2.0.0":
  "integrity" "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg=="
  "resolved" "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-decode-numeric-character-reference@^2.0.0":
  "integrity" "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw=="
  "resolved" "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-decode-string@^2.0.0":
  "integrity" "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ=="
  "resolved" "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "decode-named-character-reference" "^1.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"

"micromark-util-encode@^2.0.0":
  "integrity" "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw=="
  "resolved" "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-html-tag-name@^2.0.0":
  "integrity" "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA=="
  "resolved" "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-normalize-identifier@^2.0.0":
  "integrity" "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q=="
  "resolved" "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-symbol" "^2.0.0"

"micromark-util-resolve-all@^2.0.0":
  "integrity" "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg=="
  "resolved" "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-types" "^2.0.0"

"micromark-util-sanitize-uri@^2.0.0":
  "integrity" "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ=="
  "resolved" "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "micromark-util-character" "^2.0.0"
    "micromark-util-encode" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"

"micromark-util-subtokenize@^2.0.0":
  "integrity" "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA=="
  "resolved" "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "devlop" "^1.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"micromark-util-symbol@^2.0.0":
  "integrity" "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q=="
  "resolved" "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz"
  "version" "2.0.1"

"micromark-util-types@^2.0.0":
  "integrity" "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA=="
  "resolved" "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz"
  "version" "2.0.2"

"micromark@^4.0.0":
  "integrity" "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA=="
  "resolved" "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/debug" "^4.0.0"
    "debug" "^4.0.0"
    "decode-named-character-reference" "^1.0.0"
    "devlop" "^1.0.0"
    "micromark-core-commonmark" "^2.0.0"
    "micromark-factory-space" "^2.0.0"
    "micromark-util-character" "^2.0.0"
    "micromark-util-chunked" "^2.0.0"
    "micromark-util-combine-extensions" "^2.0.0"
    "micromark-util-decode-numeric-character-reference" "^2.0.0"
    "micromark-util-encode" "^2.0.0"
    "micromark-util-normalize-identifier" "^2.0.0"
    "micromark-util-resolve-all" "^2.0.0"
    "micromark-util-sanitize-uri" "^2.0.0"
    "micromark-util-subtokenize" "^2.0.0"
    "micromark-util-symbol" "^2.0.0"
    "micromark-util-types" "^2.0.0"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"minipass@^7.0.4", "minipass@^7.1.2":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"minizlib@^3.0.1":
  "integrity" "sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA=="
  "resolved" "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "minipass" "^7.1.2"

"mkdirp@^3.0.1":
  "integrity" "sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz"
  "version" "3.0.1"

"ms@^2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"nanoid@^3.3.8":
  "integrity" "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  "version" "3.3.11"

"node-fetch@^2.6.12":
  "integrity" "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A=="
  "resolved" "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-releases@^2.0.19":
  "integrity" "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  "version" "2.0.19"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"nwsapi@^2.2.16":
  "integrity" "sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA=="
  "resolved" "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz"
  "version" "2.2.20"

"object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"p-limit@^2.2.0":
  "integrity" "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-try@^2.0.0":
  "integrity" "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="
  "resolved" "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  "version" "2.2.0"

"pako@~1.0.2":
  "integrity" "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw=="
  "resolved" "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz"
  "version" "1.0.11"

"parse-entities@^4.0.0":
  "integrity" "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw=="
  "resolved" "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/unist" "^2.0.0"
    "character-entities-legacy" "^3.0.0"
    "character-reference-invalid" "^2.0.0"
    "decode-named-character-reference" "^1.0.0"
    "is-alphanumerical" "^2.0.0"
    "is-decimal" "^2.0.0"
    "is-hexadecimal" "^2.0.0"

"parse5@^7.2.1":
  "integrity" "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw=="
  "resolved" "https://registry.npmjs.org/parse5/-/parse5-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "entities" "^6.0.0"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"pathe@^2.0.3":
  "integrity" "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w=="
  "resolved" "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz"
  "version" "2.0.3"

"pathval@^2.0.0":
  "integrity" "sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA=="
  "resolved" "https://registry.npmjs.org/pathval/-/pathval-2.0.0.tgz"
  "version" "2.0.0"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^2.0.4", "picomatch@^2.2.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"picomatch@^3 || ^4", "picomatch@^4.0.2":
  "integrity" "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  "version" "4.0.2"

"pngjs@^5.0.0":
  "integrity" "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw=="
  "resolved" "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz"
  "version" "5.0.0"

"postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^8.1.0", "postcss@^8.4.41", "postcss@^8.5.3":
  "integrity" "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz"
  "version" "8.5.3"
  dependencies:
    "nanoid" "^3.3.8"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"prettier@^3.5.0":
  "integrity" "sha512-QQtaxnoDJeAkDvDKWCLiwIXkTgRhwYDEQCghU9Z6q03iyek/rxRh/2lC3HB7P8sWT2xC/y5JDctPLBIGzHKbhw=="
  "resolved" "https://registry.npmjs.org/prettier/-/prettier-3.5.3.tgz"
  "version" "3.5.3"

"pretty-format@^27.0.2":
  "integrity" "sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ=="
  "resolved" "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz"
  "version" "27.5.1"
  dependencies:
    "ansi-regex" "^5.0.1"
    "ansi-styles" "^5.0.0"
    "react-is" "^17.0.1"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"prop-types@^15.8.1":
  "integrity" "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  "resolved" "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"property-information@^7.0.0":
  "integrity" "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ=="
  "resolved" "https://registry.npmjs.org/property-information/-/property-information-7.1.0.tgz"
  "version" "7.1.0"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"punycode@^2.3.1":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"qr.js@0.0.0":
  "integrity" "sha512-c4iYnWb+k2E+vYpRimHqSu575b1/wKl4XFeJGpFmrJQz5I88v9aY2czh7s0w36srfCM1sXgC/xpoJz5dJfq+OQ=="
  "resolved" "https://registry.npmjs.org/qr.js/-/qr.js-0.0.0.tgz"
  "version" "0.0.0"

"qrcode.react@^4.2.0":
  "integrity" "sha512-QpgqWi8rD9DsS9EP3z7BT+5lY5SFhsqGjpgW5DY/i3mK4M9DTBNz3ErMi8BWYEfI3L0d8GIbGmcdFAS1uIRGjA=="
  "resolved" "https://registry.npmjs.org/qrcode.react/-/qrcode.react-4.2.0.tgz"
  "version" "4.2.0"

"qrcode@^1.5.4":
  "integrity" "sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg=="
  "resolved" "https://registry.npmjs.org/qrcode/-/qrcode-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "dijkstrajs" "^1.0.1"
    "pngjs" "^5.0.0"
    "yargs" "^15.3.1"

"raf@^3.4.1":
  "integrity" "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA=="
  "resolved" "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "performance-now" "^2.1.0"

"react-day-picker@^8.10.1":
  "integrity" "sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA=="
  "resolved" "https://registry.npmjs.org/react-day-picker/-/react-day-picker-8.10.1.tgz"
  "version" "8.10.1"

"react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom@^18 || ^19 || ^19.0.0-rc", "react-dom@^18.0.0 || ^19.0.0", "react-dom@^19.0.0", "react-dom@>=16.8.0", "react-dom@>=18.0.0 || >=19.0.0":
  "integrity" "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz"
  "version" "19.1.0"
  dependencies:
    "scheduler" "^0.26.0"

"react-hook-form@^7.55.0", "react-hook-form@^7.56.4":
  "integrity" "sha512-Rob7Ftz2vyZ/ZGsQZPaRdIefkgOSrQSPXfqBdvOPwJfoGnjwRJUs7EM7Kc1mcoDv3NOtqBzPGbcMB8CGn9CKgw=="
  "resolved" "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.56.4.tgz"
  "version" "7.56.4"

"react-i18next@^15.5.2":
  "integrity" "sha512-ePODyXgmZQAOYTbZXQn5rRsSBu3Gszo69jxW6aKmlSgxKAI1fOhDwSu6bT4EKHciWPKQ7v7lPrjeiadR6Gi+1A=="
  "resolved" "https://registry.npmjs.org/react-i18next/-/react-i18next-15.5.2.tgz"
  "version" "15.5.2"
  dependencies:
    "@babel/runtime" "^7.25.0"
    "html-parse-stringify" "^3.0.1"

"react-is@^16.13.1":
  "integrity" "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^17.0.1":
  "integrity" "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-markdown@^10.1.0":
  "integrity" "sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ=="
  "resolved" "https://registry.npmjs.org/react-markdown/-/react-markdown-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "devlop" "^1.0.0"
    "hast-util-to-jsx-runtime" "^2.0.0"
    "html-url-attributes" "^3.0.0"
    "mdast-util-to-hast" "^13.0.0"
    "remark-parse" "^11.0.0"
    "remark-rehype" "^11.0.0"
    "unified" "^11.0.0"
    "unist-util-visit" "^5.0.0"
    "vfile" "^6.0.0"

"react-qr-code@^2.0.15":
  "integrity" "sha512-MkZcjEXqVKqXEIMVE0mbcGgDpkfSdd8zhuzXEl9QzYeNcw8Hq2oVIzDLWuZN2PQBwM5PWjc2S31K8Q1UbcFMfw=="
  "resolved" "https://registry.npmjs.org/react-qr-code/-/react-qr-code-2.0.15.tgz"
  "version" "2.0.15"
  dependencies:
    "prop-types" "^15.8.1"
    "qr.js" "0.0.0"

"react-refresh@^0.17.0":
  "integrity" "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ=="
  "resolved" "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz"
  "version" "0.17.0"

"react-remove-scroll-bar@^2.3.7":
  "integrity" "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q=="
  "resolved" "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "react-style-singleton" "^2.2.2"
    "tslib" "^2.0.0"

"react-remove-scroll@^2.6.3":
  "integrity" "sha512-sGsQtcjMqdQyijAHytfGEELB8FufGbfXIsvUTe+NLx1GDRJCXtCFLBLUI1eyZCKXXvbEU2C6gai0PZKoIE9Vbg=="
  "resolved" "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "react-remove-scroll-bar" "^2.3.7"
    "react-style-singleton" "^2.2.3"
    "tslib" "^2.1.0"
    "use-callback-ref" "^1.3.3"
    "use-sidecar" "^1.1.3"

"react-style-singleton@^2.2.2", "react-style-singleton@^2.2.3":
  "integrity" "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ=="
  "resolved" "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "get-nonce" "^1.0.0"
    "tslib" "^2.0.0"

"react@*", "react@^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^18 || ^19", "react@^18 || ^19 || ^19.0.0-rc", "react@^18.0.0 || ^19.0.0", "react@^19.0.0", "react@^19.1.0", "react@>= 16 || ^19.0.0-rc", "react@>= 16.8.0", "react@>=16.8.0", "react@>=18", "react@>=18.0.0", "react@>=18.0.0 || >=19.0.0":
  "integrity" "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg=="
  "resolved" "https://registry.npmjs.org/react/-/react-19.1.0.tgz"
  "version" "19.1.0"

"readable-stream@~2.3.6":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerator-runtime@^0.13.7":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"remark-gfm@^4.0.1":
  "integrity" "sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg=="
  "resolved" "https://registry.npmjs.org/remark-gfm/-/remark-gfm-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-gfm" "^3.0.0"
    "micromark-extension-gfm" "^3.0.0"
    "remark-parse" "^11.0.0"
    "remark-stringify" "^11.0.0"
    "unified" "^11.0.0"

"remark-parse@^11.0.0":
  "integrity" "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA=="
  "resolved" "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-from-markdown" "^2.0.0"
    "micromark-util-types" "^2.0.0"
    "unified" "^11.0.0"

"remark-rehype@^11.0.0":
  "integrity" "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw=="
  "resolved" "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz"
  "version" "11.1.2"
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "mdast-util-to-hast" "^13.0.0"
    "unified" "^11.0.0"
    "vfile" "^6.0.0"

"remark-stringify@^11.0.0":
  "integrity" "sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw=="
  "resolved" "https://registry.npmjs.org/remark-stringify/-/remark-stringify-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    "mdast-util-to-markdown" "^2.0.0"
    "unified" "^11.0.0"

"require-directory@^2.1.1":
  "integrity" "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q=="
  "resolved" "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"resolve-pkg-maps@^1.0.0":
  "integrity" "sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw=="
  "resolved" "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
  "version" "1.0.0"

"rgbcolor@^1.0.1":
  "integrity" "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw=="
  "resolved" "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  "version" "1.0.1"

"rollup@npm:@rollup/wasm-node":
  "integrity" "sha512-70qfem+U3hAgwNgOlnUQiIdfKHLELUxsEWbFWg3aErPUvsyXYF1HALJBwoDgMUhRWyn+SqWVneDTnO/Kbey9hg=="
  "resolved" "https://registry.npmjs.org/@rollup/wasm-node/-/wasm-node-4.41.1.tgz"
  "version" "4.41.1"
  dependencies:
    "@types/estree" "1.0.7"
  optionalDependencies:
    "fsevents" "~2.3.2"

"rrweb-cssom@^0.8.0":
  "integrity" "sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw=="
  "resolved" "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.8.0.tgz"
  "version" "0.8.0"

"safe-buffer@~5.1.0", "safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"saxes@^6.0.0":
  "integrity" "sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA=="
  "resolved" "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.26.0":
  "integrity" "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz"
  "version" "0.26.0"

"semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"seroval-plugins@~1.3.0":
  "integrity" "sha512-0QvCV2lM3aj/U3YozDiVwx9zpH0q8A60CTWIv4Jszj/givcudPb48B+rkU5D51NJ0pTpweGMttHjboPa9/zoIQ=="
  "resolved" "https://registry.npmjs.org/seroval-plugins/-/seroval-plugins-1.3.2.tgz"
  "version" "1.3.2"

"seroval@^1.0", "seroval@~1.3.0":
  "integrity" "sha512-RbcPH1n5cfwKrru7v7+zrZvjLurgHhGyso3HTyGtRivGWgYjbOmGuivCQaORNELjNONoK35nj28EoWul9sb1zQ=="
  "resolved" "https://registry.npmjs.org/seroval/-/seroval-1.3.2.tgz"
  "version" "1.3.2"

"set-blocking@^2.0.0":
  "integrity" "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw=="
  "resolved" "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"setimmediate@^1.0.5":
  "integrity" "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA=="
  "resolved" "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"siginfo@^2.0.0":
  "integrity" "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g=="
  "resolved" "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz"
  "version" "2.0.0"

"solid-js@^1.9.5", "solid-js@>=1.9.5":
  "integrity" "sha512-/saTKi8iWEM233n5OSi1YHCCuh66ZIQ7aK2hsToPe4tqGm7qAejU1SwNuTPivbWAYq7SjuHVVYxxuZQNRbICiw=="
  "resolved" "https://registry.npmjs.org/solid-js/-/solid-js-1.9.7.tgz"
  "version" "1.9.7"
  dependencies:
    "csstype" "^3.1.0"
    "seroval" "~1.3.0"
    "seroval-plugins" "~1.3.0"

"source-map-js@^1.2.1":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"space-separated-tokens@^2.0.0":
  "integrity" "sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q=="
  "resolved" "https://registry.npmjs.org/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz"
  "version" "2.0.2"

"stackback@0.0.2":
  "integrity" "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw=="
  "resolved" "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz"
  "version" "0.0.2"

"stackblur-canvas@^2.0.0":
  "integrity" "sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ=="
  "resolved" "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz"
  "version" "2.7.0"

"std-env@^3.9.0":
  "integrity" "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw=="
  "resolved" "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz"
  "version" "3.9.0"

"string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0":
  "integrity" "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  "resolved" "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"stringify-entities@^4.0.0":
  "integrity" "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg=="
  "resolved" "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "character-entities-html4" "^2.0.0"
    "character-entities-legacy" "^3.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A=="
  "resolved" "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"style-to-js@^1.0.0":
  "integrity" "sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw=="
  "resolved" "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.16.tgz"
  "version" "1.1.16"
  dependencies:
    "style-to-object" "1.0.8"

"style-to-object@1.0.8":
  "integrity" "sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g=="
  "resolved" "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "inline-style-parser" "0.2.4"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"svg-pathdata@^6.0.3":
  "integrity" "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw=="
  "resolved" "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  "version" "6.0.3"

"symbol-tree@^3.2.4":
  "integrity" "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw=="
  "resolved" "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"tailwind-merge@^3.3.1":
  "integrity" "sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g=="
  "resolved" "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.3.1.tgz"
  "version" "3.3.1"

"tailwindcss@^4.1.7", "tailwindcss@4.1.7":
  "integrity" "sha512-kr1o/ErIdNhTz8uzAYL7TpaUuzKIE6QPQ4qmSdxnoX/lo+5wmUHQA6h3L5yIqEImSRnAAURDirLu/BgiXGPAhg=="
  "resolved" "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.7.tgz"
  "version" "4.1.7"

"tapable@^2.2.0":
  "integrity" "sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz"
  "version" "2.2.2"

"tar@^7.4.3":
  "integrity" "sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw=="
  "resolved" "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz"
  "version" "7.4.3"
  dependencies:
    "@isaacs/fs-minipass" "^4.0.0"
    "chownr" "^3.0.0"
    "minipass" "^7.1.2"
    "minizlib" "^3.0.1"
    "mkdirp" "^3.0.1"
    "yallist" "^5.0.0"

"text-segmentation@^1.0.3":
  "integrity" "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw=="
  "resolved" "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "utrie" "^1.0.2"

"tiny-invariant@^1.3.3":
  "integrity" "sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg=="
  "resolved" "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz"
  "version" "1.3.3"

"tiny-warning@^1.0.3":
  "integrity" "sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="
  "resolved" "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  "version" "1.0.3"

"tinybench@^2.9.0":
  "integrity" "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg=="
  "resolved" "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz"
  "version" "2.9.0"

"tinyexec@^0.3.2":
  "integrity" "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA=="
  "resolved" "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.2.tgz"
  "version" "0.3.2"

"tinyglobby@^0.2.13":
  "integrity" "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ=="
  "resolved" "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz"
  "version" "0.2.14"
  dependencies:
    "fdir" "^6.4.4"
    "picomatch" "^4.0.2"

"tinypool@^1.0.2":
  "integrity" "sha512-al6n+QEANGFOMf/dmUMsuS5/r9B06uwlyNjZZql/zv8J7ybHCgoihBNORZCY2mzUuAnomQa2JdhyHKzZxPCrFA=="
  "resolved" "https://registry.npmjs.org/tinypool/-/tinypool-1.0.2.tgz"
  "version" "1.0.2"

"tinyrainbow@^2.0.0":
  "integrity" "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw=="
  "resolved" "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-2.0.0.tgz"
  "version" "2.0.0"

"tinyspy@^3.0.2":
  "integrity" "sha512-n1cw8k1k0x4pgA2+9XrOkFydTerNcJ1zWCO5Nn9scWHTD+5tp8dghT2x1uduQePZTZgd3Tupf+x9BxJjeJi77Q=="
  "resolved" "https://registry.npmjs.org/tinyspy/-/tinyspy-3.0.2.tgz"
  "version" "3.0.2"

"tldts-core@^6.1.86":
  "integrity" "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA=="
  "resolved" "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.86.tgz"
  "version" "6.1.86"

"tldts@^6.1.32":
  "integrity" "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ=="
  "resolved" "https://registry.npmjs.org/tldts/-/tldts-6.1.86.tgz"
  "version" "6.1.86"
  dependencies:
    "tldts-core" "^6.1.86"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"tough-cookie@^5.1.1":
  "integrity" "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "tldts" "^6.1.32"

"tr46@^5.1.0":
  "integrity" "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "punycode" "^2.3.1"

"tr46@~0.0.3":
  "integrity" "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  "version" "0.0.3"

"trim-lines@^3.0.0":
  "integrity" "sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg=="
  "resolved" "https://registry.npmjs.org/trim-lines/-/trim-lines-3.0.1.tgz"
  "version" "3.0.1"

"trough@^2.0.0":
  "integrity" "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw=="
  "resolved" "https://registry.npmjs.org/trough/-/trough-2.2.0.tgz"
  "version" "2.2.0"

"tslib@^2.0.0", "tslib@^2.1.0":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"tsx@^4.19.2", "tsx@^4.8.1":
  "integrity" "sha512-gK5GVzDkJK1SI1zwHf32Mqxf2tSJkNx+eYcNly5+nHvWqXUJYUkWBQtKauoESz3ymezAI++ZwT855x5p5eop+Q=="
  "resolved" "https://registry.npmjs.org/tsx/-/tsx-4.19.4.tgz"
  "version" "4.19.4"
  dependencies:
    "esbuild" "~0.25.0"
    "get-tsconfig" "^4.7.5"
  optionalDependencies:
    "fsevents" "~2.3.3"

"tw-animate-css@^1.3.0":
  "integrity" "sha512-jrJ0XenzS9KVuDThJDvnhalbl4IYiMQ/XvpA0a2FL8KmlK+6CSMviO7ROY/I7z1NnUs5NnDhlM6fXmF40xPxzw=="
  "resolved" "https://registry.npmjs.org/tw-animate-css/-/tw-animate-css-1.3.0.tgz"
  "version" "1.3.0"

"typescript@^5", "typescript@^5.7.2":
  "integrity" "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ=="
  "resolved" "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz"
  "version" "5.8.3"

"undici-types@~6.21.0":
  "integrity" "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ=="
  "resolved" "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  "version" "6.21.0"

"unified@^11.0.0":
  "integrity" "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA=="
  "resolved" "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz"
  "version" "11.0.5"
  dependencies:
    "@types/unist" "^3.0.0"
    "bail" "^2.0.0"
    "devlop" "^1.0.0"
    "extend" "^3.0.0"
    "is-plain-obj" "^4.0.0"
    "trough" "^2.0.0"
    "vfile" "^6.0.0"

"unist-util-is@^6.0.0":
  "integrity" "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw=="
  "resolved" "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-position@^5.0.0":
  "integrity" "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA=="
  "resolved" "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-stringify-position@^4.0.0":
  "integrity" "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ=="
  "resolved" "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

"unist-util-visit-parents@^6.0.0":
  "integrity" "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw=="
  "resolved" "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-is" "^6.0.0"

"unist-util-visit@^5.0.0":
  "integrity" "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg=="
  "resolved" "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-is" "^6.0.0"
    "unist-util-visit-parents" "^6.0.0"

"unplugin@^2.1.2":
  "integrity" "sha512-RyWSb5AHmGtjjNQ6gIlA67sHOsWpsbWpwDokLwTcejVdOjEkJZh7QKu14J00gDDVSh8kGH4KYC/TNBceXFZhtw=="
  "resolved" "https://registry.npmjs.org/unplugin/-/unplugin-2.3.5.tgz"
  "version" "2.3.5"
  dependencies:
    "acorn" "^8.14.1"
    "picomatch" "^4.0.2"
    "webpack-virtual-modules" "^0.6.2"

"update-browserslist-db@^1.1.3":
  "integrity" "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"use-callback-ref@^1.3.3":
  "integrity" "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg=="
  "resolved" "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "tslib" "^2.0.0"

"use-sidecar@^1.1.3":
  "integrity" "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ=="
  "resolved" "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "detect-node-es" "^1.1.0"
    "tslib" "^2.0.0"

"use-sync-external-store@^1.5.0", "use-sync-external-store@>=1.2.0":
  "integrity" "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A=="
  "resolved" "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz"
  "version" "1.5.0"

"util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utrie@^1.0.2":
  "integrity" "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw=="
  "resolved" "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "base64-arraybuffer" "^1.0.2"

"vfile-message@^4.0.0":
  "integrity" "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw=="
  "resolved" "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@types/unist" "^3.0.0"
    "unist-util-stringify-position" "^4.0.0"

"vfile@^6.0.0":
  "integrity" "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q=="
  "resolved" "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "@types/unist" "^3.0.0"
    "vfile-message" "^4.0.0"

"vite-node@3.1.4":
  "integrity" "sha512-6enNwYnpyDo4hEgytbmc6mYWHXDHYEn0D1/rw4Q+tnHUGtKTJsn8T1YkX6Q18wI5LCrS8CTYlBaiCqxOy2kvUA=="
  "resolved" "https://registry.npmjs.org/vite-node/-/vite-node-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "cac" "^6.7.14"
    "debug" "^4.4.0"
    "es-module-lexer" "^1.7.0"
    "pathe" "^2.0.3"
    "vite" "^5.0.0 || ^6.0.0"

"vite@^4.2.0 || ^5.0.0 || ^6.0.0", "vite@^5.0.0 || ^6.0.0", "vite@^5.2.0 || ^6", "vite@^6.1.0", "vite@>=5.0.0 || >=6.0.0":
  "integrity" "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ=="
  "resolved" "https://registry.npmjs.org/vite/-/vite-6.3.5.tgz"
  "version" "6.3.5"
  dependencies:
    "esbuild" "^0.25.0"
    "fdir" "^6.4.4"
    "picomatch" "^4.0.2"
    "postcss" "^8.5.3"
    "rollup" "^4.34.9"
    "tinyglobby" "^0.2.13"
  optionalDependencies:
    "fsevents" "~2.3.3"

"vitest@^3.0.5":
  "integrity" "sha512-Ta56rT7uWxCSJXlBtKgIlApJnT6e6IGmTYxYcmxjJ4ujuZDI59GUQgVDObXXJujOmPDBYXHK1qmaGtneu6TNIQ=="
  "resolved" "https://registry.npmjs.org/vitest/-/vitest-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@vitest/expect" "3.1.4"
    "@vitest/mocker" "3.1.4"
    "@vitest/pretty-format" "^3.1.4"
    "@vitest/runner" "3.1.4"
    "@vitest/snapshot" "3.1.4"
    "@vitest/spy" "3.1.4"
    "@vitest/utils" "3.1.4"
    "chai" "^5.2.0"
    "debug" "^4.4.0"
    "expect-type" "^1.2.1"
    "magic-string" "^0.30.17"
    "pathe" "^2.0.3"
    "std-env" "^3.9.0"
    "tinybench" "^2.9.0"
    "tinyexec" "^0.3.2"
    "tinyglobby" "^0.2.13"
    "tinypool" "^1.0.2"
    "tinyrainbow" "^2.0.0"
    "vite" "^5.0.0 || ^6.0.0"
    "vite-node" "3.1.4"
    "why-is-node-running" "^2.3.0"

"void-elements@3.1.0":
  "integrity" "sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w=="
  "resolved" "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz"
  "version" "3.1.0"

"w3c-xmlserializer@^5.0.0":
  "integrity" "sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA=="
  "resolved" "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "xml-name-validator" "^5.0.0"

"web-vitals@^4.2.4":
  "integrity" "sha512-r4DIlprAGwJ7YM11VZp4R884m0Vmgr6EAKe3P+kO0PPj3Unqyvv59rczf6UiGcb9Z8QxZVcqKNwv/g0WNdWwsw=="
  "resolved" "https://registry.npmjs.org/web-vitals/-/web-vitals-4.2.4.tgz"
  "version" "4.2.4"

"webidl-conversions@^3.0.0":
  "integrity" "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webidl-conversions@^7.0.0":
  "integrity" "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  "version" "7.0.0"

"webpack-virtual-modules@^0.6.2":
  "integrity" "sha512-66/V2i5hQanC51vBQKPH4aI8NMAcBW59FVBs+rC7eGHupMyfn34q7rZIE+ETlJ+XTevqfUhVVBgSUNSW2flEUQ=="
  "resolved" "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz"
  "version" "0.6.2"

"whatwg-encoding@^3.1.1":
  "integrity" "sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ=="
  "resolved" "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "iconv-lite" "0.6.3"

"whatwg-mimetype@^4.0.0":
  "integrity" "sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg=="
  "resolved" "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz"
  "version" "4.0.0"

"whatwg-url@^14.0.0", "whatwg-url@^14.1.1":
  "integrity" "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz"
  "version" "14.2.0"
  dependencies:
    "tr46" "^5.1.0"
    "webidl-conversions" "^7.0.0"

"whatwg-url@^5.0.0":
  "integrity" "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"which-module@^2.0.0":
  "integrity" "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ=="
  "resolved" "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz"
  "version" "2.0.1"

"why-is-node-running@^2.3.0":
  "integrity" "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w=="
  "resolved" "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "siginfo" "^2.0.0"
    "stackback" "0.0.2"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"ws@^8.18.0":
  "integrity" "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz"
  "version" "8.18.2"

"xml-name-validator@^5.0.0":
  "integrity" "sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg=="
  "resolved" "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-5.0.0.tgz"
  "version" "5.0.0"

"xmlchars@^2.2.0":
  "integrity" "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw=="
  "resolved" "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^5.0.0":
  "integrity" "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz"
  "version" "5.0.0"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs@^15.3.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"zod@^3.24.2", "zod@^3.25.32":
  "integrity" "sha512-OSm2xTIRfW8CV5/QKgngwmQW/8aPfGdaQFlrGoErlgg/Epm7cjb6K6VEyExfe65a3VybUOnu381edLb0dfJl0g=="
  "resolved" "https://registry.npmjs.org/zod/-/zod-3.25.32.tgz"
  "version" "3.25.32"

"zustand@^5.0.5":
  "integrity" "sha512-mILtRfKW9xM47hqxGIxCv12gXusoY/xTSHBYApXozR0HmQv299whhBeeAcRy+KrPPybzosvJBCOmVjq6x12fCg=="
  "resolved" "https://registry.npmjs.org/zustand/-/zustand-5.0.5.tgz"
  "version" "5.0.5"

"zwitch@^2.0.0":
  "integrity" "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A=="
  "resolved" "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz"
  "version" "2.0.4"
