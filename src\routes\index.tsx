import { createFileRoute } from "@tanstack/react-router";
import { z } from "zod";
import { HeroSection } from "@/components/home/<USER>";
import { SearchForm } from "@/components/home/<USER>";
import { SearchFormProvider } from "@/contexts/SearchFormContext";
import { PromotionSection } from "@/components/home/<USER>";
import { PromotionStoreSection } from "@/components/home/<USER>";
import { PopularRoutesSection } from "@/components/home/<USER>";
import { SupportSection } from "@/components/home/<USER>";
import { NewsSection } from "@/components/home/<USER>";
import { useSelectedTripsStore } from "@/stores/selectedTripsStore";
import { useEffect } from "react";

const homePageSearchSchema = z
  .object({
    fromPlaceId: z.string().optional(),
    toPlaceId: z.string().optional(),
    departureDate: z.string().optional(),
    numTickets: z.string().optional(),
  })
  .catchall(z.any());

export const Route = createFileRoute("/")({
  validateSearch: homePageSearchSchema,
  component: HomePage,
});

function HomePage() {
  const { clearTrips } = useSelectedTripsStore();

  useEffect(() => {
    clearTrips();
  }, []);

  return (
    <SearchFormProvider>
      <section className="relative w-full min-h-[173px] md:min-h-[400px] pt-[80px] pb-0 md:pb-32 bg-transparent md:bg-[#F4F6FB] ">
        <HeroSection />
        <SearchForm />
      </section>

      {/* Popular Routes Section */}
      <PopularRoutesSection />

      {/* Promotion Store Section */}
      <PromotionStoreSection />

      {/* Promotion Section */}
      <PromotionSection />

      {/* Support Section */}
      <SupportSection />

      {/* News Section */}
      <NewsSection />

      {/* Contact Advertise Section */}
      {/* <ContactAdvertiseSection /> */}
    </SearchFormProvider>
  );
}
