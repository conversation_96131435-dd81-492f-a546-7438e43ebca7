import React from "react";

// Simple utility function to combine class names
const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(" ");
};

// Reusable CSS classes
const FORM_FIELD_CONTAINER =
  "h-[50px] px-[16px] bg-gray-100 rounded-lg flex flex-col justify-center";
const FORM_LABEL =
  "block text-xs text-[#5C5C5C] font-bold leading-3 mb-1 cursor-text";
const FORM_INPUT =
  "w-full bg-transparent outline-none text-sm font-normal leading-[18px] text-[#747474] placeholder:text-[#747474]";
const TOGGLE_BUTTON_BASE =
  "px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-200 cursor-pointer pointer-events-auto";

// Reusable Components
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  children,
  className,
}) => (
  <div className={cn(FORM_FIELD_CONTAINER, className)}>
    <label className={FORM_LABEL}>{label}</label>
    {children}
  </div>
);

interface ToggleButtonProps {
  isActive: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

export const ToggleButton: React.FC<ToggleButtonProps> = ({
  isActive,
  onClick,
  children,
}) => (
  <button
    type="button"
    onClick={onClick}
    className={cn(
      TOGGLE_BUTTON_BASE,
      isActive ? "bg-[#1E4FFF] text-white" : "text-gray-700 hover:text-blue-600"
    )}
  >
    {children}
  </button>
);

// Export utility functions and constants
export { cn, FORM_INPUT, FORM_LABEL };
