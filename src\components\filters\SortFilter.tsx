import React from "react";
import RadioGroup from "@/shared/components/RadioGroup";
import { useTranslation } from "@/shared/hooks/useTranslation";
import type { SortOption, RadioOption } from "@/shared/types/bus";

interface SortFilterProps {
  value: SortOption;
  onChange: (sort: SortOption) => void;
}

const SortFilter: React.FC<SortFilterProps> = ({ value, onChange }) => {
  const { t } = useTranslation();

  const sortOptions: RadioOption[] = [
    { value: "default", label: t("filters.sort.default") },
    { value: "earliest", label: t("filters.sort.earliest") },
    { value: "latest", label: t("filters.sort.latest") },
    { value: "cheapest", label: t("filters.sort.cheapest") },
    { value: "most_expensive", label: t("filters.sort.most_expensive") },
  ];

  return (
    <RadioGroup
      name="sort"
      options={sortOptions}
      value={value}
      onChange={(newValue) => onChange(newValue as SortOption)}
    />
  );
};

export default SortFilter;
