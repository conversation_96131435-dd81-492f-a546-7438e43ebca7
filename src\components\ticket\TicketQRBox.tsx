import React from "react";
import {
  ClipboardIcon,
  ArrowDownTrayIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
  QrCodeIcon,
} from "@heroicons/react/24/outline";

interface TicketQRBoxProps {
  qrImage: string;
  bank: string;
  accountNumber: string;
  accountName: string;
  amount: number;
  content: string;
  expiryText: string;
  onDownloadQR: () => void;
}

const steps = [
  {
    icon: <DevicePhoneMobileIcon className="w-8 h-8 text-[#2D5BFF]" />,
    label: "Mở ứng dụng ngân hàng hoặc Ví điện tử",
  },
  {
    icon: <QrCodeIcon className="w-8 h-8 text-[#2D5BFF]" />,
    label: "Dùng tính năng Mã QR quét hình bên",
  },
  {
    icon: <CheckCircleIcon className="w-9 h-9 text-[#2D5BFF]" />,
    label: "Chờ xác nhận",
  },
];

const InfoRow: React.FC<{
  label: string;
  value: string;
  isCopyable?: boolean;
  onCopy?: () => void;
  valueClassName?: string;
  helperText?: string;
}> = ({ label, value, isCopyable, onCopy, valueClassName, helperText }) => (
  <div className="flex justify-between items-start py-2 border-b border-[#EDEDED] last:border-b-0">
    <span className="text-[14px] text-[#181818] font-normal pt-1">{label}</span>
    <span className="flex flex-col items-end min-w-[120px]">
      <span
        className={`flex items-center gap-1 ${isCopyable ? "cursor-pointer" : ""}`}
        onClick={onCopy}
      >
        <span
          className={`text-[14px] font-bold ${valueClassName || "text-[#181818]"}`}
        >
          {value}
        </span>
        {isCopyable && (
          <ClipboardIcon className="w-4 h-4 text-[#2D5BFF] ml-1" />
        )}
      </span>
      {isCopyable && helperText && (
        <span className="text-xs text-[#5C5C5C] mt-0.5 pr-0.5">
          {helperText}
        </span>
      )}
    </span>
  </div>
);

export const TicketQRBox: React.FC<TicketQRBoxProps> = ({
  qrImage,
  bank,
  accountNumber,
  accountName,
  amount,
  content,
  expiryText,
  onDownloadQR,
}) => {
  // Copy handlers
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="bg-white rounded-2xl border border-[#EDEDED] p-6 w-full max-w-5xl mx-auto">
      {/* Main content: 2 columns */}
      <div className="flex">
        {/* Column 1: Expiry warning + Steps & QR */}
        <div className="flex-1 flex flex-col gap-3 px-4">
          {/* Row 1: Expiry warning */}
          <div className="w-full">
            <div className="rounded-lg bg-[#FEF5F5] text-[14px] text-[#FF3030] py-2.5 text-center">
              {expiryText}
            </div>
          </div>

          {/* Row 2: Steps & QR side by side */}
          <div className="flex w-full gap-4">
            {/* Steps */}
            <div className="bg-[#EDF8FF] rounded-2xl flex flex-col items-center justify-center py-5 px-4 min-w-[166px] gap-8">
              {steps.map((step, idx) => (
                <div key={idx} className="flex flex-col items-center gap-2">
                  {step.icon}
                  <span className="text-xs font-bold text-[#181818] text-center leading-tight">
                    {step.label}
                  </span>
                </div>
              ))}
            </div>

            {/* QR code + Download */}
            <div className="flex flex-1 flex-col gap-4">
              <div className="border-4 border-[#2D5BFF] rounded-2xl p-3 bg-white flex items-center justify-center">
                <img
                  src={qrImage}
                  alt="QR Code"
                  className="min-w-[125px] min-h-[125px] max-w-[225px] max-h-[225px] object-contain rounded-lg"
                />
              </div>
              <button
                className="w-full h-[44px] flex items-center justify-center gap-2 bg-[#ECF0FF] text-[#2D5BFF] rounded-lg font-extrabold text-base hover:bg-[#dbeafe] transition-colors border border-[#2D5BFF]"
                onClick={onDownloadQR}
              >
                <ArrowDownTrayIcon className="w-6 h-6" />
                Lưu mã QR về máy
              </button>
            </div>
          </div>
        </div>

        {/* Column 2: Bank Info */}
        <div className="w-full min-w-[250px] rounded-2xl border border-[#EDEDED] p-4 bg-white flex flex-col gap-2">
          <div className="text-[14px] font-bold text-[#181818] mb-2">
            Không thể quét mã?
          </div>
          <InfoRow label="Ngân hàng" value={bank} />
          <InfoRow
            label="Số tài khoản"
            value={accountNumber}
            isCopyable
            onCopy={() => handleCopy(accountNumber)}
            valueClassName="text-[#2D5BFF]"
            helperText="Nhấn vào để sao chép"
          />
          <InfoRow label="Chủ tài khoản" value={accountName} />
          <InfoRow
            label="Tổng tiền"
            value={amount.toLocaleString("vi-VN") + "₫"}
            isCopyable
            onCopy={() => handleCopy(amount.toLocaleString("vi-VN"))}
            valueClassName="text-[#2D5BFF]"
            helperText="Nhấn vào để sao chép"
          />
          <InfoRow
            label="Nội dung"
            value={content}
            isCopyable
            onCopy={() => handleCopy(content)}
            valueClassName="text-[#2D5BFF]"
            helperText="Nhấn vào để sao chép"
          />
        </div>
      </div>
    </div>
  );
};
