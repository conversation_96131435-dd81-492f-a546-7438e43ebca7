import { useQuery } from "@tanstack/react-query";
import { getPopularRoutes } from "@/api/strapiApi";
import type {
  StrapiPopularRoutesResponse,
  StrapiApiError,
} from "@/shared/types/strapi";

// Query keys for popular routes
export const popularRoutesKeys = {
  all: ["popularRoutes"] as const,
  list: () => [...popularRoutesKeys.all, "list"] as const,
};

/**
 * Hook to fetch popular routes from Strapi
 */
export const usePopularRoutes = () => {
  return useQuery<StrapiPopularRoutesResponse, StrapiApiError>({
    queryKey: popularRoutesKeys.list(),
    queryFn: () => getPopularRoutes(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (previously cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
