import { useMutation } from "@tanstack/react-query";
import { createOrder } from "@/api/merchants/orders.api";
import type {
  CreateOrderRequest,
  CreateOrderResponse,
} from "@/api/merchants/orders.types";
import { useQueryClient } from "@tanstack/react-query";

export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation<CreateOrderResponse, Error, CreateOrderRequest>({
    mutationFn: createOrder,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orders"] });
    },
    onError: (error) => {
      console.error("Failed to create order:", error);
    },
  });
};
