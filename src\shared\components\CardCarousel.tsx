import React, { memo, useCallback, useEffect, useState } from "react";
import { Skeleton } from "@/shared/components/skeleton";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/shared/components/carousel";
import { Container } from "./Container";

export interface CardItem {
  id: string;
  image: string;
  title: string;
  timeAgo: string;
}

interface ItemsPerView {
  mobile: number;
  tablet: number;
  desktop: number;
}

interface CardCarouselProps {
  title: string | React.ReactNode;
  items: CardItem[];
  itemsPerView?: ItemsPerView;
  renderCard: (item: CardItem) => React.ReactNode;
  className?: string;
  isLoading?: boolean;
}

// Skeleton Card Component - Simplified to match BaseCard structure
const SkeletonCard: React.FC = memo(() => (
  <div className="w-full lg:max-w-[300px] mx-auto relative min-h-[210px] lg:min-h-[280px] flex flex-col">
    {/* Image Skeleton - Responsive height */}
    <Skeleton className="w-full h-[126px] lg:h-[168px] rounded-t-[24px]" />

    {/* Content Skeleton - Responsive height */}
    <div className="bg-white border-1 border-[#EDEDED] rounded-b-[24px] p-3 lg:p-4 flex-1 flex flex-col justify-between min-h-[84px] lg:min-h-[112px]">
      <div className="flex flex-col gap-2 mb-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    </div>
  </div>
));

SkeletonCard.displayName = "SkeletonCard";

export const CardCarousel: React.FC<CardCarouselProps> = memo(
  ({
    title,
    items,
    itemsPerView = { mobile: 1, tablet: 2, desktop: 4 },
    renderCard,
    className = "",
    isLoading = false,
  }) => {
    const [api, setApi] = useState<CarouselApi>();
    const [canScrollPrev, setCanScrollPrev] = useState(false);
    const [canScrollNext, setCanScrollNext] = useState(false);

    // Setup carousel API
    useEffect(() => {
      if (!api) return;

      const onSelect = () => {
        setCanScrollPrev(api.canScrollPrev());
        setCanScrollNext(api.canScrollNext());
      };

      onSelect();
      api.on("select", onSelect);
      api.on("reInit", onSelect);

      return () => {
        api.off("select", onSelect);
        api.off("reInit", onSelect);
      };
    }, [api]);

    // Display items - responsive skeleton count based on screen size
    const displayItems = React.useMemo(() => {
      if (isLoading) {
        // Show more skeleton items to ensure smooth carousel experience
        // but let the carousel basis handle the actual visible count
        const skeletonCount = Math.max(
          itemsPerView.mobile + 1,
          itemsPerView.desktop
        );
        return Array.from({ length: skeletonCount }, (_, index) => ({
          id: `skeleton-${index}`,
          image: "",
          title: "",
          timeAgo: "",
        }));
      }
      return items;
    }, [items, isLoading, itemsPerView]);

    const scrollToPrevious = useCallback(() => {
      api?.scrollPrev();
    }, [api]);

    const scrollToNext = useCallback(() => {
      api?.scrollNext();
    }, [api]);

    return (
      <section className={`w-full ${className}`}>
        <div className="">
          {/* Header */}
          <Container>
            <div className="flex sm:justify-between mb-5">
              <div className="flex flex-col gap-3">
                {isLoading ? (
                  <Skeleton className="h-12 w-48" />
                ) : typeof title === "string" ? (
                  <h2 className="text-[#181818] font-mulish font-extrabold text-[20px] sm:text-[20px] md:text-[24px] lg:text-[32px] leading-[1.2]">
                    {title}
                  </h2>
                ) : (
                  title
                )}
              </div>

              {/* Custom Navigation - Hidden on mobile */}
              <div className="hidden md:flex gap-2">
                <button
                  onClick={scrollToPrevious}
                  disabled={!canScrollPrev || isLoading}
                  className={`w-12 h-12 rounded-lg border flex items-center justify-center transition-all duration-200 ${
                    !canScrollPrev || isLoading
                      ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                      : "bg-white text-[#181818] hover:bg-[#2D5BFF] hover:text-white border-[#EFEFEF] hover:border-[#2D5BFF]"
                  }`}
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={scrollToNext}
                  disabled={!canScrollNext || isLoading}
                  className={`w-12 h-12 rounded-lg border flex items-center justify-center transition-all duration-200 ${
                    !canScrollNext || isLoading
                      ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                      : "bg-[#2D5BFF] text-white hover:bg-[#1e40af] border-[#2D5BFF]"
                  }`}
                >
                  <ChevronRightIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </Container>

          {/* Carousel */}
          <Container className="py-2 px-0 sm:px-20 lg:px-24 max-w-full lg:max-w-7xl">
            <Carousel
              setApi={setApi}
              className="w-full"
              opts={{
                align: "start",
                slidesToScroll: 1,
                dragFree: true,
              }}
            >
              <CarouselContent className="pl-4 sm:pl-0 -ml-2">
                {displayItems.map((item) => {
                  return (
                    <CarouselItem
                      key={item.id}
                      className="basis-[75%] sm:basis-1/2 md:basis-1/3 lg:basis-1/4 flex justify-center pl-2"
                    >
                      <div className="w-full flex justify-center">
                        {isLoading ? <SkeletonCard /> : renderCard(item)}
                      </div>
                    </CarouselItem>
                  );
                })}
              </CarouselContent>
            </Carousel>
          </Container>
        </div>
      </section>
    );
  }
);

CardCarousel.displayName = "CardCarousel";
