import { useTranslation as useI18nTranslation } from "react-i18next";

/**
 * Custom hook for translations with type safety
 * @param ns - Namespace (defaults to 'common')
 * @returns Translation function and i18n instance
 */
export const useTranslation = (ns: string = "common") => {
  const { t, i18n } = useI18nTranslation(ns);

  return { t, i18n };
};

/**
 * Hook for language switching functionality
 * @returns Object with current language, available languages, and change function
 */
export const useLanguage = () => {
  const { i18n } = useI18nTranslation();

  const changeLanguage = async (lng: string) => {
    i18n.changeLanguage(lng);
  };

  // Reset to Vietnamese (useful for testing or forcing default)
  const resetToVietnamese = () => {
    localStorage.setItem("i18nextLng", "vi");
    i18n.changeLanguage("vi");
  };

  const availableLanguages = [
    // { code: "en", name: "English" },
    { code: "vi", name: "Tiếng Việt" },
  ];

  return {
    currentLanguage: i18n.language,
    availableLanguages,
    changeLanguage,
    resetToVietnamese,
    isLoading: !i18n.isInitialized,
  };
};
