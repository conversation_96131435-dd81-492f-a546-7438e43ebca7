import React, { useState, useCallback } from "react";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { Skeleton } from "@/shared/components/skeleton";

export const HeroSection: React.FC = React.memo(() => {
  const { t } = useTranslation();
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  return (
    <>
      {/* Background Image */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {!imageLoaded && <Skeleton className="w-full h-full" />}

        {/* Mobile Background */}
        <div
          className={`
            md:hidden w-full h-[173px]
            transition-opacity duration-300 
            ${imageLoaded ? "opacity-100" : "opacity-0"}
          `}
          style={{
            backgroundImage: "url('/assets/hero.svg')",
            backgroundPosition: "center -100px",
            backgroundSize: "600px auto",
            backgroundRepeat: "no-repeat",
          }}
        />

        {/* Desktop Background */}
        <div
          className={`
            hidden md:block w-full h-full 
            bg-cover bg-center
            transition-opacity duration-300 
            ${imageLoaded ? "opacity-100" : "opacity-0"}
          `}
          style={{ backgroundImage: "url('/assets/hero.svg')" }}
        />

        {/* Preload image */}
        <img
          src="/assets/hero.svg"
          alt="Hero background"
          className="hidden"
          onLoad={handleImageLoad}
        />
      </div>

      {/* Hero Text */}
      <div className="absolute right-20 top-0 z-10 max-w-6xl mx-auto px-6 pt-20">
        <div className="mb-20 hidden lg:block">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
            <div className="text-white text-[48px] mb-2">
              {t("home.hero.title1")}
            </div>
            <div className="text-white text-[48px] mb-2">
              {t("home.hero.title2")}
            </div>
            <div className="text-[#1E4FFF] text-[40px]">
              {t("home.hero.subtitle")}
            </div>
          </h1>
        </div>
      </div>
    </>
  );
});

HeroSection.displayName = "HeroSection";
