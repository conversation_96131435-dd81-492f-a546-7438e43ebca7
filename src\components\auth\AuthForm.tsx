import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { clearResetToken } from "@/shared/utils/tokenUtils";
import { Route } from "@/routes/auth";
import { AuthHeader } from "./AuthHeader";
import { AuthTabs } from "./AuthTabs";
import { SigninForm } from "./SigninForm";
import { SignupFlow } from "./SignupFlow";
import { ForgotPasswordFlow } from "./ForgotPasswordFlow";

interface AuthFormProps {
  onSigninSuccess?: () => void;
  onSignupSuccess?: () => void;
  defaultTab?: "signin" | "signup";
  className?: string;
}

type AuthMode = "main" | "signup" | "forgot-password";

export const AuthForm: React.FC<AuthFormProps> = ({
  onSigninSuccess,
  onSignupSuccess,
  defaultTab = "signin",
  className,
}) => {
  const [activeTab, setActiveTab] = useState<"signin" | "signup">(defaultTab);
  const [currentMode, setCurrentMode] = useState<AuthMode>("main");
  const navigate = Route.useNavigate();

  // Sync URL -> state
  useEffect(() => {
    if (defaultTab !== activeTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab]);

  // Sync state -> URL
  useEffect(() => {
    navigate({
      search: (prev) => ({ ...prev, tab: activeTab }),
      replace: true,
    });
  }, [activeTab, navigate]);

  const handleTabSwitch = (tab: "signin" | "signup") => {
    setActiveTab(tab);
    setCurrentMode("main");
    // Clean up reset token when switching tabs
    clearResetToken();
  };

  const handleForgotPassword = () => {
    setCurrentMode("forgot-password");
  };

  const handleBackToMain = () => {
    setCurrentMode("main");
    clearResetToken();
  };

  // Render forgot password flow
  if (currentMode === "forgot-password") {
    return (
      <div className={cn("w-full max-w-md mx-auto", className)}>
        <ForgotPasswordFlow onBack={handleBackToMain} />
      </div>
    );
  }

  // Render signup flow
  if (currentMode === "signup") {
    return (
      <div className={cn("w-full max-w-md mx-auto", className)}>
        <SignupFlow
          onSignupSuccess={onSignupSuccess}
          onBack={handleBackToMain}
        />
      </div>
    );
  }

  // Render main form
  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      {/* Header */}
      <AuthHeader activeTab={activeTab} />

      {/* Tab Navigation */}
      <AuthTabs activeTab={activeTab} onTabChange={handleTabSwitch} />

      {/* Form Content */}
      <div className="relative flex flex-col">
        {activeTab === "signin" ? (
          <SigninForm
            onSigninSuccess={onSigninSuccess}
            onForgotPassword={handleForgotPassword}
          />
        ) : (
          <SignupFlow
            onSignupSuccess={onSignupSuccess}
            onBack={handleBackToMain}
          />
        )}
      </div>
    </div>
  );
};
