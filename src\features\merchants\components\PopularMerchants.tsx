import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/shared/components/carousel";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import PopularMerchantCard from "@/features/merchants/components/PopularMerchantCard";
import type { Merchant } from "@/api/merchants/merchants.types";
import { useState, useEffect, useCallback } from "react";
import type { CarouselApi } from "@/shared/components/carousel";

export default function PopularMerchants({
  merchants,
  onSelectMerchant,
  selectedMerchantId,
}: {
  merchants: Merchant[];
  onSelectMerchant: (merchant: Merchant) => void;
  selectedMerchantId?: string | null;
}) {
  const [api, setApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  // Setup carousel API
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    onSelect();
    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api.off("select", onSelect);
      api.off("reInit", onSelect);
    };
  }, [api]);

  const scrollToPrevious = useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollToNext = useCallback(() => {
    api?.scrollNext();
  }, [api]);

  return (
    <div>
      {/* Header with Navigation */}
      <div className="flex justify-between items-center mb-5">
        <div className="flex flex-col gap-3">
          <h2 className="text-xl font-extrabold text-[#181818]">
            Danh sách nhà hàng
          </h2>
        </div>

        <div className="flex gap-2 opacity-0">
          <button
            onClick={scrollToPrevious}
            disabled={!canScrollPrev}
            className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 ${
              !canScrollPrev
                ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                : "bg-white text-[#181818] hover:bg-[#2D5BFF] hover:text-white border-[#EFEFEF] hover:border-[#2D5BFF]"
            }`}
          >
            <ChevronLeftIcon className="w-4 h-4" />
          </button>
          <button
            onClick={scrollToNext}
            disabled={!canScrollNext}
            className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 shadow-[0px_0px_30px_0px_rgba(132,132,132,0.25)] ${
              !canScrollNext
                ? "opacity-50 cursor-not-allowed bg-[#EFEFEF] text-[#8A8A8A] border-[#EFEFEF]"
                : "bg-[#2D5BFF] text-white hover:bg-[#1e40af] border-[#2D5BFF]"
            }`}
          >
            <ChevronRightIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Restaurant Cards */}
      <div className="max-w-full">
        <Carousel
          setApi={setApi}
          className="w-full"
          opts={{
            align: "start",
            slidesToScroll: 1,
            dragFree: true,
          }}
        >
          <CarouselContent className="sm:pl-0">
            {merchants?.map((merchant: Merchant) => (
              <CarouselItem
                key={merchant.id}
                className="basis-[150px] flex justify-center"
              >
                <PopularMerchantCard
                  merchant={merchant}
                  onClick={() => onSelectMerchant(merchant)}
                  isSelected={selectedMerchantId === merchant.id}
                />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
}
