import { useState, useCallback } from "react";
import { PasswordValidation } from "@/schemas/passwordSchema";

interface UsePasswordInputOptions {
  maxLength?: number;
  initialShowPassword?: boolean;
}

interface UsePasswordInputReturn {
  show: boolean;
  toggle: () => void;
  handlePasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxLength: number;
}

/**
 * Custom hook for password input handling with length validation
 * @param options Configuration options for the password input
 * @returns Object with password input handlers and state
 */
export const usePasswordInput = (
  options: UsePasswordInputOptions = {}
): UsePasswordInputReturn => {
  const {
    maxLength = PasswordValidation.MAX_LENGTH,
    initialShowPassword = false,
  } = options;
  const [showPassword, setShowPassword] = useState(initialShowPassword);

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  const handlePasswordChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value.slice(0, maxLength);
      e.target.value = val;
    },
    [maxLength]
  );

  return {
    show: showPassword,
    toggle: togglePasswordVisibility,
    handlePasswordChange,
    maxLength,
  };
};
