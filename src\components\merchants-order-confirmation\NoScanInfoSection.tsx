import React, { useState } from "react";
import { ClipboardIcon } from "@heroicons/react/24/outline";

interface NoScanInfoSectionProps {
  bank: string;
  accountNumber: string;
  accountName: string;
  amount: string | number;
  content: string;
  onCopyAccount?: () => void;
  onCopyAmount?: () => void;
  onCopyContent?: () => void;
}

const InfoRow: React.FC<{
  label: string;
  value: string;
  isCopyable?: boolean;
  onCopy?: () => void;
  valueClassName?: string;
  helperText?: string;
}> = ({ label, value, isCopyable, onCopy, valueClassName, helperText }) => (
  <div className="flex justify-between items-start border-b border-[#EDEDED] pb-2 last:border-b-0 last:pb-0">
    <span className="text-[14px] text-[#181818] font-normal pt-1">{label}</span>
    <span className="flex flex-col items-end min-w-[120px]">
      <span
        className={`flex items-center gap-1 ${isCopyable ? "cursor-pointer" : ""}`}
        onClick={onCopy}
      >
        <span
          className={`text-[14px] font-bold ${valueClassName || "text-[#181818]"}`}
        >
          {value}
        </span>
        {isCopyable && <ClipboardIcon className="w-4 h-4 text-[#2D5BFF]" />}
      </span>
      {isCopyable && helperText && (
        <span className="text-xs text-[#5C5C5C] mt-0.5 pr-0.5">
          {helperText}
        </span>
      )}
    </span>
  </div>
);

const Toast: React.FC<{ show: boolean; message: string }> = ({
  show,
  message,
}) => (
  <div
    className={`fixed bottom-6 right-6 z-[9999] transition-all duration-300 ${show ? "opacity-100" : "opacity-0 pointer-events-none"}`}
  >
    <div className="bg-[#181818] text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium">
      {message}
    </div>
  </div>
);

const NoScanInfoSection: React.FC<NoScanInfoSectionProps> = ({
  bank,
  accountNumber,
  accountName,
  amount,
  content,
  onCopyAccount,
  onCopyAmount,
  onCopyContent,
}) => {
  const [toast, setToast] = useState<{ show: boolean; message: string }>({
    show: false,
    message: "",
  });

  const handleCopy = (cb?: () => void) => {
    if (cb) cb();
    setToast({ show: true, message: "Đã sao chép!" });
    setTimeout(() => setToast({ show: false, message: "" }), 1200);
  };

  return (
    <>
      <div className="bg-white border border-[#EDEDED] rounded-xl px-4 py-3 flex flex-col gap-2">
        <div className="text-[14px] font-bold text-[#181818] mb-2">
          Không thể quét mã?
        </div>
        <InfoRow label="Ngân hàng" value={bank} />
        <InfoRow
          label="Số tài khoản"
          value={accountNumber}
          isCopyable
          onCopy={() => handleCopy(onCopyAccount)}
          valueClassName="text-[#2D5BFF]"
          helperText="Nhấn vào để sao chép"
        />
        <InfoRow label="Chủ tài khoản" value={accountName} />
        <InfoRow
          label="Tổng tiền"
          value={String(amount)}
          isCopyable
          onCopy={() => handleCopy(onCopyAmount)}
          valueClassName="text-[#2D5BFF]"
          helperText="Nhấn vào để sao chép"
        />
        <InfoRow
          label="Nội dung"
          value={content}
          isCopyable
          onCopy={() => handleCopy(onCopyContent)}
          valueClassName="text-[#2D5BFF]"
          helperText="Nhấn vào để sao chép"
        />
      </div>
      <Toast show={toast.show} message={toast.message} />
    </>
  );
};

export default NoScanInfoSection;
