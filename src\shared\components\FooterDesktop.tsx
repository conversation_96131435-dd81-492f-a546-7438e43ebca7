import { memo } from "react";
import logo from "../../logo.png";
import { Container } from "@/shared/components";
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { getCompanyInfoSections } from "@/api/strapiApi/companyInfoSections";

// Social icons data
const SOCIAL_ICONS = [
  {
    key: "youtube",
    icon: <img src="assets/youtube.svg" alt="YouTube" className="w-6 h-6" />,
  },
  {
    key: "facebook",
    icon: <img src="assets/facebook.svg" alt="Facebook" className="w-6 h-6" />,
  },
  {
    key: "tiktok",
    icon: <img src="assets/tiktok.svg" alt="TikTok" className="w-6 h-6" />,
  },
];

// FooterNavSection component
interface FooterNavSectionProps {
  title: string;
  links: { label: string; href: string }[];
  className?: string;
  gap?: string;
  mb?: string;
}
const FooterNavSection: React.FC<FooterNavSectionProps> = memo(
  ({ title, links }) => (
    <div className="w-fit">
      <h3
        className={`text-base sm:text-lg font-bold text-[#181818] mb-6 leading-5 sm:leading-6`}
      >
        {title}
      </h3>
      <ul className={`flex flex-col gap-3`}>
        {links.map((link) => (
          <li key={link.label}>
            <a
              href={link.href}
              className="text-sm font-normal text-[#747474] hover:text-[#181818] leading-6 block"
            >
              {link.label}
            </a>
          </li>
        ))}
      </ul>
    </div>
  )
);
FooterNavSection.displayName = "FooterNavSection";

// FooterSocialIcon component
interface FooterSocialIconProps {
  icon: React.ReactNode;
  keyName: string;
}
const FooterSocialIcon: React.FC<FooterSocialIconProps> = memo(
  ({ icon, keyName }) => (
    <div className="w-6 h-6 bg-white rounded" key={keyName}>
      {icon}
    </div>
  )
);
FooterSocialIcon.displayName = "FooterSocialIcon";

function useCompanyInfoSectionsQuery() {
  return useQuery({
    queryKey: ["company-info-sections"],
    queryFn: getCompanyInfoSections,
  });
}

export const FooterDesktop: React.FC = memo(() => {
  const { data, isLoading, isError } = useCompanyInfoSectionsQuery();

  return (
    <Container className="hidden lg:block bg-[#F8F8F8]">
      <footer className="border-t border-[#EDEDED] py-8">
        <div className="">
          {/* Main Footer Content */}
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start mb-8 gap-8 lg:gap-0">
            {/* Company Info - Always first on mobile, left on desktop */}
            <div className="max-w-[291px] order-1">
              {/* Logo */}
              <div className="w-[196px] h-[45px] mb-4">
                <img className="h-full w-auto" src={logo} alt="GTech" />
              </div>
              {/* Company Description */}
              <p className="text-sm font-bold text-[#5C5C5C] leading-[18px] mb-4">
                CÔNG TY CỔ PHẦN GIẢI PHÁP CÔNG NGHỆ PHẦN MỀM GTECH
              </p>
              {/* Certification Logo */}
              <div className="w-[120px] h-[45px]">
                <img
                  className="w-full h-full object-contain"
                  src="/assets/certificate-logo.png"
                  alt="Certification Logo"
                />
              </div>
            </div>

            {/* Navigation Links - Stacked on mobile, horizontal on desktop */}
            <div className="flex w-1/2 gap-16 order-2">
              {isLoading && (
                <div className="text-sm text-gray-400">Đang tải...</div>
              )}
              {isError && (
                <div className="text-sm text-red-500">Lỗi tải dữ liệu</div>
              )}
              {!isLoading && !isError && data && data.length === 0 && (
                <div className="text-sm text-gray-400">Không có dữ liệu</div>
              )}
              {!isLoading &&
                !isError &&
                data &&
                data.map((section) => (
                  <FooterNavSection
                    key={section.id}
                    title={section.title}
                    links={section.links}
                  />
                ))}
            </div>

            {/* Social Icons - Below navigation on mobile, right on desktop */}
            <div className="flex gap-5 order-3 lg:order-3">
              {SOCIAL_ICONS.map((icon) => (
                <FooterSocialIcon
                  key={icon.key}
                  keyName={icon.key}
                  icon={icon.icon}
                />
              ))}
            </div>
          </div>

          {/* Separator Line */}
          <hr className="border-[#EDEDED] mb-6" />

          {/* Copyright Section - Vertical on mobile, horizontal on desktop */}
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
            <p className="text-sm text-[#5C5C5C] font-normal">
              Bản quyền © 2025 thuộc về Gtech
            </p>
            <p className="text-sm text-[#747474] font-normal">
              Địa chỉ đăng ký kinh doanh: Tầng 4, Số 4 Ngõ 91 Phố Trần Duy Hưng,
              Phường Trung Hoà, Quận Cầu Giấy, Thành phố Hà Nội, Việt Nam
            </p>
          </div>
        </div>
      </footer>
    </Container>
  );
});

export default FooterDesktop;
