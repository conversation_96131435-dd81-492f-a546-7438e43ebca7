import { useEffect, useState } from "react";

export const DotLoader = () => {
  const [active, setActive] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActive((prev) => (prev + 1) % 3);
    }, 300);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex gap-1 items-center h-6">
      {[0, 1, 2].map((i) => (
        <span
          key={i}
          className={`inline-block w-2 h-2 rounded-full bg-white transition-all duration-200 ${
            active === i ? "scale-125" : "scale-100 opacity-50"
          }`}
        />
      ))}
    </div>
  );
};
