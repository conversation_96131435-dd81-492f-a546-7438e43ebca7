import React, { useState } from "react";
import { Button } from "@/shared/components/button";
import { Input } from "@/shared/components";
import { cn } from "@/lib/utils";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { validateVietnamesePhoneNumber } from "@/shared/utils/phoneNumberUtils";

interface ForgotPasswordFormProps {
  onSubmit?: (phoneNumber: string) => void;
  onBack?: () => void;
  className?: string;
  isLoading?: boolean;
  error?: string;
  onError?: (error: string) => void;
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onSubmit,
  onBack,
  className,
  isLoading = false,
  error: externalError,
  onError,
}) => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [localError, setLocalError] = useState("");

  // Use external error if provided, otherwise use local error
  const error = externalError || localError;

  const handleInputChange = (value: string) => {
    setPhoneNumber(value);
    if (error) {
      setLocalError("");
      onError?.("");
    }
  };

  const validateForm = () => {
    const phoneValidation = validateVietnamesePhoneNumber(phoneNumber);
    if (!phoneValidation.isValid) {
      setLocalError(phoneValidation.errorMessage!);
      onError?.(phoneValidation.errorMessage!);
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    // Normalize phone number before submitting
    const phoneValidation = validateVietnamesePhoneNumber(phoneNumber);
    const normalizedPhone = phoneValidation.normalizedNumber || phoneNumber;

    onSubmit?.(normalizedPhone);
  };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="space-y-12">
        {/* Header */}
        <div className="space-y-4">
          {/* Back Button */}
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="flex items-center gap-4 text-[#5C5C5C] hover:text-[#181818] transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
              <span className="text-[18px] font-normal">
                Quay lại Đăng nhập
              </span>
            </button>
          </div>

          {/* Title and Description */}
          <div className="text-center space-y-4">
            <h2 className="text-[32px] font-extrabold text-[#181818] leading-tight">
              Quên mật khẩu
            </h2>
            <p className="text-[18px] text-[#5C5C5C] leading-relaxed">
              Nhập số điện thoại bạn đã đăng ký
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="space-y-4">
          {/* Phone Number Field */}
          <Input
            type="tel"
            name="phoneNumber"
            placeholder="Số điện thoại"
            value={phoneNumber}
            onChange={(e) => handleInputChange(e.target.value)}
            error={error}
          />

          {/* Submit Button */}
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-full mt-10 bg-[#2D5BFF] hover:bg-[#2D5BFF]/90 text-white py-6 rounded-lg font-extrabold text-[18px] tracking-wide"
          >
            {isLoading ? "Đang xử lý..." : "Tiếp tục"}
          </Button>
        </div>
      </div>
    </div>
  );
};
