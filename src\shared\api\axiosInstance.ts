// libs/axios.ts
import axios from "axios";
const axiosInstance = axios.create({
  baseURL:
    import.meta.env.VITE_API_BASE_URL || "https://dev.api.gtech-ecom.gone.vn/",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to automatically add token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (res) => res,
  (err) => {
    // Handle lỗi chung
    console.error("API Error:", err.response?.data || err.message);
    return Promise.reject(err);
  }
);

export const axiosInstanceStrapi = axios.create({
  baseURL:
    import.meta.env.VITE_STRAPI_BASE_URL ||
    "https://strapi.gtech-ecom.gone.vn/",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to automatically add token for Strapi instance
axiosInstanceStrapi.interceptors.request.use(
  (config) => {
    // const token = localStorage.getItem("access_token");
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstanceStrapi.interceptors.response.use(
  (res) => res,
  (err) => {
    // Handle lỗi chung
    console.error("API Error:", err.response?.data || err.message);
    return Promise.reject(err);
  }
);

export default axiosInstance;
