import { create } from "zustand";

interface Profile {
  id: string;
  full_name: string;
  phone_number: string;
  email: string;
}

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  profile: Profile | null;
  setProfile: (profile: Profile | null) => void;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
}

export const authStore = create<AuthState>((set) => ({
  isAuthenticated: false,
  isLoading: true,
  profile: null,
  setProfile: (profile) => set({ profile }),
  setIsAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
  setIsLoading: (isLoading) => set({ isLoading }),
}));
