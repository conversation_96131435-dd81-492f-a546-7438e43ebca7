import { useMemo, useCallback } from "react";
import type { Restaurant } from "@/features/merchants/types";

export const useCartCalculation = (restaurant: Restaurant | null) => {
  const { totalItems, totalPrice } = useMemo(() => {
    if (!restaurant) return { totalItems: 0, totalPrice: 0 };

    let items = 0;
    let price = 0;

    // restaurant.categories.forEach((category) => {
    //   category.food_items.forEach((item) => {
    //     items += item.quantity;
    //     price += item.price * item.quantity;
    //   });
    // });

    return { totalItems: items, totalPrice: price };
  }, [restaurant]);

  const handleQuantityChange = useCallback(
    (itemId: string, newQuantity: number) => {
      if (!restaurant) return null;
      const updatedCategories = restaurant.categories.map((category) => ({
        ...category,
        food_items: category.food_items.map((item) =>
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        ),
      }));
      const updatedRestaurant = {
        ...restaurant,
        categories: updatedCategories,
      };
      return updatedRestaurant;
    },
    [restaurant]
  );

  return {
    totalItems,
    totalPrice,
    handleQuantityChange,
  };
};
