import React, { createContext, useContext, useState } from "react";
import type { ReactNode } from "react";

export interface SelectedTrip {
  id: string;
  scheduleId: string;
  name: string;
  busNumber: string;
  busDescription: string;
  departureTime: string;
  arrivalTime: string;
  price: number;
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  duration: string;
  dropoffId?: string;
  route: string;
  companyName: string;
  note: string;
  image?: string;
}

interface SelectedTripsState {
  outboundTrip: SelectedTrip | null;
  returnTrip: SelectedTrip | null;
  isRoundTrip: boolean;

  // Actions
  setOutboundTrip: (trip: SelectedTrip | null) => void;
  setReturnTrip: (trip: SelectedTrip | null) => void;
  setIsRoundTrip: (isRoundTrip: boolean) => void;
  clearTrips: () => void;
  getTotalPrice: () => number;
  getSelectedTripsCount: () => number;
  isReadyForCheckout: () => boolean;
}

const SelectedTripsContext = createContext<SelectedTripsState | undefined>(
  undefined
);

interface SelectedTripsProviderProps {
  children: ReactNode;
}

export const SelectedTripsProvider = ({
  children,
}: SelectedTripsProviderProps) => {
  const [outboundTrip, setOutboundTrip] = useState<SelectedTrip | null>(null);
  const [returnTrip, setReturnTrip] = useState<SelectedTrip | null>(null);
  const [isRoundTrip, setIsRoundTrip] = useState(false);

  const clearTrips = () => {
    setOutboundTrip(null);
    setReturnTrip(null);
    setIsRoundTrip(false);
  };

  const getTotalPrice = () => {
    let total = 0;
    if (outboundTrip) total += outboundTrip.price;
    if (returnTrip) total += returnTrip.price;
    return total;
  };

  const getSelectedTripsCount = () => {
    let count = 0;
    if (outboundTrip) count++;
    if (returnTrip) count++;
    return count;
  };

  const isReadyForCheckout = () => {
    if (!isRoundTrip) {
      return !!outboundTrip;
    } else {
      return !!outboundTrip && !!returnTrip;
    }
  };

  const value: SelectedTripsState = {
    outboundTrip,
    returnTrip,
    isRoundTrip,
    setOutboundTrip,
    setReturnTrip,
    setIsRoundTrip,
    clearTrips,
    getTotalPrice,
    getSelectedTripsCount,
    isReadyForCheckout,
  };

  return React.createElement(
    SelectedTripsContext.Provider,
    { value },
    children
  );
};

export const useSelectedTripsStore = () => {
  const context = useContext(SelectedTripsContext);
  if (context === undefined) {
    throw new Error(
      "useSelectedTripsStore must be used within a SelectedTripsProvider"
    );
  }
  return context;
};
