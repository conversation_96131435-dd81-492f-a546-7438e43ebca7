import { memo } from "react";
import { MapPinIcon } from "@heroicons/react/24/outline";
import { MapPinIcon as MapPinIconSolid } from "@heroicons/react/24/solid";

interface TripTimeDisplayProps {
  departureTime: string;
  arrivalTime: string;
  departureLocation: string;
  arrivalLocation: string;
  duration: string;
  variant?: "default" | "compact" | "large" | "bus-card";
  showLocations?: boolean;
}

const TripTimeDisplay = memo(
  ({
    departureTime,
    arrivalTime,
    departureLocation,
    arrivalLocation,
    duration,
    variant = "default",
    showLocations = true,
  }: TripTimeDisplayProps) => {
    const getTimeClasses = () => {
      switch (variant) {
        case "compact":
          return "text-lg font-bold text-[#181818]";
        case "large":
          return "text-3xl font-extrabold text-[#181818]";
        case "bus-card":
          return "text-2xl font-extrabold text-[#181818]";
        default:
          return "text-2xl font-extrabold text-[#181818]";
      }
    };

    const getLocationClasses = () => {
      switch (variant) {
        case "compact":
          return "text-xs text-[#181818]";
        case "large":
          return "text-base text-[#181818]";
        case "bus-card":
          return "text-sm font-normal text-[#181818]";
        default:
          return "text-sm text-[#181818]";
      }
    };

    const getDurationClasses = () => {
      switch (variant) {
        case "compact":
          return "text-sm text-[#5C5C5C] px-2";
        case "large":
          return "text-lg text-[#5C5C5C] px-2";
        case "bus-card":
          return "text-base font-normal text-[#5C5C5C] bg-white px-2 whitespace-nowrap";
        default:
          return "text-base text-[#5C5C5C] px-2";
      }
    };

    const getRouteLineClasses = () => {
      switch (variant) {
        case "bus-card":
          return "flex-1 border-t border-[#D7D7D7] border-dashed relative";
        default:
          return "flex-1 border-t border-[#D7D7D7] border-dashed";
      }
    };

    const getStartDotClasses = () => {
      switch (variant) {
        case "bus-card":
          return "w-4 h-4 bg-[#2D5BFF] rounded-full flex-shrink-0";
        default:
          return "w-4 h-4 bg-[#2D5BFF] rounded-full";
      }
    };

    const getEndIconClasses = () => {
      switch (variant) {
        case "bus-card":
          return "w-4 h-4 flex-shrink-0";
        default:
          return "w-4 h-4 text-[#FF7F37]";
      }
    };

    const renderRouteSection = () => {
      if (variant === "bus-card") {
        return (
          <div className="flex items-center gap-1 flex-1">
            <div className={getStartDotClasses()}></div>
            <div className={getRouteLineClasses()}>
              <span className="absolute top-[-12px] left-1/2 transform -translate-x-1/2 text-base font-normal text-[#5C5C5C] bg-white px-2 whitespace-nowrap">
                {duration}
              </span>
            </div>
            <div className={getEndIconClasses()}>
              <MapPinIconSolid className="w-full h-full text-[#FF7F37]" />
            </div>
          </div>
        );
      }

      return (
        <div className="flex-1 flex items-center gap-1">
          <div className={getStartDotClasses()}></div>
          <div className={getRouteLineClasses()}></div>
          <span className={getDurationClasses()}>{duration}</span>
          <div className={getRouteLineClasses()}></div>
          <MapPinIcon className={getEndIconClasses()} />
        </div>
      );
    };

    return (
      <div className="flex flex-col gap-2">
        {/* Time Row */}
        <div className="flex items-center gap-4">
          <span className={getTimeClasses()}>{departureTime}</span>
          {renderRouteSection()}
          <span className={getTimeClasses()}>{arrivalTime}</span>
        </div>

        {/* Location Row */}
        {showLocations && (
          <div className="flex justify-between">
            <span className={`${getLocationClasses()} w-40`}>
              {departureLocation}
            </span>
            <span className={`${getLocationClasses()} w-40 text-right`}>
              {arrivalLocation}
            </span>
          </div>
        )}
      </div>
    );
  }
);

TripTimeDisplay.displayName = "TripTimeDisplay";

export { TripTimeDisplay };
