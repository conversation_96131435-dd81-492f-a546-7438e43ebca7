import { formatPrice } from "@/features/merchants/utils";
import { useStore } from "@tanstack/react-store";
import { simpleCartStore, simpleCartActions } from "@/stores/simpleCartStore";

interface ItemSearchProps {
  item: {
    id: string;
    name: string;
    price: number | string;
    image_url?: string;
    merchantName: string;
    merchantLogo?: { sizes?: { large?: string } } | string;
    merchantId: string;
  };
  onSelectItem?: () => void;
}

export default function ItemSearch({ item, onSelectItem }: ItemSearchProps) {
  const quantity = useStore(
    simpleCartStore,
    (state) => state.items[item.id]?.quantity || 0
  );

  const handleIncrease = (e: React.MouseEvent) => {
    e.stopPropagation();
    simpleCartActions.addItem({
      id: item.id,
      name: item.name,
      price: Number(item.price),
      quantity: 1,
      image_url: item.image_url || "",
      merchantId: item.merchantId,
      description: "",
    });
  };
  const handleDecrease = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (quantity > 1) {
      simpleCartActions.updateItemQuantity(item.id, quantity - 1);
    } else if (quantity === 1) {
      simpleCartActions.removeItem(item.id);
    }
  };

  return (
    <div
      className="flex items-end gap-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
      onClick={onSelectItem}
    >
      <img
        src={
          typeof item.merchantLogo === "object"
            ? (item.merchantLogo?.sizes?.large ??
              "assets/merchants/restaurant_default.jpg")
            : item.merchantLogo || "assets/merchants/restaurant_default.jpg"
        }
        alt={item.name}
        className="w-15 h-15 rounded-lg object-cover"
      />
      <div className="flex-1">
        <div className="font-bold text-[#2563eb] text-sm">{item.name}</div>
        <div className="text-xs text-gray-500 mb-1.5">{item.merchantName}</div>
        <div className="text-xs font-bold text-gray-800">
          {formatPrice(Number(item.price))}
        </div>
      </div>
      {quantity === 0 ? (
        <button
          onClick={handleIncrease}
          className="w-6 h-6 rounded bg-[#2D5BFF] flex items-center justify-center"
        >
          <svg
            className="w-3.5 h-3.5 text-white"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4v16m8-8H4"
            />
          </svg>
        </button>
      ) : (
        <div className="flex items-center gap-2">
          <button
            onClick={handleDecrease}
            className="w-6 h-6 rounded bg-[#EFEFEF] flex items-center justify-center"
          >
            <svg
              className="w-3.5 h-3.5 text-[#7C7B7B]"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M20 12H4" />
            </svg>
          </button>
          <div className="w-8 h-6 rounded bg-[#F8F8F8] flex items-center justify-center px-2">
            <span className="text-xs font-normal text-[#5C5C5C]">
              {quantity}
            </span>
          </div>
          <button
            onClick={handleIncrease}
            className="w-6 h-6 rounded bg-[#2D5BFF] flex items-center justify-center"
          >
            <svg
              className="w-3.5 h-3.5 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 4v16m8-8H4"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}
