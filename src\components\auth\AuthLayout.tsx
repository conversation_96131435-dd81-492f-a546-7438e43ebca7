import React from "react";
import { cn } from "@/lib/utils";

interface AuthLayoutProps {
  children: React.ReactNode;
  className?: string;
  showBackgroundImage?: boolean;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  className,
  showBackgroundImage = true,
}) => {
  return (
    <div className="min-h-screen w-full mx-auto bg-gray-100 flex flex-col">
      {/* Main Container - White with rounded corners and shadow */}
      <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="mx-auto w-full max-w-7xl lg:max-w-[100%] xl:max-w-[90%] 2xl:max-w-[63%] min-h-[500px] lg:h-auto bg-white rounded-[24px] shadow-lg overflow-hidden flex">
          {/* Background Image Section - Desktop only */}
          {showBackgroundImage && (
            <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
              <img
                src="/assets/hero.svg"
                alt="Hero background"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Form Section */}
          <div
            className={cn(
              "flex-1 flex sm:items-center justify-center p-6 lg:p-12",
              showBackgroundImage ? "lg:w-1/2" : "w-full",
              className
            )}
          >
            <div className="w-full max-w-md min-h-[400px] sm:min-h-[500px]">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
