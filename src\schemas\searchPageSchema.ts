import { z } from "zod";

// Define the expected search parameters for the search results page
export const searchPageSearchSchema = z
  .object({
    fromPlaceId: z.number().optional(),
    toPlaceId: z.number().optional(),
    fromPlaceName: z.string().optional(),
    toPlaceName: z.string().optional(),
    departureDate: z.string().optional(), // Expecting YYYY-MM-DD
    returnDate: z.string().optional(), // Expecting YYYY-MM-DD
    numTickets: z.number().optional(),
    isRoundTrip: z.boolean().optional(),
    focusDate: z.enum(["departure", "return"]).optional(), // Which date picker to focus
  })
  .catchall(z.any());
