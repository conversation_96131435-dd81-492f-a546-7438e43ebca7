import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  useCallback,
  useEffect,
} from "react";
import { useNavigate } from "@tanstack/react-router";
import { usePlacesOnDemand } from "@/features/booking/hooks/usePlaces";
import {
  useFormValidation,
  type FormValues,
  type ValidationErrors,
} from "@/features/booking/hooks/useFormValidation";
import { useTranslation } from "@/shared/hooks/useTranslation";
import { safeNumber } from "@/lib/utils";
import { convertToISODate } from "@/lib/dateUtils";
import { saveRecentSearch } from "@/lib/recentSearches";

interface Place {
  id: number;
  name: string;
  code: string;
}

interface SearchFormState {
  fromLocation: string;
  toLocation: string;
  isRoundTrip: boolean;
  departureDate: string;
  returnDate: string;
  numTickets: number;
}

type SearchFormVariant = "homepage" | "search-page";

interface SearchFormContextValue {
  // State
  state: SearchFormState;
  variant: SearchFormVariant;

  // Actions
  setFromLocation: (value: string) => void;
  setToLocation: (value: string) => void;
  setIsRoundTrip: (value: boolean) => void;
  setDepartureDate: (value: string) => void;
  setReturnDate: (value: string) => void;
  setNumTickets: (value: number) => void;
  swapLocations: () => void;
  resetForm: () => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;

  // Validation
  errors: ValidationErrors;
  touched: Record<string, boolean>;
  isValid: boolean;
  hasErrors: boolean;
  setErrors: (errors: ValidationErrors) => void;

  // Data
  places: Place[];
  filteredFromPlaces: Place[];
  filteredToPlaces: Place[];

  // Loading states
  isLoading: boolean;
  error: any;

  // Callback management
  addSubmitSuccessCallback: (callback: () => void) => void;
  removeSubmitSuccessCallback: (callback: () => void) => void;
}

const SearchFormContext = createContext<SearchFormContextValue | undefined>(
  undefined
);

const initialState: SearchFormState = {
  fromLocation: "Hà Nội",
  toLocation: "",
  isRoundTrip: false,
  departureDate: "",
  returnDate: "",
  numTickets: 1,
};

interface SearchFormProviderProps {
  children: React.ReactNode;
  variant?: SearchFormVariant;
  initialValues?: Partial<SearchFormState>;
  onSubmitSuccess?: () => void;
}

export const SearchFormProvider: React.FC<SearchFormProviderProps> = ({
  children,
  variant = "homepage",
  initialValues,
  onSubmitSuccess,
}) => {
  const { t } = useTranslation();

  // Determine if round trip should be enabled based on initial values
  // Priority: 1. explicit isRoundTrip value, 2. presence of returnDate
  const shouldEnableRoundTrip =
    initialValues?.isRoundTrip ?? (initialValues?.returnDate ? true : false);

  const [state, setState] = useState<SearchFormState>({
    ...initialState,
    ...initialValues,
    // Override isRoundTrip based on logic above
    isRoundTrip: shouldEnableRoundTrip,
    // Ensure numTickets is always a positive number
    numTickets: safeNumber(initialValues?.numTickets, 1),
  });
  const navigate = useNavigate();

  // Callback registry for submit success
  const [submitSuccessCallbacks, setSubmitSuccessCallbacks] = useState<
    (() => void)[]
  >([]);

  // Add submit success callback
  const addSubmitSuccessCallback = useCallback((callback: () => void) => {
    setSubmitSuccessCallbacks((prev) => [...prev, callback]);
  }, []);

  // Remove submit success callback
  const removeSubmitSuccessCallback = useCallback((callback: () => void) => {
    setSubmitSuccessCallbacks((prev) => prev.filter((cb) => cb !== callback));
  }, []);

  const {
    data: places = [],
    isLoading,
    error,
    refetch: fetchPlaces,
  } = usePlacesOnDemand();

  useEffect(() => {
    fetchPlaces();
  }, []);

  const {
    errors,
    touched,
    setFieldTouched,
    setErrors,
    validateForm,
    isValid,
    hasErrors,
  } = useFormValidation();

  // Filter places based on input values with memoization
  const filteredFromPlaces = useMemo(
    () =>
      places.filter((place: Place) =>
        place.name.toLowerCase().includes(state.fromLocation.toLowerCase())
      ),
    [places, state.fromLocation]
  );

  const filteredToPlaces = useMemo(
    () =>
      places.filter((place: Place) =>
        place.name.toLowerCase().includes(state.toLocation.toLowerCase())
      ),
    [places, state.toLocation]
  );

  // Actions with validation
  const setFromLocation = useCallback(
    (value: string) => {
      setState((prev) => ({ ...prev, fromLocation: value }));
      // Clear error when user starts typing/selecting
      if (errors.fromLocation) {
        setErrors((prev) => ({ ...prev, fromLocation: null }));
      }
    },
    [errors.fromLocation, setErrors]
  );

  const setToLocation = useCallback(
    (value: string) => {
      setState((prev) => ({ ...prev, toLocation: value }));
      // Clear error when user starts typing/selecting
      if (errors.toLocation) {
        setErrors((prev) => ({ ...prev, toLocation: null }));
      }
    },
    [errors.toLocation, setErrors]
  );

  const setIsRoundTrip = useCallback((value: boolean) => {
    setState((prev) => ({
      ...prev,
      isRoundTrip: value,
      // Clear return date when disabling round trip
      returnDate: value ? prev.returnDate : "",
    }));
  }, []);

  const setDepartureDate = useCallback(
    (value: string) => {
      setState((prev) => {
        // Check if return date needs to be cleared using utility function
        let newReturnDate = prev.returnDate;
        if (prev.returnDate && value) {
          const departureDate = new Date(convertToISODate(value));
          const returnDate = new Date(convertToISODate(prev.returnDate));

          // Clear return date if it's not after the new departure date
          if (returnDate <= departureDate) {
            newReturnDate = "";
          }
        }

        return {
          ...prev,
          departureDate: value,
          returnDate: newReturnDate,
        };
      });
      // Clear error when user selects date
      if (errors.departureDate) {
        setErrors((prev) => ({ ...prev, departureDate: null }));
      }
      // Clear return date error if return date was cleared
      if (errors.returnDate) {
        setErrors((prev) => ({ ...prev, returnDate: null }));
      }
    },
    [errors.departureDate, errors.returnDate, setErrors]
  );

  const setReturnDate = useCallback(
    (value: string) => {
      setState((prev) => ({
        ...prev,
        returnDate: value,
        // Automatically enable round trip when return date is set
        isRoundTrip: value ? true : prev.isRoundTrip,
      }));
      // Clear error when user selects date
      if (errors.returnDate) {
        setErrors((prev) => ({ ...prev, returnDate: null }));
      }
    },
    [errors.returnDate, setErrors]
  );

  const setNumTickets = useCallback(
    (value: number) => {
      // Use utility function to ensure safe number conversion
      const safeValue = safeNumber(value, 1);
      setState((prev) => ({ ...prev, numTickets: safeValue }));
      // Clear error when user changes number of tickets
      if (errors.numTickets && safeValue > 0) {
        setErrors((prev) => ({ ...prev, numTickets: null }));
      }
    },
    [errors.numTickets, setErrors]
  );

  const swapLocations = useCallback(() => {
    // Disabled - do nothing since fromLocation is fixed to "Hà Nội"
    return;
  }, []);

  const resetForm = useCallback(() => {
    setState(initialState);
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      // First, fetch places data if not already loaded
      let currentPlaces = places;
      if (!places.length) {
        try {
          const result = await fetchPlaces();
          currentPlaces = result.data || [];
        } catch (error) {
          console.error("Failed to fetch places:", error);
          return; // Exit early if we can't fetch places
        }
      }

      // Validate all fields
      const currentFormValues: FormValues = {
        fromLocation: state.fromLocation,
        toLocation: state.toLocation,
        departureDate: state.departureDate,
        returnDate: state.returnDate,
        numTickets: state.numTickets,
        isRoundTrip: state.isRoundTrip,
      };

      const validationErrors = validateForm(currentFormValues);

      // Set errors in state
      setErrors(validationErrors);

      // Mark all fields as touched
      Object.keys(validationErrors).forEach((field) => {
        setFieldTouched(field, true);
      });

      // Check if form is valid
      const hasValidationErrors = Object.values(validationErrors).some(
        (error) => error !== null
      );

      if (hasValidationErrors) {
        console.warn("Form has validation errors:", validationErrors);
        return;
      }

      const fromPlace = currentPlaces.find(
        (p: Place) => p.name === state.fromLocation
      );
      const toPlace = currentPlaces.find(
        (p: Place) => p.name === state.toLocation
      );

      // Handle special case for Hà Nội - always use ID 24
      const fromPlaceId = state.fromLocation === "Hà Nội" ? 24 : fromPlace?.id;
      const toPlaceId = toPlace?.id;

      if (fromPlaceId && toPlaceId) {
        const searchParams: any = {
          fromPlaceId: Number(fromPlaceId),
          toPlaceId: Number(toPlaceId),
          fromPlaceName: state.fromLocation,
          toPlaceName: state.toLocation,
        };

        if (state.departureDate) {
          searchParams.departureDate = state.departureDate;
        }

        if (state.numTickets) {
          searchParams.numTickets = Number(state.numTickets);
        }

        // Include round trip information
        if (state.isRoundTrip) {
          searchParams.isRoundTrip = true;
          if (state.returnDate) {
            searchParams.returnDate = state.returnDate;
          }
        }

        // Call onSubmitSuccess callback before navigation
        if (onSubmitSuccess) {
          onSubmitSuccess();
        }

        // Call all registered submit success callbacks
        submitSuccessCallbacks.forEach((callback) => callback());

        navigate({ to: "/booking/search", search: searchParams });

        // Save recent search
        saveRecentSearch({
          fromLocation: state.fromLocation,
          toLocation: state.toLocation,
          fromPlaceId: Number(fromPlaceId),
          toPlaceId: Number(toPlaceId),
          departureDate: state.departureDate,
          returnDate: state.returnDate,
          numTickets: state.numTickets,
          isRoundTrip: state.isRoundTrip,
        });
      } else {
        // Show specific error for missing destination
        if (!toPlaceId) {
          setErrors((prev) => ({
            ...prev,
            toLocation: t("form.validation.selectDestination"),
          }));
          setFieldTouched("toLocation", true);
        }
        console.warn(t("bus.invalidLocation"));
      }
    },
    [
      state,
      places,
      navigate,
      validateForm,
      setErrors,
      setFieldTouched,
      fetchPlaces,
      t,
      onSubmitSuccess,
      submitSuccessCallbacks,
    ]
  );

  const contextValue: SearchFormContextValue = {
    state,
    variant,
    setFromLocation,
    setToLocation,
    setIsRoundTrip,
    setDepartureDate,
    setReturnDate,
    setNumTickets,
    swapLocations,
    resetForm,
    handleSubmit,
    errors,
    touched,
    isValid,
    hasErrors,
    setErrors,
    places,
    filteredFromPlaces,
    filteredToPlaces,
    isLoading,
    error,
    addSubmitSuccessCallback,
    removeSubmitSuccessCallback,
  };

  return (
    <SearchFormContext.Provider value={contextValue}>
      {children}
    </SearchFormContext.Provider>
  );
};

export const useSearchForm = (): SearchFormContextValue => {
  const context = useContext(SearchFormContext);
  if (context === undefined) {
    throw new Error("useSearchForm must be used within a SearchFormProvider");
  }
  return context;
};
