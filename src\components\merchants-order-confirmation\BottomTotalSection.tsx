import React from "react";
import { cn } from "@/lib/utils";
import { formatPrice } from "@/features/merchants/utils";

interface BottomTotalSectionProps {
  buttonTitle?: string;
  totalPrice: number;
  onPlaceOrder: () => void;
  isPlacingOrder: boolean;
  disabled?: boolean;
  bgColor?: string;
}

export const BottomTotalSection: React.FC<BottomTotalSectionProps> = ({
  buttonTitle = "Đặt đơn",
  totalPrice,
  onPlaceOrder,
  isPlacingOrder,
  disabled = false,
  bgColor = "bg-[#2D5BFF]",
}) => {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white p-5 rounded-t-[20px] shadow-lg">
      <div className="flex items-center justify-between mb-2.5">
        <span className="text-base font-normal text-[#181818]">Tổng tiền</span>
        <span className="text-base font-bold text-[#181818]">
          {formatPrice(totalPrice)}
        </span>
      </div>

      <button
        onClick={onPlaceOrder}
        disabled={isPlacingOrder || disabled}
        className={cn(
          "w-full rounded-lg py-3 px-8 flex items-center justify-center",
          isPlacingOrder || disabled
            ? "bg-gray-400 cursor-not-allowed"
            : bgColor
        )}
      >
        {isPlacingOrder ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span className="text-lg font-extrabold text-white tracking-wider">
              Đang đặt đơn...
            </span>
          </div>
        ) : (
          <span className="text-lg font-extrabold text-white tracking-wider">
            {buttonTitle}
          </span>
        )}
      </button>
    </div>
  );
};
