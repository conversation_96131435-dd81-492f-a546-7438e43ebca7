import { createFileRoute, useParams } from "@tanstack/react-router";
import { Container } from "@/shared/components/Container";
import { MarkdownContent } from "@/shared/components/MarkdownContent";
import { useDiscountById } from "@/shared/hooks/useDiscounts";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

export const Route = createFileRoute("/promotion/$id")({
  component: PromotionDetail,
});

function PromotionDetail() {
  const { id } = useParams({ from: "/promotion/$id" });
  const { data: promotion, isLoading, error } = useDiscountById(id);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <Container>
          <div className="animate-pulse">
            <div className="h-6 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-5/6"></div>
              <div className="h-4 bg-gray-300 rounded w-4/6"></div>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  if (error || !promotion) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <Container>
          <div className="text-center py-20">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Không tìm thấy khuyến mãi
            </h1>
            <p className="text-gray-600 mb-6">
              Khuyến mãi bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
            </p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-2 lg:pb-20 lg:pt-5">
      <Container>
        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl lg:text-3xl font-bold text-[#181818] mb-4 leading-tight">
              {promotion.title}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>
                Ngày tạo:{" "}
                {new Date(promotion.created_at).toLocaleDateString("vi-VN")}
              </span>
              <span>
                Ngày cập nhật:{" "}
                {new Date(promotion.updated_at).toLocaleDateString("vi-VN")}
              </span>
            </div>
          </div>

          {/* Main Image */}
          <div className="relative w-full h-64 lg:h-[450px] bg-gray-200">
            <img
              src={getStrapiImageUrl(
                promotion.image_background,
                "/mock-images/image-2.png"
              )}
              alt={promotion.title}
              className="w-full h-full object-center"
            />
          </div>

          {/* Content Body */}
          <div className="p-6">
            {/* Main Content */}
            {promotion.content && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Nội dung khuyến mãi
                </h2>
                <MarkdownContent content={promotion.content} />
              </div>
            )}

            {/* Discount Information */}
            {(promotion.discount_percentage ||
              promotion.discount_amount ||
              promotion.code) && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thông tin ưu đãi
                </h2>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  {promotion.discount_percentage && (
                    <p className="text-green-800 font-semibold text-lg">
                      Giảm {promotion.discount_percentage}%
                    </p>
                  )}
                  {promotion.discount_amount && (
                    <p className="text-green-800 font-semibold text-lg">
                      Giảm {promotion.discount_amount.toLocaleString("vi-VN")}đ
                    </p>
                  )}
                  {promotion.code && (
                    <p className="text-green-700 mt-2">
                      Mã code:{" "}
                      <span className="font-mono font-bold">
                        {promotion.code}
                      </span>
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Validity Period */}
            {promotion.valid_from && promotion.valid_until && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thời gian áp dụng
                </h2>
                <div className="space-y-2 text-gray-700">
                  <p>
                    Từ ngày:{" "}
                    {new Date(promotion.valid_from).toLocaleDateString("vi-VN")}
                  </p>
                  <p>
                    Đến ngày:{" "}
                    {new Date(promotion.valid_until).toLocaleDateString(
                      "vi-VN"
                    )}
                  </p>
                </div>
              </div>
            )}

            {/* Published Date */}
            {promotion.published_at && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thời gian xuất bản
                </h2>
                <p className="text-gray-700">
                  {new Date(promotion.published_at).toLocaleDateString("vi-VN")}
                </p>
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
}
