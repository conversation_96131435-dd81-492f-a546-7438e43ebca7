import React, { useState, useCallback } from "react";
import {
  DocumentArrowDownIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import NoScanInfoSection from "./NoScanInfoSection";
import { BottomTotalSection } from "./BottomTotalSection";

interface VietQRPopupProps {
  show: boolean;
  onClose: () => void;
  qrData: {
    accountNumber: string;
    accountName: string;
    bank: string;
    qrImage: string;
    amount?: string | number;
    content?: string;
    vietqrLogo?: string; // for VietQR logo if needed
    totalText?: string; // e.g. "230.000 đ"
    contentNote?: string; // e.g. "Không bắt buộc"
    autoConfirmNote?: string; // e.g. "Hệ thống sẽ tự động xác nhận giao dịch"
  };
  onDownloadQR: () => void;
  onConfirmTransfer?: () => void;
  totalPrice: number;
  expiryText?: string;
  bgColor?: string;
  buttonTitle?: string;
}

export const VietQRPopup: React.FC<VietQRPopupProps> = ({
  show,
  qrData,
  onDownloadQR,
  onConfirmTransfer,
  totalPrice,
  expiryText,
  bgColor,
  buttonTitle = "Tôi đã chuyển khoản",
}) => {
  const [copiedAccount, setCopiedAccount] = useState(false);
  const [copiedAmount, setCopiedAmount] = useState(false);
  const [copiedContent, setCopiedContent] = useState(false);
  console.log("copiedAccount", copiedAccount);
  console.log("copiedAmount", copiedAmount);
  console.log("copiedContent", copiedContent);
  const handleCopyAccount = useCallback(() => {
    navigator.clipboard.writeText(qrData.accountNumber);
    setCopiedAccount(true);
    setTimeout(() => setCopiedAccount(false), 1200);
  }, [qrData.accountNumber]);

  const handleCopyAmount = useCallback(() => {
    navigator.clipboard.writeText(String(qrData.amount));
    setCopiedAmount(true);
    setTimeout(() => setCopiedAmount(false), 1200);
  }, [qrData.amount]);

  const handleCopyContent = useCallback(() => {
    if (qrData.content) {
      navigator.clipboard.writeText(qrData.content);
      setCopiedContent(true);
      setTimeout(() => setCopiedContent(false), 1200);
    }
  }, [qrData.content]);

  if (!show) return null;

  return (
    <div className="w-full min-h-screen bg-[#F8F8F8] px-4 pt-8 pb-32">
      {/* Main card */}
      <div className="bg-white rounded-2xl shadow-xl flex flex-col relative min-h-[600px] p-4">
        {/* Title + Expiry (not sticky) */}
        <div className="pt-8 pb-2 text-center">
          <h2 className="text-2xl font-extrabold text-[#181818] leading-tight mb-2">
            Thanh toán Online
          </h2>
          {expiryText && (
            <div className="mt-1 text-center">
              <span className="inline-block rounded bg-[#FEF5F5] text-xs text-[#FF3030] font-normal px-3 py-1">
                {expiryText}
              </span>
            </div>
          )}
        </div>
        {/* Content */}
        <div className="flex-1 px-0 py-4 flex flex-col gap-6 items-center justify-center">
          <div className="flex flex-row gap-4 justify-center">
            {/* Steps */}
            <VietQRSteps className="py-2 px-4 w-full max-w-[100px]" />
            {/* QR + Logo */}
            <div className="flex flex-col items-center justify-center bg-white border-4 border-[#2D5BFF] rounded-xl shadow w-[227px] h-[227px] relative p-4">
              <img
                src={qrData.qrImage}
                alt="VietQR"
                className="w-full h-full object-contain rounded-lg bg-gray-100"
              />
            </div>
          </div>
          {/* Download Button (secondary) */}
          <div className="flex justify-center w-full">
            <button
              className="w-full flex items-center justify-center gap-2 bg-[#ECF0FF] text-[#2D5BFF] rounded-xl py-3 font-extrabold text-base border border-[#2D5BFF] hover:bg-[#dbeafe] transition-colors"
              onClick={onDownloadQR}
            >
              <DocumentArrowDownIcon className="w-5 h-5" />
              Lưu mã QR về máy
            </button>
          </div>
          {/* Info Card - NoScanInfoSection */}
          <div className="w-full">
            <NoScanInfoSection
              bank={qrData.bank}
              accountNumber={qrData.accountNumber}
              accountName={qrData.accountName}
              amount={totalPrice}
              content={qrData.content || "Không bắt buộc"}
              onCopyAccount={handleCopyAccount}
              onCopyAmount={handleCopyAmount}
              onCopyContent={handleCopyContent}
            />
          </div>
        </div>
        {/* Bottom Bar */}
        {onConfirmTransfer && (
          <BottomTotalSection
            buttonTitle={buttonTitle}
            totalPrice={totalPrice}
            onPlaceOrder={onConfirmTransfer}
            isPlacingOrder={false}
            disabled={false}
            bgColor={bgColor}
          />
        )}
      </div>
    </div>
  );
};

// Step hướng dẫn VietQR (dùng chung)
export const VietQRSteps: React.FC<{ className?: string }> = ({
  className,
}) => (
  <div
    className={`flex flex-col gap-4 bg-[#EDF8FF] rounded-lg py-2 px-2 w-[120px] items-center justify-center ${className ?? ""}`}
  >
    <div className="flex flex-col items-center gap-1">
      <DocumentArrowDownIcon className="w-6 h-6 text-[#2D5BFF]" />
      <span className="text-xs font-bold text-[#181818] text-center">
        Lưu ảnh mã QR về máy
      </span>
    </div>
    <div className="flex flex-col items-center gap-1">
      <DevicePhoneMobileIcon className="w-6 h-6 text-[#2D5BFF]" />
      <span className="text-xs font-bold text-[#181818] text-center">
        Tải lên app thanh toán
      </span>
    </div>
    <div className="flex flex-col items-center gap-1">
      <CheckCircleIcon className="w-8 h-8 text-[#2D5BFF]" />
      <span className="text-xs font-bold text-[#181818] text-center">
        Chờ xác nhận
      </span>
    </div>
  </div>
);
