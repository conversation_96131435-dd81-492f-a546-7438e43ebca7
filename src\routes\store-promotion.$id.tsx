import { createFileRoute, useParams } from "@tanstack/react-router";
import { Container } from "@/shared/components/Container";
import { MarkdownContent } from "@/shared/components/MarkdownContent";
import { useDiscountBusOperatorById } from "@/features/booking/hooks/useDiscountBusOperators";
import { getStrapiImageUrl } from "@/shared/utils/strapiHelpers";

export const Route = createFileRoute("/store-promotion/$id")({
  component: StorePromotionDetail,
});

function StorePromotionDetail() {
  const { id } = useParams({ from: "/store-promotion/$id" });
  const {
    data: storePromotion,
    isLoading,
    error,
  } = useDiscountBusOperatorById(id);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <Container>
          <div className="animate-pulse">
            <div className="h-6 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-5/6"></div>
              <div className="h-4 bg-gray-300 rounded w-4/6"></div>
            </div>
          </div>
        </Container>
      </div>
    );
  }

  if (error || !storePromotion) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <Container>
          <div className="text-center py-20">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              Không tìm thấy ưu đãi từ nhà xe
            </h1>
            <p className="text-gray-600 mb-6">
              Ưu đãi bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
            </p>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-2 lg:pb-20 lg:pt-5">
      <Container>
        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl lg:text-3xl font-bold text-[#181818] mb-4 leading-tight">
              {storePromotion.title}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Nhà xe tại: {storePromotion.place}</span>
              <span>
                Ngày tạo:{" "}
                {new Date(storePromotion.created_at).toLocaleDateString(
                  "vi-VN"
                )}
              </span>
            </div>
          </div>

          {/* Main Image */}
          <div className="relative w-full h-64 lg:h-[500px] bg-gray-200">
            <img
              src={getStrapiImageUrl(
                storePromotion.image_background,
                "/mock-images/image-2.png"
              )}
              alt={storePromotion.title}
              className="w-full h-full object-center"
            />
          </div>

          {/* Content Body */}
          <div className="p-6">
            {/* Content/Description */}
            {storePromotion.content && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Nội dung ưu đãi
                </h2>
                <MarkdownContent content={storePromotion.content} />
              </div>
            )}

            {/* Discount Place Information */}
            {storePromotion.discount_place && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thông tin địa điểm
                </h2>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 font-semibold">
                    {storePromotion.discount_place.name}
                  </p>
                </div>
              </div>
            )}

            {/* Published Date */}
            {storePromotion.published_at && (
              <div className="mb-6">
                <h2 className="text-xl font-bold text-[#181818] mb-3">
                  Thời gian xuất bản
                </h2>
                <p className="text-gray-700">
                  {new Date(storePromotion.published_at).toLocaleDateString(
                    "vi-VN"
                  )}
                </p>
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
}
