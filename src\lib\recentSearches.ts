export interface RecentSearch {
  id: string;
  fromLocation: string;
  toLocation: string;
  fromPlaceId: number;
  toPlaceId: number;
  departureDate: string;
  returnDate?: string;
  numTickets: number;
  isRoundTrip: boolean;
  timestamp: number;
}

const RECENT_SEARCHES_KEY = "recent_searches";
const MAX_RECENT_SEARCHES = 3;

export const getRecentSearches = (): RecentSearch[] => {
  try {
    const stored = localStorage.getItem(RECENT_SEARCHES_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error("Error reading recent searches from localStorage:", error);
    return [];
  }
};

export const saveRecentSearch = (
  search: Omit<RecentSearch, "id" | "timestamp">
): void => {
  try {
    const recentSearches = getRecentSearches();

    // Create new search with id and timestamp
    const newSearch: RecentSearch = {
      ...search,
      id: `${search.fromLocation}-${search.toLocation}-${search.departureDate}-${Date.now()}`,
      timestamp: Date.now(),
    };

    // Remove duplicate searches (same from, to, departure date)
    const filteredSearches = recentSearches.filter(
      (existing) =>
        !(
          existing.fromLocation === search.fromLocation &&
          existing.toLocation === search.toLocation &&
          existing.departureDate === search.departureDate
        )
    );

    // Add new search at the beginning
    const updatedSearches = [newSearch, ...filteredSearches].slice(
      0,
      MAX_RECENT_SEARCHES
    );

    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
  } catch (error) {
    console.error("Error saving recent search to localStorage:", error);
  }
};

export const removeRecentSearch = (id: string): void => {
  try {
    const recentSearches = getRecentSearches();
    const filteredSearches = recentSearches.filter(
      (search) => search.id !== id
    );
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(filteredSearches));
  } catch (error) {
    console.error("Error removing recent search from localStorage:", error);
  }
};

export const clearRecentSearches = (): void => {
  try {
    localStorage.removeItem(RECENT_SEARCHES_KEY);
  } catch (error) {
    console.error("Error clearing recent searches from localStorage:", error);
  }
};
