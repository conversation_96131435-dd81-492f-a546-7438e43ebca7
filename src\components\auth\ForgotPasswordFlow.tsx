import React, { useState } from "react";
import { ForgotPasswordForm } from "./ForgotPasswordForm";
import { ForgotPasswordOtp } from "./ForgotPasswordOtp";
import { ForgotPasswordReset } from "./ForgotPasswordReset";
import { ForgotPasswordSuccess } from "./ForgotPasswordSuccess";
import {
  useForgotPassword,
  useForgotPasswordVerifyOtp,
  useResetPassword,
} from "@/shared/hooks/useAuth";
import { getTranslatedErrorMessage } from "@/shared/utils/errorMessageTranslator";
import { clearResetToken } from "@/shared/utils/tokenUtils";

interface ForgotPasswordFlowProps {
  onBack: () => void;
  className?: string;
}

type ForgotPasswordStep =
  | "forgot-phone"
  | "forgot-otp"
  | "forgot-reset"
  | "forgot-success";

export const ForgotPasswordFlow: React.FC<ForgotPasswordFlowProps> = ({
  onBack,
  className,
}) => {
  const [currentStep, setCurrentStep] =
    useState<ForgotPasswordStep>("forgot-phone");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [error, setError] = useState("");
  const [otpError, setOtpError] = useState("");
  const [isResendingOtp, setIsResendingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);

  const { mutateAsync: forgotPassword, isPending: isForgotPasswordLoading } =
    useForgotPassword();
  const { mutateAsync: forgotPasswordVerifyOtp } = useForgotPasswordVerifyOtp();
  const { mutateAsync: resetPassword, isPending: isResetPasswordLoading } =
    useResetPassword();

  const handleForgotPasswordSubmit = async (phone: string) => {
    try {
      setError("");
      const response = await forgotPassword({ phone_number: phone });

      // Check for errors in response data even if HTTP status is success
      const responseData = response as any;
      if (
        responseData?.error_type === "RATE_LIMITED" ||
        responseData?.success === false
      ) {
        const errorMessage = getTranslatedErrorMessage({
          response: { data: responseData },
        });
        setError(errorMessage);
        return;
      }

      setPhoneNumber(phone);
      setCurrentStep("forgot-otp");
    } catch (error: any) {
      console.error("Forgot password failed:", error);
      const errorMessage = getTranslatedErrorMessage(error);
      setError(errorMessage);
    }
  };

  const handleOtpVerified = async (otp: string) => {
    try {
      setIsVerifyingOtp(true);
      setOtpError("");
      await forgotPasswordVerifyOtp({
        phone_number: phoneNumber,
        otp_code: otp,
      });
      setCurrentStep("forgot-reset");
    } catch (error: any) {
      console.error("Forgot password OTP verification failed:", error);
      const errorMessage = getTranslatedErrorMessage(error);
      setOtpError(errorMessage);
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  const handlePasswordReset = async (newPassword: string) => {
    try {
      const response = await resetPassword({
        phone_number: phoneNumber,
        password: newPassword,
      });

      // Check if the response indicates success
      if (response.success) {
        setCurrentStep("forgot-success");
      } else {
        // Handle case where API returns success: false
        setError(response.message || "Đặt lại mật khẩu thất bại");
      }
    } catch (error: any) {
      console.error("Reset password failed:", error);
      setError(getTranslatedErrorMessage(error));
    }
  };

  const handleResendOtp = async () => {
    try {
      setIsResendingOtp(true);
      setOtpError("");
      const response = await forgotPassword({
        phone_number: phoneNumber,
      });

      // Check for errors in response data even if HTTP status is success
      const responseData = response as any;
      if (
        responseData?.error_type === "RATE_LIMITED" ||
        responseData?.success === false
      ) {
        const errorMessage = getTranslatedErrorMessage({
          response: { data: responseData },
        });
        setOtpError(errorMessage);
        return;
      }
    } catch (error: any) {
      console.error("Resend forgot password OTP failed:", error);
      setOtpError(getTranslatedErrorMessage(error));
    } finally {
      setIsResendingOtp(false);
    }
  };

  const handleBack = () => {
    if (currentStep === "forgot-phone") {
      // Clean up reset token when navigating back
      clearResetToken();
      onBack();
    } else {
      setCurrentStep("forgot-phone");
    }
  };

  // Render based on current step
  switch (currentStep) {
    case "forgot-phone":
      return (
        <ForgotPasswordForm
          onSubmit={handleForgotPasswordSubmit}
          onBack={handleBack}
          isLoading={isForgotPasswordLoading}
          error={error}
          onError={setError}
          className={className}
        />
      );

    case "forgot-otp":
      return (
        <ForgotPasswordOtp
          phoneNumber={phoneNumber}
          onVerifySuccess={handleOtpVerified}
          onBack={handleBack}
          onResendOtp={handleResendOtp}
          isLoading={isVerifyingOtp}
          isResendLoading={isResendingOtp}
          error={otpError}
          onError={setOtpError}
          className={className}
        />
      );

    case "forgot-reset":
      return (
        <ForgotPasswordReset
          onPasswordReset={handlePasswordReset}
          onBack={handleBack}
          isLoading={isResetPasswordLoading}
          className={className}
        />
      );

    case "forgot-success":
      return <ForgotPasswordSuccess className={className} />;

    default:
      return null;
  }
};
