import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import type { BusImage } from "@/features/booking/api/bus-schedules/bus-schedules.types";
import type {
  SortOption,
  TimeSlot,
  BusType,
  BusFilters,
} from "@/shared/types/bus";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formats duration from minutes to short format (1h5p)
 * @param minutes - Duration in minutes
 * @returns Formatted string like "1h40p" or "45p"
 */
export function formatDuration(durationMinutes: number): string {
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;

  if (hours === 0) {
    return `${minutes}p`;
  } else if (minutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h${minutes}p`;
  }
}

/**
 * Formats time string to HH:MM format
 * @param timeString - Time string in various formats (HH:MM:SS, HH:MM, etc.)
 * @returns Formatted time string as HH:MM
 */
export function formatTime(timeString: string): string {
  if (!timeString || timeString === "N/A") {
    return timeString;
  }

  try {
    // Remove any extra whitespace
    const cleanTime = timeString.trim();

    // If already in HH:MM format, ensure proper padding
    if (cleanTime.match(/^\d{1,2}:\d{2}$/)) {
      const [hours, minutes] = cleanTime.split(":");
      return `${hours.padStart(2, "0")}:${minutes}`;
    }

    // If in HH:MM:SS format, take only HH:MM part
    if (cleanTime.match(/^\d{1,2}:\d{2}:\d{2}$/)) {
      const [hours, minutes] = cleanTime.split(":");
      return `${hours.padStart(2, "0")}:${minutes}`;
    }

    // If it's a Date object string or ISO string, parse it
    if (cleanTime.includes("T") || cleanTime.includes("-")) {
      const date = new Date(cleanTime);
      if (!isNaN(date.getTime())) {
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      }
    }

    // Return original if no pattern matches
    return timeString;
  } catch {
    return timeString;
  }
}

/**
 * Formats price for Vietnamese currency display
 * @param price - Price number
 * @returns Formatted price string
 */
export function formatPrice(price: number): string {
  return new Intl.NumberFormat("vi-VN").format(price);
}

/**
 * Formats a number with Vietnamese locale
 * @param num - Number to format
 * @returns Formatted number string
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat("vi-VN").format(num);
}

/**
 * Validates if a number is positive
 * @param num - Number to validate
 * @returns boolean indicating if number is positive
 */
export function isPositiveNumber(num: number | string): boolean {
  const numValue = typeof num === "string" ? Number(num) : num;
  return !isNaN(numValue) && numValue > 0;
}

/**
 * Safely converts string to number with fallback
 * @param value - String or number value
 * @param fallback - Fallback value if conversion fails
 * @returns Converted number or fallback
 */
export function safeNumber(
  value: string | number | undefined,
  fallback: number = 0
): number {
  if (typeof value === "number") return value;
  if (typeof value === "string") {
    const parsed = Number(value);
    return isNaN(parsed) ? fallback : parsed;
  }
  return fallback;
}

/**
 * Gets the optimal image URL from bus images array
 * @param images - Array of bus images
 * @param targetSize - Preferred size for the image
 * @returns Optimized image URL or fallback
 */
export function getOptimalImageUrl(
  images: BusImage[],
  preferredSize: keyof BusImage["sizes"] = "medium"
): string {
  if (!images || images.length === 0) {
    return "/mock-images/bus-default.png"; // fallback image
  }

  const image = images[0];
  return (
    image.sizes[preferredSize] || image.sizes.medium || image.sizes.original
  );
}

// Convert UI sort option to API parameters
export function convertSortToAPIParams(sortBy: SortOption): {
  sortByDepartureTime?: "asc" | "desc";
  sortByPrice?: "asc" | "desc";
} {
  switch (sortBy) {
    case "earliest":
      return { sortByDepartureTime: "asc" };
    case "latest":
      return { sortByDepartureTime: "desc" };
    case "cheapest":
      return { sortByPrice: "asc" };
    case "most_expensive":
      return { sortByPrice: "desc" };
    case "default":
    default:
      return {};
  }
}

// Convert UI bus types to API bus types
export function convertBusTypesToAPI(busTypes: BusType[]): string[] {
  const typeMapping: Record<BusType, string> = {
    standard: "chair",
    sleeper: "bed",
    limousine: "limousine",
  };

  return busTypes.map((type) => typeMapping[type]);
}

// Convert time slots to time ranges
export function convertTimeSlotsToTimeRange(timeSlots: TimeSlot[]): {
  departureTimeFrom?: string;
  departureTimeTo?: string;
} {
  if (timeSlots.length === 0) return {};

  const timeRanges: Record<TimeSlot, { from: string; to: string }> = {
    early_morning: { from: "00:00", to: "06:00" },
    morning: { from: "06:00", to: "12:00" },
    afternoon: { from: "12:00", to: "18:00" },
    evening: { from: "18:00", to: "23:59" },
    night: { from: "00:00", to: "06:00" },
  };

  // If multiple time slots selected, find the earliest start and latest end
  const times = timeSlots.map((slot) => timeRanges[slot]);
  const startTimes = times.map((t) => t.from);
  const endTimes = times.map((t) => t.to);

  const earliestStart = startTimes.sort()[0];
  const latestEnd = endTimes.sort().reverse()[0];

  return {
    departureTimeFrom: earliestStart,
    departureTimeTo: latestEnd,
  };
}

// Convert all UI filters to API parameters
export function convertFiltersToAPIParams(filters: BusFilters) {
  const sortParams = convertSortToAPIParams(filters.sortBy);
  const timeParams = convertTimeSlotsToTimeRange(filters.timeSlots);
  const busTypes = convertBusTypesToAPI(filters.busTypes);

  return {
    ...sortParams,
    ...timeParams,
    ...(busTypes.length > 0 && { busTypes }),
  };
}

export const formatTimeAgo = (
  value: number,
  unit: "hours" | "days" | "weeks",
  t: (key: string) => string
): string => {
  switch (unit) {
    case "hours":
      return `${value} ${t("time.hoursAgo")}`;
    case "days":
      return `${value} ${t("time.daysAgo")}`;
    case "weeks":
      return `${value} ${t("time.weeksAgo")}`;
    default:
      return "";
  }
};
