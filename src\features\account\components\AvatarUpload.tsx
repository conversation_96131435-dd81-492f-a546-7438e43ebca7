import React from "react";
import { CloudArrowUpIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface AvatarUploadProps {
  avatarUrl?: string;
  // fileName?: string;
  // fileSize?: string;
  onUpload?: () => void;
  onClear?: () => void;
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  avatarUrl,
  // fileName,
  // fileSize,
  onUpload,
  onClear,
}) => {
  if (!avatarUrl) {
    return (
      <div className="flex flex-col gap-2 items-start">
        <button
          type="button"
          onClick={onUpload}
          className="w-[120px] h-[120px] bg-gray-100 rounded-lg flex flex-col items-center justify-center gap-2 hover:bg-gray-200 transition-colors border-2 border-dashed border-gray-300 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <CloudArrowUpIcon className="w-8 h-8 text-gray-400" />
          <span className="text-xs text-gray-500 font-medium">Upload</span>
        </button>
        <div className="flex flex-col text-xs w-[120px]">
          <span className="font-bold text-[#181818]">Chưa có ảnh</span>
          <span className="text-[#5c5c5c]">Tải lên ảnh đại diện</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3 items-start">
      <div className="relative">
        <img
          src={avatarUrl}
          alt="avatar"
          className="w-[120px] h-[120px] rounded-lg object-cover bg-gray-100"
        />
        <button
          type="button"
          onClick={onClear}
          className="absolute -right-1 -top-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </div>
      <button
        type="button"
        onClick={onUpload}
        className="w-[120px] px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        Đổi ảnh đại diện
      </button>
    </div>
  );
};
