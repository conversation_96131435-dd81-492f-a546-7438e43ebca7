import React from 'react';

interface BusInfoProps {
  departureTime: string;
  arrivalTime: string;
  duration: string;
  departureLocation: string;
  arrivalLocation: string;
}

const BusInfo: React.FC<BusInfoProps> = ({
  departureTime,
  arrivalTime,
  duration,
  departureLocation,
  arrivalLocation,
}) => {
  return (
    <div className="flex items-center space-x-4">
      {/* Departure */}
      <div className="text-center">
        <div className="text-xl font-bold text-gray-800">{departureTime}</div>
        <div className="text-sm text-gray-600">{departureLocation}</div>
      </div>

      {/* Duration and Arrow */}
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
          <div className="flex-1 h-px bg-gray-300 relative">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
              <span className="text-xs text-gray-500">{duration}</span>
            </div>
          </div>
          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
        </div>
      </div>

      {/* Arrival */}
      <div className="text-center">
        <div className="text-xl font-bold text-gray-800">{arrivalTime}</div>
        <div className="text-sm text-gray-600">{arrivalLocation}</div>
      </div>
    </div>
  );
};

export default BusInfo;