// Authentication related types

export interface SignupRequest {
  phone_number: string;
}

export interface SignupResponse {
  message: string;
}

export interface VerifyOtpRequest {
  phone_number: string;
  otp_code: string;
}

export interface VerifyOtpResponse {
  user: Record<string, unknown>;
  access_token: string;
}

export interface SetPasswordRequest {
  password: string;
  // full_name: string;
  // email: string;
}

export interface SetPasswordResponse {
  message: string;
  user: Record<string, unknown>;
}

export interface SigninRequest {
  phone_number: string;
  password: string;
}

export interface SigninResponse {
  user: Record<string, unknown>;
  access_token: string;
}

export interface ForgotPasswordRequest {
  phone_number: string;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ForgotPasswordVerifyOtpRequest {
  phone_number: string;
  otp_code: string;
}

export interface ForgotPasswordVerifyOtpResponse {
  message: string;
  access_token?: string;
}

export interface ResetPasswordRequest {
  phone_number: string;
  password: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  user: Record<string, unknown>;
  access_token: string;
  message: string;
}

export interface UserProfile {
  id: number;
  phone_number: string;
  password: string;
  full_name: string;
  email: string;
  is_phone_verified: boolean;
  otp_code: string;
  otp_expires_at: string;
  is_active: boolean;
  avatar?: {
    id: string;
    originalName: string;
    sizes: {
      thumbnail: string;
      small: string;
      medium: string;
      large: string;
      original: string;
    };
    uploadedAt: string;
  };
  metadata?: {
    preferences?: {
      language?: string;
      theme?: string;
      notifications?: {
        email?: boolean;
        sms?: boolean;
      };
    };
    customFields?: {
      companyName?: string;
      referralCode?: string;
      tags?: string[];
    };
    userSettings?: {
      autoLogin?: boolean;
      defaultPaymentMethod?: string;
      birthday?: string;
      gender?: string;
      address?: string;
      job?: string;
    };
  };
  created_at: string;
  updated_at: string;
}

export interface AuthUser {
  id: number;
  phone_number: string;
  full_name: string;
  email: string;
  status: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Fleet related types
export interface CreateFleetRequest {
  // Add fleet properties based on your requirements
  name: string;
  description?: string;
  // Add other fleet properties as needed
}

export interface Fleet {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  // Add other fleet properties as needed
}

// Error response type
export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}
