import type { Bus } from "@/shared/types/bus";

// Helper function to update bus schedules with translations
export const updateBusSchedulesWithTranslations = (
  buses: Bus[],
  t: (key: string) => string
): Bus[] => {
  return buses.map((bus) => ({
    ...bus,
    duration:
      bus.duration === "Không xác định"
        ? t("bus.durationUnknown")
        : bus.duration,
    amenities: bus.amenities.map((amenity) => {
      if (amenity === "Lịch trình") return t("bus.amenities.schedule");
      if (amenity === "Chính sách") return t("bus.amenities.policy");
      return amenity;
    }),
  }));
};
