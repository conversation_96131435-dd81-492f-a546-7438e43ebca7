import React from "react";
import { But<PERSON> } from "@/shared/components/button";
import { PasswordInput } from "@/shared/components";
import { cn } from "@/lib/utils";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import {
  passwordSetupSchema,
  type PasswordSetupFormValues,
  PasswordValidation,
} from "@/schemas/passwordSchema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface ForgotPasswordResetProps {
  onPasswordReset?: (password: string) => void;
  onBack?: () => void;
  className?: string;
  isLoading?: boolean;
}

export const ForgotPasswordReset: React.FC<ForgotPasswordResetProps> = ({
  onPasswordReset,
  onBack,
  className,
  isLoading = false,
}) => {
  const form = useForm<PasswordSetupFormValues>({
    resolver: zodResolver(passwordSetupSchema),
    mode: "onChange",
  });

  const handleSubmit = (values: PasswordSetupFormValues) => {
    onPasswordReset?.(values.password);
  };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="space-y-12">
        {/* Header */}
        <div className="space-y-4">
          {/* Back Button */}
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="flex items-center gap-4 text-[#5C5C5C] hover:text-[#181818] transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
              <span className="text-[18px] font-normal">
                Quay lại Đăng nhập
              </span>
            </button>
          </div>

          {/* Title and Description */}
          <div className="text-center space-y-4">
            <h2 className="text-[32px] font-extrabold text-[#181818] leading-tight">
              Tạo mật khẩu mới
            </h2>
            <p className="text-[18px] text-[#5C5C5C] leading-relaxed">
              Mật khẩu phải có ít nhất 8 ký tự và tối đa 20 ký tự, bao gồm chữ
              hoa, chữ thường, số và ký tự đặc biệt.
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Password Field */}
          <PasswordInput
            placeholder="Mật khẩu"
            maxLength={PasswordValidation.MAX_LENGTH as number}
            error={form.formState.errors.password?.message}
            {...form.register("password")}
          />

          {/* Confirm Password Field */}
          <PasswordInput
            placeholder="Nhập lại mật khẩu"
            maxLength={PasswordValidation.MAX_LENGTH as number}
            error={form.formState.errors.confirmPassword?.message}
            {...form.register("confirmPassword")}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#2D5BFF] hover:bg-[#2D5BFF]/90 text-white py-6 rounded-lg font-extrabold text-[18px] tracking-wide"
          >
            {isLoading ? "Đang xử lý..." : "Hoàn tất"}
          </Button>
        </form>
      </div>
    </div>
  );
};
