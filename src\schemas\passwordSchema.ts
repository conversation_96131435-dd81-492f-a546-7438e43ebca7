import * as z from "zod";

/**
 * Password validation constants
 */
export enum PasswordValidation {
  MIN_LENGTH = 8,
  MAX_LENGTH = 20,
  SPECIAL_CHARS = "@$!%*?&#",
}

/**
 * Password validation schema with common rules
 * - Minimum 8 characters
 * - Maximum 20 characters
 * - Must contain at least 1 uppercase, 1 lowercase, 1 number, and 1 special character
 */
export const passwordSchema = z
  .string()
  .min(PasswordValidation.MIN_LENGTH, "Mật khẩu phải có ít nhất 8 ký tự")
  .max(PasswordValidation.MAX_LENGTH, "Mật khẩu không được vượt quá 20 ký tự")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/,
    "M<PERSON>t khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt (@$!%*?&#)"
  );

/**
 * Password change form schema
 */
export const passwordChangeSchema = z
  .object({
    oldPassword: z.string().min(1, "Vui lòng nhập mật khẩu cũ"),
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, "Vui lòng xác nhận mật khẩu mới"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Mật khẩu mới và xác nhận không khớp",
    path: ["confirmPassword"],
  });

/**
 * Password setup form schema (for new password creation)
 */
export const passwordSetupSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, "Vui lòng xác nhận mật khẩu"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Mật khẩu và xác nhận không khớp",
    path: ["confirmPassword"],
  });

export type PasswordChangeFormValues = z.infer<typeof passwordChangeSchema>;
export type PasswordSetupFormValues = z.infer<typeof passwordSetupSchema>;
