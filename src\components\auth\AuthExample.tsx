import React, { useState } from "react";
import { Link } from "@tanstack/react-router";
import {
  useSignup,
  useVerifyOtp,
  useSetPassword,
  useSignin,
  useLogout,
  useAuthState,
} from "@/shared/hooks/useAuth";
import { useCreateFleet, useFleets } from "@/features/booking/hooks/useFleets";

export const AuthExample: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otpCode, setOtpCode] = useState("");
  const [password, setPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");

  // Use auth state hook
  const { isAuthenticated, isLoading, profile } = useAuthState();

  // Auth hooks
  const signupMutation = useSignup();
  const verifyOtpMutation = useVerifyOtp();
  const setPasswordMutation = useSetPassword();
  const signinMutation = useSignin();
  const logoutMutation = useLogout();

  // Fleet hooks
  const createFleetMutation = useCreateFleet();
  const { data: fleets, isLoading: fleetsLoading } = useFleets();

  const handleSignup = () => {
    signupMutation.mutate({ phone_number: phoneNumber });
  };

  const handleVerifyOtp = () => {
    verifyOtpMutation.mutate({
      phone_number: phoneNumber,
      otp_code: otpCode,
    });
  };

  const handleSetPassword = () => {
    setPasswordMutation.mutate({
      password,
    });
  };

  const handleSignin = () => {
    signinMutation.mutate({
      phone_number: phoneNumber,
      password,
    });
  };

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleCreateFleet = () => {
    createFleetMutation.mutate({
      name: "Test Fleet",
      description: "A test fleet",
    });
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Auth & Fleet Example</h2>

      {/* Navigation to New Auth Pages */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-bold mb-3">Màn hình Auth mới:</h3>
        <div className="space-y-2">
          <Link
            to="/auth"
            search={{ tab: "signup" }}
            className="block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-center"
          >
            Đăng ký (UI mới)
          </Link>
          <Link
            to="/auth"
            search={{ tab: "signin" }}
            className="block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-center"
          >
            Đăng nhập (UI mới)
          </Link>
        </div>
      </div>

      {/* Profile Display */}
      {isAuthenticated && profile && (
        <div className="mb-6 p-4 bg-green-100 rounded">
          <h3 className="font-bold">Logged in as:</h3>
          <p>Name: {profile.full_name}</p>
          <p>Phone: {profile.phone_number}</p>
          <p>Email: {profile.email}</p>
          <button
            onClick={handleLogout}
            className="mt-2 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Logout
          </button>
        </div>
      )}

      {/* Loading state */}
      {isLoading && (
        <div className="mb-6 p-4 bg-gray-100 rounded">
          <p>Loading profile...</p>
        </div>
      )}

      {/* Auth Forms */}
      {!isAuthenticated && !isLoading && (
        <div className="space-y-4">
          <h3 className="font-medium text-gray-600">Hoặc test trực tiếp:</h3>

          {/* Phone Number Input */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+84901234567"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          {/* Signup Button */}
          <button
            onClick={handleSignup}
            disabled={signupMutation.isPending || !phoneNumber}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
          >
            {signupMutation.isPending ? "Sending OTP..." : "Sign Up"}
          </button>

          {/* OTP Input (show after signup) */}
          {signupMutation.isSuccess && (
            <div>
              <label className="block text-sm font-medium mb-1">OTP Code</label>
              <input
                type="text"
                value={otpCode}
                onChange={(e) => setOtpCode(e.target.value)}
                placeholder="123456"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              <button
                onClick={handleVerifyOtp}
                disabled={verifyOtpMutation.isPending || !otpCode}
                className="w-full mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300"
              >
                {verifyOtpMutation.isPending ? "Verifying..." : "Verify OTP"}
              </button>
            </div>
          )}

          {/* Password Setup (show after OTP verification) */}
          {verifyOtpMutation.isSuccess && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Password
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <button
                onClick={handleSetPassword}
                disabled={setPasswordMutation.isPending || !password}
                className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-300"
              >
                {setPasswordMutation.isPending ? "Setting..." : "Set Password"}
              </button>
            </div>
          )}

          {/* Sign In Form */}
          <div className="pt-4 border-t">
            <h3 className="font-medium mb-3">Or Sign In</h3>
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <button
              onClick={handleSignin}
              disabled={signinMutation.isPending || !phoneNumber || !password}
              className="w-full mt-2 px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:bg-gray-300"
            >
              {signinMutation.isPending ? "Signing In..." : "Sign In"}
            </button>
          </div>
        </div>
      )}

      {/* Fleet Management (show when logged in) */}
      {isAuthenticated && profile && (
        <div className="mt-6 pt-4 border-t">
          <h3 className="font-bold mb-3">Fleet Management</h3>

          <button
            onClick={handleCreateFleet}
            disabled={createFleetMutation.isPending}
            className="w-full mb-3 px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-300"
          >
            {createFleetMutation.isPending
              ? "Creating..."
              : "Create Test Fleet"}
          </button>

          {fleetsLoading && <p>Loading fleets...</p>}

          {fleets && fleets.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Fleets:</h4>
              <ul className="space-y-1">
                {fleets.map((fleet) => (
                  <li
                    key={fleet.id}
                    className="text-sm bg-gray-100 p-2 rounded"
                  >
                    {fleet.name} - {fleet.description}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {(signupMutation.error ||
        verifyOtpMutation.error ||
        setPasswordMutation.error ||
        signinMutation.error) && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          Error occurred. Check console for details.
        </div>
      )}
    </div>
  );
};
