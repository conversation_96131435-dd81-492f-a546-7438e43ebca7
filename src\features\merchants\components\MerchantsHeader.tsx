import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import logo from "@/logo.png";

export function MerchantsHeader({ onSearch }: { onSearch: () => void }) {
  return (
    <div className="flex justify-end">
      <div className="flex items-center justify-between w-2/3 h-16 px-4 bg-transparent">
        <img src={logo} alt="GTECH" className="h-7" style={{ minWidth: 90 }} />
        <button
          className="p-0 flex items-center justify-center"
          onClick={onSearch}
        >
          <MagnifyingGlassIcon className="w-6 h-6 text-[#7C7B7B]" />
        </button>
      </div>
    </div>
  );
}

MerchantsHeader.displayName = "MerchantsHeader";
