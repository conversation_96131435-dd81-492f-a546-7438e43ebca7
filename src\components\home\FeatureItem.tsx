import React, { memo } from "react";

interface FeatureItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  bgColor: string;
}

export const FeatureItem: React.FC<FeatureItemProps> = memo(
  ({ icon, title, description, bgColor }) => {
    return (
      <div className="flex items-start sm:items-center gap-4 lg:gap-5">
        {/* Icon */}
        <div
          className={`w-[50px] h-[50px] lg:w-[60px] lg:h-[60px] mt-3 sm:mt-0 rounded-full flex items-center justify-center flex-shrink-0`}
          style={{ backgroundColor: bgColor }}
        >
          <div className="w-6 h-6 lg:w-8 lg:h-8">{icon}</div>
        </div>

        {/* Content */}
        <div className="flex flex-col gap-2 lg:gap-3 max-w-[353px]">
          <h3 className="text-[#181818] font-mulish font-extrabold text-lg lg:text-xl leading-[1.4]">
            {title}
          </h3>
          <p className="text-[#5C5C5C] font-mulish font-normal text-sm lg:text-base leading-[1.4] lg:leading-5">
            {description}
          </p>
        </div>
      </div>
    );
  }
);

FeatureItem.displayName = "FeatureItem";
