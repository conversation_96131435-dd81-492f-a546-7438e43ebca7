import axiosInstance from "@/shared/api/axiosInstance";
import {
  GET_ALL_PLACES_API_ENDPOINT,
  SEARCH_PLACES_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";

const getAllPlaces = async () => {
  const response = await axiosInstance.get(GET_ALL_PLACES_API_ENDPOINT);
  return response.data;
};

const searchPlaces = async (query: string) => {
  const response = await axiosInstance.get(SEARCH_PLACES_API_ENDPOINT, {
    params: { term: query || "" },
  });
  return response.data;
};

export { getAllPlaces, searchPlaces };
