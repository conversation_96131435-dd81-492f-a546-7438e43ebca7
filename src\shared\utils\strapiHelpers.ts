import type { StrapiImage } from "@/shared/types/strapi";

// Strapi server base URL
const STRAPI_BASE_URL = "https://strapi.gtech-ecom.gone.vn";

/**
 * Convert a Strapi image object to a full URL
 * @param image - StrapiImage object or string URL
 * @param fallbackUrl - Fallback URL if image is not available
 * @returns Full image URL
 */
export function getStrapiImageUrl(
  image: StrapiImage | string | null | undefined,
  fallbackUrl: string = "https://via.placeholder.com/400x300/E5E7EB/9CA3AF?text=No+Image"
): string {
  // If image is null or undefined, return fallback
  if (!image) {
    return fallbackUrl;
  }

  // If image is already a string (full URL), return as is
  if (typeof image === "string") {
    // If it's a relative path, make it absolute
    if (image.startsWith("/uploads/")) {
      return `${STRAPI_BASE_URL}${image}`;
    }
    // If it's already a full URL, return as is
    return image;
  }

  // If image is a StrapiImage object, extract the URL
  if (image.url) {
    // If it's a relative path, make it absolute
    if (image.url.startsWith("/uploads/")) {
      return `${STRAPI_BASE_URL}${image.url}`;
    }
    // If it's already a full URL, return as is
    return image.url;
  }

  // If we can't extract a URL, return fallback
  return fallbackUrl;
}

/**
 * Convert the first image from a StrapiImage array to a full URL
 * @param images - Array of StrapiImage objects
 * @param fallbackUrl - Fallback URL if no images are available
 * @returns Full image URL
 */
export function getFirstStrapiImageUrl(
  images: StrapiImage[] | null | undefined,
  fallbackUrl: string = "https://via.placeholder.com/400x300/E5E7EB/9CA3AF?text=No+Image"
): string {
  if (!images || images.length === 0) {
    return fallbackUrl;
  }

  return getStrapiImageUrl(images[0], fallbackUrl);
}

/**
 * Get a specific format of Strapi image (thumbnail, small, medium, large)
 * @param image - StrapiImage object
 * @param format - Image format to get
 * @param fallbackUrl - Fallback URL if format is not available
 * @returns Full image URL
 */
export function getStrapiImageFormat(
  image: StrapiImage | null | undefined,
  format: "thumbnail" | "small" | "medium" | "large",
  fallbackUrl?: string
): string {
  if (!image) {
    return getStrapiImageUrl(null, fallbackUrl);
  }

  // Try to get the specific format
  const formatData = image.formats?.[format];
  if (formatData?.url) {
    return getStrapiImageUrl(formatData.url, fallbackUrl);
  }

  // Fallback to original image URL
  return getStrapiImageUrl(image, fallbackUrl);
}

/**
 * Convert relative URL from markdown content to absolute Strapi URL
 * @param src - Source URL (could be relative or absolute)
 * @returns Full URL
 */
export function convertMarkdownImageUrl(
  src: string | null | undefined
): string {
  if (!src) {
    return "";
  }

  // If it's a relative Strapi path, make it absolute
  if (src.startsWith("/uploads/")) {
    return `${STRAPI_BASE_URL}${src}`;
  }

  // If it's already absolute, return as is
  return src;
}
