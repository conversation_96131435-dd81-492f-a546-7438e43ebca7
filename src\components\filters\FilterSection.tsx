import React from "react";
import { clsx } from "clsx";

interface FilterSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  children,
  className,
}) => {
  return (
    <div className={clsx("space-y-2", className)}>
      <h3 className="text-base font-mulish font-bold text-[#5C5C5C]">
        {title}
      </h3>
      <div className="space-y-0">{children}</div>
    </div>
  );
};

export default FilterSection;
