export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  })
    .format(price)
    .replace("₫", "đ");
};

export const getOperatingStatus = (
  startTime: string,
  endTime: string
): { status: string; color: string } => {
  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();

  const [startHour, startMin] = startTime.split(":").map(Number);
  const [endHour, endMin] = endTime.split(":").map(Number);

  const startTimeMinutes = startHour * 60 + startMin;
  const endTimeMinutes = endHour * 60 + endMin;

  const isOpen =
    currentTime >= startTimeMinutes && currentTime <= endTimeMinutes;

  return {
    status: isOpen ? "Đang mở cửa" : "Đóng cửa",
    color: isOpen ? "#369926" : "#FF0000",
  };
};

// Random food image generator
export const getRandomFoodImage = (index: string): string => {
  // Convert string to number using character codes
  const seed = index
    .split("")
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const imageIndex = (seed % 7) + 7; // Random between 7-13
  return `/assets/merchants/image-${imageIndex}.png`;
};
