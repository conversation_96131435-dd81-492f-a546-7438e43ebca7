import { createFileRoute } from "@tanstack/react-router";
import { AuthLayout } from "@/components/auth/AuthLayout";
import { AuthForm } from "@/components/auth/AuthForm";
import { useNavigate } from "@tanstack/react-router";

export const Route = createFileRoute("/auth")({
  component: Auth,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      tab: (search.tab as "signin" | "signup") || "signin",
    };
  },
});

function Auth() {
  const navigate = useNavigate();
  const { tab } = Route.useSearch();

  const handleSigninSuccess = () => {
    navigate({ to: "/" });
  };

  const handleSignupSuccess = () => {
    navigate({ to: "/" });
  };

  return (
    <AuthLayout>
      <AuthForm
        onSigninSuccess={handleSigninSuccess}
        onSignupSuccess={handleSignupSuccess}
        defaultTab={tab}
      />
    </AuthLayout>
  );
}
