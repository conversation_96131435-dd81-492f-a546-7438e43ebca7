import { useState, useEffect, useCallback } from "react";
import {
  getRecentSearches,
  saveRecentSearch,
  removeRecentSearch,
  clearRecentSearches,
  type RecentSearch,
} from "@/lib/recentSearches";

export const useRecentSearches = () => {
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);

  // Load recent searches on mount
  useEffect(() => {
    const searches = getRecentSearches();
    setRecentSearches(searches);
  }, []);

  // Save a new search
  const addRecentSearch = useCallback(
    (search: Omit<RecentSearch, "id" | "timestamp">) => {
      saveRecentSearch(search);
      const updatedSearches = getRecentSearches();
      setRecentSearches(updatedSearches);
    },
    []
  );

  // Remove a specific search
  const removeSearch = useCallback((id: string) => {
    removeRecentSearch(id);
    setRecentSearches((prev) => prev.filter((search) => search.id !== id));
  }, []);

  // Clear all recent searches
  const clearAllSearches = useCallback(() => {
    clearRecentSearches();
    setRecentSearches([]);
  }, []);

  return {
    recentSearches,
    addRecentSearch,
    removeSearch,
    clearAllSearches,
  };
};
