import React, { useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { convertMarkdownImageUrl } from "@/shared/utils/strapiHelpers";

interface MarkdownContentProps {
  content: string;
  className?: string;
}

// Component ImageWithSkeleton để handle loading state
const ImageWithSkeleton = ({ src, alt, ...props }: any) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Use the helper function to convert image URL
  const imageUrl = convertMarkdownImageUrl(src);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return null; // Hide failed images
  }

  return (
    <div className="relative w-full">
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded aspect-video" />
      )}
      {!hasError && (
        <img
          src={imageUrl}
          alt={alt}
          {...props}
          loading="lazy"
          className={`w-full h-auto transition-opacity duration-300 ${
            isLoading ? "opacity-0" : "opacity-100"
          }`}
          onLoad={handleLoad}
          onError={handleError}
        />
      )}
    </div>
  );
};

export const MarkdownContent: React.FC<MarkdownContentProps> = ({
  content,
  className = "",
}) => {
  return (
    <div className={`prose prose-gray max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          img: ImageWithSkeleton,
          p: ({ children, ...props }) => (
            <p {...props} className="text-gray-700 leading-relaxed mb-4">
              {children}
            </p>
          ),
          h1: ({ children, ...props }) => (
            <h1
              {...props}
              className="text-2xl font-bold text-[#181818] mb-4 mt-6"
            >
              {children}
            </h1>
          ),
          h2: ({ children, ...props }) => (
            <h2
              {...props}
              className="text-xl font-bold text-[#181818] mb-3 mt-5"
            >
              {children}
            </h2>
          ),
          h3: ({ children, ...props }) => (
            <h3
              {...props}
              className="text-lg font-semibold text-[#181818] mb-2 mt-4"
            >
              {children}
            </h3>
          ),
          ul: ({ children, ...props }) => (
            <ul {...props} className="list-disc list-inside mb-4 text-gray-700">
              {children}
            </ul>
          ),
          ol: ({ children, ...props }) => (
            <ol
              {...props}
              className="list-decimal list-inside mb-4 text-gray-700"
            >
              {children}
            </ol>
          ),
          li: ({ children, ...props }) => (
            <li {...props} className="mb-1">
              {children}
            </li>
          ),
          a: ({ href, children, ...props }) => (
            <a
              {...props}
              href={href}
              className="text-[#2D5BFF] hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          blockquote: ({ children, ...props }) => (
            <blockquote
              {...props}
              className="border-l-4 border-[#2D5BFF] pl-4 italic text-gray-600 my-4"
            >
              {children}
            </blockquote>
          ),
          code: ({ children, ...props }) => (
            <code
              {...props}
              className="bg-gray-100 px-2 py-1 rounded text-sm font-mono"
            >
              {children}
            </code>
          ),
          pre: ({ children, ...props }) => (
            <pre
              {...props}
              className="bg-gray-100 p-4 rounded-lg overflow-x-auto my-4"
            >
              {children}
            </pre>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
