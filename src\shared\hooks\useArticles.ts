import { useQuery } from "@tanstack/react-query";
import { getArticles, getArticleById } from "@/api/strapiApi";
import type {
  StrapiArticlesResponse,
  StrapiApiError,
  StrapiArticle,
} from "@/shared/types/strapi";

// Query keys for articles
export const articlesKeys = {
  all: ["articles"] as const,
  list: () => [...articlesKeys.all, "list"] as const,
  detail: (id: string) => [...articlesKeys.all, "detail", id] as const,
};

/**
 * Hook to fetch articles from Strapi
 */
export const useArticles = () => {
  return useQuery<StrapiArticlesResponse, StrapiApiError>({
    queryKey: articlesKeys.list(),
    queryFn: () => getArticles(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook to fetch a single article by ID from Strapi
 * @param id - The article ID to fetch
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useArticleById = (id: string, enabled: boolean = true) => {
  return useQuery<StrapiArticle, StrapiApiError>({
    queryKey: articlesKeys.detail(id),
    queryFn: () => getArticleById(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!id,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error?.error?.status >= 400 && error?.error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
