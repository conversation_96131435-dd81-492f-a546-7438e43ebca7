import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import {
  getAllBusSchedules,
  searchBusSchedules,
  getBusScheduleById,
  createBusSchedule,
  updateBusSchedule,
} from "@/features/booking/api/bus-schedules";
import type {
  BusSchedule,
  CreateBusScheduleRequest,
  UpdateBusScheduleRequest,
  SearchBusSchedulesParams,
} from "@/features/booking/api/bus-schedules";

// Hook to get all bus schedules
export const useBusSchedules = (busId?: string) => {
  return useQuery({
    queryKey: queryKeys.busSchedules.lists(busId),
    queryFn: () => getAllBusSchedules(busId),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to search bus schedules
export const useSearchBusSchedules = (
  params: SearchBusSchedulesParams | null,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: params
      ? queryKeys.busSchedules.search(params)
      : ["bus-schedules", "search", "disabled"],
    queryFn: () => {
      if (!params) throw new Error("Search parameters are required");
      return searchBusSchedules(params);
    },
    enabled: enabled && !!params && !!params.fromPlaceId && !!params.toPlaceId,
    staleTime: 1000 * 60 * 2, // 2 minutes for search results
  });
};

// Hook to get a specific bus schedule by ID
export const useBusScheduleById = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.busSchedules.detail(id),
    queryFn: () => getBusScheduleById(id),
    enabled: enabled && !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to create a new bus schedule
export const useCreateBusSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateBusScheduleRequest) => createBusSchedule(data),
    onSuccess: () => {
      // Invalidate and refetch bus schedules
      queryClient.invalidateQueries({ queryKey: queryKeys.busSchedules.all });
    },
  });
};

// Hook to update a bus schedule
export const useUpdateBusSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateBusScheduleRequest;
    }) => updateBusSchedule(id, data),
    onSuccess: (updatedSchedule: BusSchedule) => {
      // Update the specific schedule in cache
      queryClient.setQueryData(
        queryKeys.busSchedules.detail(updatedSchedule.id),
        updatedSchedule
      );
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({
        queryKey: queryKeys.busSchedules.lists(),
      });
    },
  });
};

// Hook for prefetching bus schedules (useful for preloading data)
export const usePrefetchBusSchedules = () => {
  const queryClient = useQueryClient();

  return {
    prefetchAllBusSchedules: (busId?: string) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.busSchedules.lists(busId),
        queryFn: () => getAllBusSchedules(busId),
        staleTime: 1000 * 60 * 5,
      });
    },

    prefetchSearchBusSchedules: (params: SearchBusSchedulesParams) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.busSchedules.search(params),
        queryFn: () => searchBusSchedules(params),
        staleTime: 1000 * 60 * 2,
      });
    },

    prefetchBusScheduleById: (id: string) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.busSchedules.detail(id),
        queryFn: () => getBusScheduleById(id),
        staleTime: 1000 * 60 * 5,
      });
    },
  };
};

// Hook for invalidating bus schedules cache
export const useInvalidateBusSchedules = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAllBusSchedules: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.busSchedules.all });
    },

    invalidateSearchBusSchedules: () => {
      queryClient.invalidateQueries({
        queryKey: [...queryKeys.busSchedules.all, "search"],
      });
    },

    invalidateBusScheduleById: (id: string) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.busSchedules.detail(id),
      });
    },
  };
};
