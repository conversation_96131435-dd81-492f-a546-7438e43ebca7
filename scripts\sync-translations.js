#!/usr/bin/env node

/**
 * Sync translation files from src/locales to public/locales
 * This ensures the HTTP backend can load the translation files
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, "../src/locales");
const publicDir = path.join(__dirname, "../public/locales");

function copyTranslations() {
  try {
    // Ensure public/locales directory exists
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // Read all language directories
    const languages = fs
      .readdirSync(srcDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    console.log(`Found languages: ${languages.join(", ")}`);

    languages.forEach((lang) => {
      const srcLangDir = path.join(srcDir, lang);
      const publicLangDir = path.join(publicDir, lang);

      // Ensure public language directory exists
      if (!fs.existsSync(publicLangDir)) {
        fs.mkdirSync(publicLangDir, { recursive: true });
      }

      // Copy all JSON files
      const files = fs
        .readdirSync(srcLangDir)
        .filter((file) => file.endsWith(".json"));

      files.forEach((file) => {
        const srcFile = path.join(srcLangDir, file);
        const publicFile = path.join(publicLangDir, file);

        fs.copyFileSync(srcFile, publicFile);
        console.log(`✅ Copied ${lang}/${file}`);
      });
    });

    console.log("🎉 Translation sync completed!");
  } catch (error) {
    console.error("❌ Error syncing translations:", error.message);
    process.exit(1);
  }
}

copyTranslations();
