/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          "Mulish",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Oxygen",
          "Ubuntu",
          "Cantarell",
          "Fira Sans",
          "Droid Sans",
          "Helvetica Neue",
          "sans-serif",
        ],
        mulish: ["Mulish", "sans-serif"],
      },
      colors: {
        primary: {
          DEFAULT: "#0288D1",
          600: "#0288D1",
        },
        gray: {
          350: "#D7D7D7",
          400: "#5C5C5C",
          800: "#181818",
          border: "#EDEDED",
        },
        booking: {
          blue: "#2D5BFF",
          orange: "#FF7F37",
          success: "#3AC922",
          background: "#F8F8F8",
          text: "#181818",
          muted: "#5C5C5C",
          border: "#EDEDED",
        },
        restaurant: {
          blue: "#2D5BFF",
          orange: "#FF7F37",
          success: "#3AC922",
          green: "#369926",
          danger: "#FF4444",
          background: {
            light: "#F4F5F7",
            gray: "#F8F8F8",
            disabled: "#EFEFEF",
          },
          text: {
            primary: "#181818",
            secondary: "#5C5C5C",
            tertiary: "#747474",
            disabled: "#7C7B7B",
          },
          border: {
            light: "#EDEDED",
            radio: "#D7D7D7",
          },
        },
      },
    },
  },
  plugins: [],
};
