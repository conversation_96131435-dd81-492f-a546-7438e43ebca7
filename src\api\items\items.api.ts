import axiosInstance from "@/shared/api/axiosInstance";
import {
  GET_ITEM_BY_ID_API_ENDPOINT,
  GET_ALL_ITEM_API_ENDPOINT,
} from "@/shared/api/apiEndpoint";
import type { GetItemDetailResponse, ItemsResponse } from "./items.types";

// GET /api/v1/orders/:id - Get order by ID
const getItemById = async (itemId: string): Promise<GetItemDetailResponse> => {
  const response = await axiosInstance.get(
    `${GET_ITEM_BY_ID_API_ENDPOINT}/${itemId}`
  );
  return response.data;
};

// GET /api/v1/orders - Get all orders with optional pagination
const getAllItems = async (params?: {
  page?: number;
  limit?: number;
}): Promise<ItemsResponse> => {
  const response = await axiosInstance.get(GET_ALL_ITEM_API_ENDPOINT, {
    params,
  });
  return response.data;
};

export { getItemById, getAllItems };
