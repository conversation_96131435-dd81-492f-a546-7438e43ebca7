import { useQuery } from "@tanstack/react-query";
import { getQRInfo } from "@/api/merchants/qr.api";
import type { QRApiResponse } from "@/api/merchants/qr.types";

interface UseQRInfoProps {
  qrCode?: string;
  enabled?: boolean;
}

export const useQRInfo = ({ qrCode, enabled = true }: UseQRInfoProps) => {
  return useQuery<QRApiResponse, Error>({
    queryKey: ["qr-info", qrCode],
    queryFn: () => {
      if (!qrCode) {
        throw new Error("QR code is required");
      }
      return getQRInfo(qrCode);
    },
    enabled: enabled && !!qrCode,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
  });
};
