import React from "react";
import { ChevronLeftIcon } from "@heroicons/react/16/solid";

interface OrderHeaderProps {
  restaurantName: string;
  onBackToMenu: () => void;
  disabled?: boolean;
}

export const OrderHeader: React.FC<OrderHeaderProps> = ({
  restaurantName,
  onBackToMenu,
  disabled = false,
}) => {
  return (
    <div className="bg-white px-4 py-3">
      <div className="flex items-center justify-between">
        <button
          onClick={onBackToMenu}
          className="w-10 h-10 rounded-full flex items-center justify-center"
          disabled={disabled}
        >
          <ChevronLeftIcon className="w-6 h-6 text-[#7C7B7B]" />
        </button>

        <div className="flex flex-col items-center">
          <h1 className="text-sm font-bold text-[#181818]">
            Xác nhận đặt hàng
          </h1>
          <p className="text-xs text-[#7C7B7B]">{restaurantName}</p>
        </div>

        <div className="w-10 h-10" />
      </div>
    </div>
  );
};
