import React, { use<PERSON>em<PERSON>, useState, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/components/table";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import type { Order, OrderTicket } from "@/api/orders";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { formatPrice } from "@/shared/utils/formatPrice";
import {
  // MagnifyingGlassIcon,
  ChevronDownIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { Button } from "@/shared/components/button";
import { DesktopTicketCard, MobileTicketCard } from "@/routes/payment-success";
import { MobileTicketCard as CustomMobileTicketCard } from "./MobileTicketCard";
import { DatePicker } from "@/shared/components/date-picker/DatePicker";
import { CodeIcon } from "@/shared";

interface TicketsTableProps {
  orders: Order[];
  isLoading: boolean;
  showPagination?: boolean;
}

interface TicketsListModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tickets: OrderTicket[];
  orderData: Order;
  onDownloadTicket: (ticket: OrderTicket, isReturnTicket: boolean) => void;
}

const statusOptions = [
  { value: "", label: "Tất cả trạng thái" },
  { value: "pending", label: "Chờ xác nhận" },
  { value: "confirmed", label: "Đã xác nhận" },
  { value: "cancelled", label: "Đã hủy" },
  { value: "completed", label: "Hoàn thành" },
  { value: "processing", label: "Đang xử lý" },
  { value: "failed", label: "Thất bại" },
  { value: "paid", label: "Đã thanh toán" },
];

const TicketsListModalCustom: React.FC<TicketsListModalProps> = ({
  open,
  onOpenChange,
  tickets,
  orderData,
  onDownloadTicket,
}) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fade-in"
        onClick={() => onOpenChange(false)}
      />
      {/* Modal content */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-screen-2xl w-[90vw] p-8 flex flex-col gap-6 max-h-[90vh] overflow-y-auto animate-fade-in">
        {/* Close button */}
        <button
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 focus:outline-none"
          onClick={() => onOpenChange(false)}
        >
          <XMarkIcon className="w-6 h-6 text-gray-500" />
        </button>
        <h2 className="text-2xl font-bold mb-2">Danh sách vé</h2>
        <div className="flex flex-col gap-4 w-full">
          {tickets.map((ticket) => (
            <React.Fragment key={ticket.id}>
              <div className="hidden md:block">
                <DesktopTicketCard
                  ticket={ticket}
                  orderData={orderData}
                  isReturn={false}
                  onDownloadTicket={onDownloadTicket}
                />
              </div>
              <div className="block md:hidden">
                <MobileTicketCard
                  ticket={ticket}
                  orderData={orderData}
                  isReturn={false}
                  onDownloadTicket={onDownloadTicket}
                />
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export const TicketsTable = ({
  orders,
  isLoading,
  showPagination = true,
}: TicketsTableProps) => {
  // State tạm cho filter input
  const [searchInput, setSearchInput] = useState("");
  const [statusInput, setStatusInput] = useState("");
  const [dateInput, setDateInput] = useState<Date | undefined>(undefined);

  // State thực tế cho filter (chỉ cập nhật khi bấm Tìm)
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [page, setPage] = useState(1);
  const itemsPerPage = 10;

  const [modalOpen, setModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Filter orders
  const filtered = useMemo(() => {
    return orders.filter((order) => {
      const ticket = order.tickets[0];
      const matchSearch =
        search === "" ||
        order.id.toLowerCase().includes(search.toLowerCase()) ||
        ticket?.ticket_number?.toLowerCase().includes(search.toLowerCase());

      // So sánh status (có thể là enum hoặc string, nên chuyển về cùng lowercase)
      const matchStatus =
        status === "" ||
        String(order.status).toLowerCase() === status.toLowerCase();

      // Filter by departure date
      let matchDate = true;
      if (selectedDate && ticket?.departure_date) {
        const ticketDate = new Date(ticket.departure_date);
        const filterDate = new Date(selectedDate);

        // Reset time to start of day for accurate comparison
        ticketDate.setHours(0, 0, 0, 0);
        filterDate.setHours(0, 0, 0, 0);

        matchDate = ticketDate.getTime() === filterDate.getTime();
      }

      return matchSearch && matchStatus && matchDate;
    });
  }, [orders, search, status, selectedDate]);

  // Pagination
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  const paged = filtered.slice((page - 1) * itemsPerPage, page * itemsPerPage);

  // Reset page khi filter thực tế thay đổi
  React.useEffect(() => {
    setPage(1);
  }, [search, status, selectedDate]);

  // Dummy download handler, replace with actual logic if needed
  const handleDownloadTicket = useCallback(() => {
    // Implement download logic or pass from parent
  }, []);

  // Handle clear date filter
  const handleClearDate = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    setDateInput(undefined);
    setSelectedDate(undefined);
  }, []);

  return (
    <div>
      {/* Filter UI */}
      <div className="bg-white rounded-xl p-2 sm:p-4 md:p-6 mb-4 md:mb-6">
        <div className="grid grid-cols-1 md:grid-cols-11 gap-4 items-end">
          {/* Search */}
          <div className="md:col-span-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mã vé
            </label>
            <div className="flex h-11 w-full overflow-hidden rounded-lg border border-gray-200 bg-white transition-all focus-within:bg-gray-50 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50 focus-within:border-blue-300">
              <div className="flex w-full">
                <div className="bg-[#EDEDED] p-[17px]">
                  <CodeIcon />
                </div>
                <input
                  type="text"
                  placeholder="Nhập mã vé..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="flex-1 px-3 text-sm bg-[#F8F8F8] text-gray-700 placeholder-gray-400 focus:outline-none focus:placeholder-gray-500 transition-all focus:bg-gray-50"
                />
              </div>
            </div>
          </div>

          {/* Date */}
          <div className="md:col-span-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ngày khởi hành
            </label>
            <div className="relative">
              <DatePicker
                value={dateInput}
                onChange={setDateInput}
                placeholder="Chọn ngày..."
                containerClassName="h-[44px]"
                classNameIcon="p-[14px]"
              />
              {dateInput && (
                <button
                  type="button"
                  onClick={handleClearDate}
                  className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-200 transition-colors"
                  title="Xóa ngày"
                >
                  <XMarkIcon className="w-4 h-4 text-gray-500" />
                </button>
              )}
            </div>
          </div>

          {/* Status */}
          <div className="md:col-span-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trạng thái
            </label>
            <div className="relative flex h-11 w-full overflow-hidden rounded-lg border border-gray-200 bg-[#F8F8F8] transition-all focus-within:bg-gray-50 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-50 focus-within:border-blue-300">
              <select
                className="flex-1 appearance-none px-3 text-sm text-gray-700 placeholder-gray-400 focus:outline-none bg-transparent cursor-pointer"
                value={statusInput}
                onChange={(e) => setStatusInput(e.target.value)}
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <ChevronDownIcon className="w-5 h-5 absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Search Button */}
          <div className="md:col-span-2 flex items-end">
            <Button
              className="w-full bg-[#2D5BFF] h-11 text-white font-bold px-4 py-2 rounded-lg hover:bg-[#2046c7] transition"
              onClick={() => {
                setSearch(searchInput);
                setStatus(statusInput);
                setSelectedDate(dateInput);
              }}
            >
              Tìm
            </Button>
          </div>
        </div>
      </div>
      {/* Table for desktop, Cards for mobile */}
      <div className="hidden md:block overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="border-b border-gray-200">
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[120px] whitespace-nowrap">
                Mã vé
              </TableHead>
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[180px]">
                Tuyến đường
              </TableHead>
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[150px] whitespace-nowrap">
                Ngày khởi hành
              </TableHead>
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[80px] whitespace-nowrap">
                Số vé
              </TableHead>
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[120px] whitespace-nowrap">
                Tổng tiền
              </TableHead>
              <TableHead className="py-4 px-6 text-ld font-semibold text-gray-600 min-w-[120px] whitespace-nowrap">
                Trạng thái
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : paged.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center py-8 text-gray-500"
                >
                  Không có dữ liệu
                </TableCell>
              </TableRow>
            ) : (
              paged.map((order) => {
                const ticket = order.tickets[0];
                return (
                  <TableRow
                    key={order.id}
                    className="hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-0"
                  >
                    <TableCell className="py-4 px-6 min-w-[120px] whitespace-nowrap">
                      <button
                        className="font-bold text-[#2D5BFF] hover:underline focus:outline-none"
                        onClick={() => {
                          setSelectedOrder(order);
                          setModalOpen(true);
                        }}
                        type="button"
                      >
                        {ticket?.ticket_number || "-"}
                      </button>
                    </TableCell>
                    <TableCell className="py-4 px-6 text-gray-600 min-w-[180px]">
                      {ticket?.busSchedule?.departure_place?.name || "-"} -{" "}
                      {ticket?.busDropoff?.arrival_place?.name || "-"}
                    </TableCell>
                    <TableCell className="py-4 px-6 text-gray-600 min-w-[150px] whitespace-nowrap">
                      {ticket
                        ? (() => {
                            const dateStr = `${ticket.departure_date}T${ticket.departure_time}`;
                            const dateObj = new Date(dateStr);
                            return isNaN(dateObj.getTime())
                              ? "-"
                              : format(dateObj, "dd/MM/yyyy HH:mm", {
                                  locale: vi,
                                });
                          })()
                        : "-"}
                    </TableCell>
                    <TableCell className="py-4 px-6 text-gray-600 min-w-[80px] whitespace-nowrap text-center">
                      {order.tickets.length}
                    </TableCell>
                    <TableCell className="py-4 px-6 font-medium text-gray-900 min-w-[120px] whitespace-nowrap">
                      {formatPrice(Number(order.total_amount))}
                    </TableCell>
                    <TableCell className="py-4 px-6 min-w-[120px] whitespace-nowrap">
                      <OrderStatusBadge status={order.status} />
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
        {/* Pagination for desktop */}
        {showPagination && totalPages > 1 && (
          <div className="flex justify-end gap-2 p-4 border-t border-gray-100">
            <button
              className="px-4 py-2 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:hover:bg-white"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
            >
              Trước
            </button>
            <span className="px-3 py-2 text-sm text-gray-600">
              Trang {page} / {totalPages}
            </span>
            <button
              className="px-4 py-2 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:hover:bg-white"
              disabled={page === totalPages}
              onClick={() => setPage(page + 1)}
            >
              Sau
            </button>
          </div>
        )}
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : paged.length === 0 ? (
          <div className="text-center py-8 text-gray-500">Không có dữ liệu</div>
        ) : (
          <div className="flex flex-col gap-3">
            {paged.map((order) => (
              <CustomMobileTicketCard
                key={order.id}
                order={order}
                onTicketClick={(order) => {
                  setSelectedOrder(order);
                  setModalOpen(true);
                }}
              />
            ))}
          </div>
        )}
        {/* Pagination for mobile */}
        {showPagination && totalPages > 1 && (
          <div className="flex justify-center gap-2 p-4">
            <button
              className="px-4 py-2 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:hover:bg-white"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
            >
              Trước
            </button>
            <span className="px-3 py-2 text-sm text-gray-600">
              Trang {page} / {totalPages}
            </span>
            <button
              className="px-4 py-2 text-sm font-medium text-gray-600 rounded-lg border border-gray-200 hover:bg-gray-50 disabled:opacity-50 disabled:hover:bg-white"
              disabled={page === totalPages}
              onClick={() => setPage(page + 1)}
            >
              Sau
            </button>
          </div>
        )}
      </div>
      {/* Ticket List Modal */}
      {selectedOrder && (
        <TicketsListModalCustom
          open={modalOpen}
          onOpenChange={(open) => {
            setModalOpen(open);
            if (!open) setSelectedOrder(null);
          }}
          tickets={selectedOrder.tickets}
          orderData={selectedOrder}
          onDownloadTicket={handleDownloadTicket}
        />
      )}
    </div>
  );
};
