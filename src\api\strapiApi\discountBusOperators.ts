import { axiosInstanceStrapi } from "@/shared/api/axiosInstance";
import type {
  StrapiDiscountBusOperatorsResponse,
  StrapiDiscountBusOperator,
} from "@/shared/types/strapi";

export interface GetDiscountBusOperatorsParams {
  place_id?: number;
}

/**
 * Fetch discount bus operators from Strapi CMS
 * @param params - Optional parameters including place_id filter
 */
export const getDiscountBusOperators = async (
  params?: GetDiscountBusOperatorsParams
): Promise<StrapiDiscountBusOperatorsResponse> => {
  const queryParams = new URLSearchParams();

  if (params?.place_id) {
    queryParams.append("discount_place.place_id", params.place_id.toString());
  }

  const url = queryParams.toString()
    ? `discount-bus-operators?${queryParams.toString()}`
    : "discount-bus-operators";

  const response = await axiosInstanceStrapi.get(url);
  return response.data;
};

/**
 * Fetch a single discount bus operator by ID from Strapi CMS
 */
export const getDiscountBusOperatorById = async (
  id: string
): Promise<StrapiDiscountBusOperator> => {
  const response = await axiosInstanceStrapi.get(
    `discount-bus-operators/${id}`
  );
  return response.data;
};
